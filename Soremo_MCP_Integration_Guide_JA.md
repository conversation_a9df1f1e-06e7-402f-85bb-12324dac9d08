# SOREMO MCP サーバーの統合と使用ガイド

---

## 目次
1. [MCP統合コード用のGitブランチ](#mcp統合コード用のgitブランチ)
2. [APIキーの作成とコピー](#apiキーの作成とコピー)
3. [MCPクライアントでの設定と使用](#mcpクライアントでの設定と使用)
4. [モジュール/アプリ別のMCPツールコード整理](#モジュールアプリ別のmcpツールコード整理)

---

## 1. MCP統合コード用のGitブランチ
- すべてのMCPサーバー統合コードは以下のブランチに保存されています：

  **feat/mcp-integration**

- チェックアウトまたは開発を行うには、以下のコマンドを実行してください：
  ```bash
  git checkout feat/mcp-integration
  ```

---

## 2. APIキーの作成とコピー

### ステップ1: APIキーの作成
- Soremoシステムにログインします。
- APIキー管理ページに移動します：
  
  **http://localhost:8010/accounts/api-keys/**
  
  (または`localhost:8010`を実際のドメイン/IPとポートに置き換えてください)
- メモ（備考）を入力し、「Submit」をクリックして新しいAPIキーを作成します。
- 作成後、システムは64文字のAPIキーと完全なMCP URL（形式：`http://localhost:8010/mcp/<API_KEY>/sse`）を表示します。

### ステップ2: MCP URLのコピー
- MCP URLの隣にある📋（MCP URLをコピー）ボタンをクリックして、クリップボードに素早くコピーします。
- さらに、一部の画面（例：アカウント情報）では、「🚀 MCP URL作成&コピー」ボタンがあり、ワンクリックで新しいAPIキーを作成し、自動的にMCP URLをコピーできます。

---

## 3. MCPクライアントでの設定と使用

### ステップ1: MCP URLをMCPクライアント設定ファイルに貼り付け
- MCPクライアントの設定ファイル（例：`~/.cursor/mcp.json`）を開きます
- 以下のように内容を追加または編集します：

```json
{
    "mcpServers": {
        "soremo-mcp": {
            "url": "http://localhost:8010/mcp/<API_KEY>/sse"
        }
    }
}
```
- `<API_KEY>`を作成したAPIキーに置き換えてください。

### ステップ2: 保存と使用
- 設定ファイルを保存し、MCPクライアントを再起動して新しい設定を使用します。

---

## 4. モジュール/アプリ別のMCPツールコード整理

### MCPツールを個別のファイルに分離
- より良いコード整理と保守性のため、各モジュール/アプリ用のMCPツールを個別のファイルに分離してください。
- 例えば、`accounts`アプリの場合、ファイルを作成します：`accounts/mcp_tools.py`

### このファイルでツールを定義
```python
# accounts/mcp_tools.py
from django_mcp import mcp_app

@mcp_app.tool()
def get_account_info(user_id: int):
    """
    user_idによるアカウント情報の取得
    """
    # ユーザーデータを取得するロジック
    ...
    return {...}
```

### Django起動時にファイルがインポートされることを確認
- MCPサーバーがツールを認識するために、アプリの`apps.py`でツールファイルをインポートします：

```python
# accounts/apps.py
from django.apps import AppConfig

class AccountsConfig(AppConfig):
    name = 'accounts'

    def ready(self):
        import accounts.mcp_tools  # noqa
```
- アプリが`INSTALLED_APPS`にリストされ、正しい`AppConfig`クラスを使用していることを確認してください。

### サーバーの再起動
- ツールファイルを作成してインポートした後、新しいツールを有効にするためにサーバーを再起動してください。 