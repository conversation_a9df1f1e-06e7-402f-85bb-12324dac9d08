{% load util %}

{% if item|class_name == 'SectionCredit' %}
  <div class="modal-staff-credit-section-container section--container"
      data-section-id="{{ item.pk }}">
    <div class="modal-staff-credit-section-header"
        data-section="{{ item.section_name }}"
        data-section-en="{{ item.section_name_en }}"
        data-desc="{{ item.explanation }}"
        data-desc-en="{{ item.explanation_en }}"
        data-section-id="{{ item.pk }}"
        data-type="section">
      {% if language == 'jp' %}
      <div class="modal-staff-credit-section-header--title heading-18-spacing">{{ item.section_name }}</div>
        <div class="modal-staff-credit-section-header--desc bodytext-11">{{ item.explanation }}</div>
      {% else %}
        <div class="modal-staff-credit-section-header--title heading-18-spacing">{{ item.section_name_en }}</div>
        <div class="modal-staff-credit-section-header--desc bodytext-11">{{ item.explanation_en }}</div>
      {% endif %}
      {% if editable %}
        <div class="modal-staff-credit-section--action-btn drag-section">
          <i class="icon icon--sicon-equal"></i>
          <i class="icon icon--sicon-trash"></i>
        </div>
      {% endif %}
    </div>
  </div>
{% else %}
  <div class="modal-staff-credit-section-container section--container"
      data-section-id="{{ item.pk }}">
    <div class="modal-staff-credit-section-header"
      data-section="{{ item.section_name }}"
      data-section-en="{{ item.section_name_en }}"
      data-desc="{{ item.explanation }}"
      data-desc-en="{{ item.explanation_en }}"
      data-section-id="{{ item.pk }}"
      data-type="item">
      <div class="all-artist-item-container">
          {% include 'credit/_item_artist.html' with item_artist=item language=language editable=editable %}
      </div>
      {% if editable %}
        <div class="modal-staff-credit-section--action-btn drag-section">
          <i class="icon icon--sicon-equal"></i>
          <i class="icon icon--sicon-trash"></i>
        </div>
      {% endif %}
    </div>
  </div>
{% endif %}