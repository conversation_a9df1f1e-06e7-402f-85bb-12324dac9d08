{% load static %}
{% load util %}
<div class="mmessage-main">
  {% if not message.is_near %}
      <div class="c-avatar32" style="background-image: url({{ receiver|get_avatar:'medium' }})"></div>
  {% endif %}
  <div class="mmessage-content">

    {% if message.content and message.content != '' %}
      <div class="mmessenger mmessenger--text mmessenger--gray">
        <div class="messenger-content">
          <div class="s-text s-text--gray">{{ message.content }}</div>
        </div>
      </div>
    {% endif %}

    {% for file in message|get_sorted_message_files %}
      {% if not file.folder %}
        {% with file.is_audio_file as type_file %}
          {% if type_file == 'audio' %}
            <div class="mmessenger mmessenger--audio-wave mmessenger--gray" data-file-id="{{ file.pk }}">
              <div class="messenger-content">
                <div class="s-audio s-audio--audio-wave s-audio--gray">
                  <div class="s-audio-control">
                    <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                      play_circle
                    </span>
                    <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
                  </div>
                  {% with audio_data=file.file|get_audio_url_with_waveform %}
                  <div class="s-audio-source" 
                       data-link="{{ audio_data.url }}"
                       data-title="{{ file.real_name }}" 
                       data-waveColor="#d3d3d3"
                       data-progressColor="#53565a" 
                       data-peaks-loaded="{{ file.peaks }}"
                       data-waveform-url="{{ audio_data.waveform_url }}"
                       data-has-waveform="{{ audio_data.has_waveform|yesno:'true,false' }}"></div>
                  {% endwith %}
                  <div class="s-audio-waveform"></div>
                  <div class="s-audio-time"></div>
                </div>
              </div>
            </div>
          {% else %}
            <div class="mmessenger mmessenger--file mmessenger--gray" data-toggle="modal"
                 data-target="#modal-{{ type_file }}-popup" data-link="{{ file.file.url }}"
                 data-name="{{ file.real_name }}" data-type="{{ type_file }}" data-file-id="{{ file.pk }}">
              <div class="messenger-content">
                <div class="s-file s-file--file s-file--gray {% if type_file == 'image' %}messenger-image-preview-content{% endif %}">
                  {% if type_file == 'image' %}
                    <div class="comment-file-content">
                      <i class="icon icon--sicon-clip"></i>{{ file.real_name }}
                    </div>
                    <div class="image-preview-comment {% if type_file == 'image' %}active-view{% endif %}">
                      <img src="{{ file.file.url }}" alt="{{ file.real_name }}" loading="lazy">
                    </div>
                  {% else %}
                    <i class="icon icon--sicon-clip"></i>{{ file.real_name }}
                  {% endif %}
                </div>
              </div>
            </div>
          {% endif %}
        {% endwith %}
      {% endif %}
    {% endfor %}

    {% for folder in message.folders.all %}
      {% if not folder.parent %}
        <div class="mmessenger mmessenger--file mmessenger--gray messager-folder" data-toggle="modal"
             data-target="#modal-{{ type_file }}-popup"
             data-name="{{ folder.name }}" data-type="{{ type_file }}" data-file-id="{{ folder.pk }}">
          <div class="messenger-content">
            <div class="s-file s-file--file s-file--gray">
              <i class="icon icon--sicon-storage"></i>{{ folder.name }}</div>
          </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>
</div>
<div class="mmessage-info {% if message|check_has_audio_file%}message-info-audio{% endif %}">
  {% include 'messenger/_message_infor.html' with message=message user=user %}
</div>
