{% load static %}
{% load util %}

<div class="cvideo refactor">
  <div class="project-delivery-item-content ui-sortable-handle {% if deleted %} deleted{% endif %}"
       data-scene-title-id="{{ st.pk }}"
       data-modified="{{ st.updated_at|get_updated_datetime }}" data-status="{{ st.status }}"
       data-rating="{{ st|get_rating:role  }}" data-title="{{ st.title }}" data-scene-id="{{ scene.pk }}">

       <div class="cvideo__meta video-action-done-rate" style="justify-content: space-between;">
        {% if not scene|get_schedule_out_of_date %}
          {% if st.status not in '5,6' %}
            <div title="" class="project-chapter-video-done {% if role == 'admin'  or view_only or scene|get_schedule_out_of_date %}cannot-check{% endif %}">
              <span class="material-symbols-rounded">
                check_circle
            </span>
            </div>
          {% else %}
            <div title="" class="project-chapter-video-undone {% if view_only or scene|get_schedule_out_of_date %}cannot-check{% endif %}">
              <span class="material-symbols-rounded u-text-blue">
                check_circle
            </span>
            </div>
          {% endif %}

          <div class="cvideo__rating " style="height: 100%; display: flex; justify-content: center; align-items: center;">
            {% with st|get_rating:role as rating %}
            <div class="stars {% if role == 'master_client' %} can-rate {% endif %}  {% if role == 'admin' or scene|get_schedule_out_of_date %}cannot-check average-star{% endif %}" data-rating="{{ rating }}">
              <span class="material-symbols-rounded star-1" data-value="1">star</span>
              <span class="material-symbols-rounded star-2" data-value="2">star</span>
              <span class="material-symbols-rounded star-3" data-value="3">star</span>
              <span class="material-symbols-rounded star-4" data-value="4">star</span>
              <span class="material-symbols-rounded star-5" data-value="5">star</span>
            </div>
            {% endwith %}
          </div>
        {% endif %}
        
      </div>

    <div class="cvideo__thumb" data-file-type="{% if scene.movie.name %}{{scene.is_audio_file}}{% endif %}" out-of-date-schedule="{% if scene|get_schedule_out_of_date %}{{scene|get_schedule_out_of_date}}{% endif %}">
      {% if not scene|get_schedule_out_of_date %}
        {% with video_info=scene.movie|get_video_url_with_fallback %}
        <video width="100%" height="256px" poster="{{ scene|get_thumbnail }}"
              preload="none" style="vertical-align: middle; border-radius: 6px;"
              data-video-src="{{ video_info.url }}"
              data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
              data-fallback-src="{% if scene.movie.name %}{{ scene.movie.url }}{% endif %}">
          <source src="{{ video_info.url }}" type="video/mp4">
        </video>
        {% endwith %}
      {% else %}
        <div class="thumb-schedule-video" style="{% if scene.movie.name %}background-image: url({{ scene|get_thumbnail }});mix-blend-mode: luminosity;opacity: 0.5;{% endif %}">
          <!-- <span>
            {{scene|get_schedule_out_of_date}}
          </span> -->
        </div>
      {% endif %}
    </div>

    <div class="cvideo__heading" style="line-height: 200%; margin: 0;">
      <div class="cvideo__title" data-scene-title-id="{{ st.pk }}" style="padding-left: 0;">{{ st.title }}</div>
      {% if not view_only %}
        <div class="scene-title__action">
          <a class="scene-title-button scene-title__move" href="javascript:void(0)">
            <i class="fas fa-arrows-alt"></i>
          </a>
          {% if role == 'admin' %}
            <a class="scene-title-button scene-title__delete" href="javascript:void(0)">
              <i class="icon icon--sicon-trash"></i>
            </a>
          {% endif %}
          <a class="scene-title-button scene-title__edit" href="javascript:void(0)">
            <i class="icon icon--sicon-pencil"></i>
          </a>
        </div>
      {% endif %}
    </div>

    <div class="cvideo__date-time" style="margin: 0; padding: 0; color: #A7A8A9;">
      {% if scene.movie.name %}
        {% if scene|get_schedule_out_of_date %}
          <div class="cvideo__date"  style="font-size: 11px;">配信予定: {{scene|get_schedule_out_of_date}}</div>
        {% else %}
          <div class="cvideo__date"  style="font-size: 11px;">{{ st|get_schedule_out_of_date:'last-update' }}</div>
          <!-- <div class="cvideo__time" style="line-height: unset; font-size: 11px;">{{ st.updated_at|date:"H:i" }}</div> -->
        {% endif %}
      {% else %}
        {% if not scene|get_schedule_out_of_date or scene|get_schedule_out_of_date == 'まもなくリリース' %}
          <div class="release-soon">まもなくリリース</div>
        {% else %}
          <div class="cvideo__date" style="font-size: 11px;">配信予定: {{scene|get_schedule_out_of_date}}</div>
        {% endif %}
      {% endif %}
    </div>

  </div>
</div>
