{% load util %}
{% load static %}
{% for project in projects %}
  {% with  user|get_product_user:project as product_user %}
    <div class="project-item-handle {% if not user|check_notification_product:project %}no-notification{% endif %}"
         data-product-user-id="{{ product_user.pk }}">
      <a href="
              {% if user.role == 'master_admin' %}{% url 'app:message_owner'  project.pk %} {% else %}{% url 'app:top_project_detail' project.pk %}{% endif %}">
        <div class="project-item"
             data-project-id="{{ project.pk }}">
          {% include 'top/_product_banner.html' with project=project user=user type_page=type_page project_list=True show_staff=True %}
        </div>
      </a>
    </div>
  {% endwith %}
{% endfor %}



