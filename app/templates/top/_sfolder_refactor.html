{% load static %}
{% load util %}
<div class="sfolder  {% if type_comment in 'messenger,messenger_owner' %}{% if folder.message.user.role != user.role %}sfolder-gray{% endif %}{% endif %}" data-message-id="{{ folder.message.pk }}" data-folder-id="{{ folder.pk }}">
  <ul class="mfolder">
      <div class="parent-folder">
          <i class="icon icon--sicon-storage"></i>
          <span class="hasSub" data-folder-id="{{folder.pk}}">{{ folder.name }}</span>
          {% if folder|check_child_folder_refactor %}
            <i class="icon icon--sicon-download icon--sicon-folder-download pointer" ></i>
          {% endif %}
      </div>
    <ul>
      {% for child in folder.child_folders.all %}
        <li class="mfolder__sub"><i class="icon icon--sicon-storage"></i>
          <span class="hasSub" data-folder-id="{{child.pk}}">{{ child.name }}</span>
          {% if child|check_child_folder_refactor %}
            <i class="icon icon--sicon-download icon--sicon-folder-download pointer"></i>
          {% endif %}
        </li>
      {% endfor %}
      {% for file in folder.children.all %}
        {% include 'top/_item_mfolder_sub_refactor.html' with file=file message=folder.message type_comment=type_comment %}
      {% endfor %}
    </ul>
  </ul>
</div>
