{% load util %}
{% load static %}
{% with  user|get_product_user:project as product_user %}
  <div class="sprojects-item project-item-handle" data-project="{{ project.pk }}" data-product-user-id="{{ product_user.pk }}">
    {% with project|get_owners_project as owners %}
      <a href="{% if user.role == 'master_admin' %}{% url 'app:product_update'  project.pk %} {% else %}{% url 'app:top_project_detail' project.pk %}{% endif %}">
        {% with user|check_notification_product:project as notification %}
          <div class="sproject {% if not notification %}no-notification {% endif %}"
               style="background-image: url({{ project|get_image }})">
          <div class="sproject__main">
          <div class="sproject__top">
            <div class="bdropdown">
            <span class="bdropdown-toggle dropdown-toggle" role="button" id="sproject-id"
                  data-toggle="dropdown"
                  aria-haspopup="true" aria-expanded="false">
              <i class="icon icon--sicon-menu"></i>
            </span>
              <div class="bdropdown-menu dropdown-menu drop-action-project" aria-labelledby="sproject-id">
                {% if user not in owners and user.role not in 'master_admin,admin' %}
                  <span class="bdropdown-item action-notification">{% if not notification %}ON Notification{% else %}OFF
                    Notification{% endif %}</span>
                {% endif %}
                <span class="bdropdown-item">show Credit</span>
                {% if user.role in 'admin, master_admin' or user in owners %}
                  <span class="bdropdown-item max_scene-edit"
                        data-max-scene="{{ project.max_scene }}">Edit max scene</span>
                {% endif %}
              </div>
            </div>
          </div>
          <div class="sproject__bottom">
          <div class="sproject__user-list">
          {% if owners %}
            {% for owner in owners %}
              <div class="sproject__user" data-user="{{ owner.pk }}" title="{{ owner.get_display_name }}">
                <div class="avatar avatar--image avatar--24 avatar--square">
                  <div class="avatar-image background-avt"
                       style="background-image: url({% if owner.avatar %}{{ owner|get_avatar:'small' }}{% else %}/static/images/default-avatar-owner.png{% endif %})"></div>
                </div>
              </div>
            {% endfor %}
          {% endif %}
          {% with project|get_member as members %}
            {% for member in members %}
              <div class="sproject__user" data-user="{{ member.pk }}" title="{{ member.get_display_name }}">
                <div class="avatar avatar--image avatar--24 avatar--square">
                  <div class="avatar-image background-avt"
                       style="background-image: url({{ member|get_avatar:'small' }})"></div>
                </div>
              </div>
            {% endfor %}
          {% endwith %}
          {% if user in owners or user.role in 'master_admin,admin' %}
            <span class="sproject__user-btn" data-toggle="modal" data-target="#modal-member-manage">
              <i class="icon icon--sicon-add-circle-o"></i>
            </span>
          {% endif %}
        {% endwith %}
        </div>
{#        <div class="sproject__number">{{ product_user.view }}</div>#}
        </div>
        </div>
        {% with done_count=project.current_heart scene_count=project.current_scene %}
          <div class="sproject__progressbar" data-done-scene='{{ done_count|floatformat:"0" }}'
               data-current-scene='{{ scene_count|minus:done_count|floatformat:"0" }}'>
            <div class="sprogress">
              {% if user.role == 'master_admin' %}
                {% with budget_offer=project|get_budget_offer budget_admin=project|get_budget_for_admin %}
                  {% with project.total_budget as budget %}
                    {% with budget_offers=project|get_money_spent:budget_offer budget_admins=project|get_budget_admins:budget_admin %}
                      <div class="progress-bar bg-success" role="progressbar" style="width: {{ budget_offers }}%"
                           aria-valuenow="40"
                           aria-valuemin="0"
                           aria-valuemax="100"></div>
                      <div class="progress-bar bg-warning" role="progressbar"
                           style="width: {{ budget_admins|minus:budget_offers }}%" aria-valuenow="40"
                           aria-valuemin="0"
                           aria-valuemax="100"></div>
                    {% endwith %}
                  {% endwith %}
                {% endwith %}
              {% else %}
                <div class="progress-bar bg-success" role="progressbar"
                     style="width: {{ project.get_current_heart_rate }}%"
                     aria-valuenow="40"
                     aria-valuemin="0"
                     aria-valuemax="100"></div>
                <div class="progress-bar bg-warning" role="progressbar"
                     style="width: {{ project.get_current_scene_rate }}%"
                     aria-valuenow="40"
                     aria-valuemin="0"
                     aria-valuemax="100"></div>
              {% endif %}
            </div>
          </div>
        {% endwith %}
        </div>
      </a>
    {% endwith %}
    <div class="sproject-time">
      <i class="icon icon--sicon-clock"></i>{{ project|get_last_time_update_project }}
    </div>
  </div>
{% endwith %}
