{% extends "base_nofooter_refactor.html" %}
{% load bootstrap3 %}
{% load util %}
{% load static %}
{% load user_agents %}
{% load i18n compress %}

{% block extrahead %}
    {% comment %} <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/sumoselect.min.css"/> {% endcomment %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/sumoselect.css' %}"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.css" />
    {% compress css %}
    <link rel="stylesheet" type="text/css" href="{% static 'css/main.css' %}"/>
    {% comment %} <link rel="stylesheet" type="text/css" href="{% static 'css/modal.css' %}"/> {% endcomment %}
    {% endcompress %}
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
{% endblock %}

{% block content %}
  {% compress js inline %}
  <script src="{% static 'js/cropper.min.js' %}"></script>
  {% endcompress %}
  {% compress css %}
  <link href="{% static 'css/cropper.min.css' %}" rel="stylesheet">
  <link rel="stylesheet" type="text/css" href="{% static 'css/main_new.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/top.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/project_list.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/top_admin.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/product_banner.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_manager.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/drop_file.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/message_file.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/modal_contract.css' %}"/>
  <link rel="stylesheet" type="text/css" href="{% static 'css/message.css' %}"/>

    <style>
        .btn-create-new-project {
            margin: 0 auto 20px;
            text-align: center;
            width: 40%;
            min-width: 300px;
            position: relative;
            padding-top: 40px;
            color: #53565a;
        }

        .btn-create-new-project:before {
            content: '+';
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #52565a;
            color: #fff;
            top: 10px;
            position: absolute;
            left: 48%;
            font-weight: bold;
            font-size: 1.2em;
            line-height: 20px;
        }

        .owner-top {
            background: #FFFFFF !important;
        }

        .btn-create-new-project:hover {
            color: #009ace;
        }

        .btn-create-new-project:hover:before {
            background: #009ace;
        }
    </style>
    {% endcompress %}
    <main class="owner-top {% if not request|is_pc %} on-mobile{% endif %}">
      <div class="container">
        <div class="sprojects">
          {% if user.role == "master_client" or user.role == "admin" %}
              <div class="sprojects-add-project">
                  <a class="sprojects-add-project__content" href="
                          {% if user.role == "master_client" %}{% url 'app:direct_create_offer' %}{% else %}{% url 'app:artist_create_product' %}{% endif %}">
                      <span class="material-symbols-rounded" style="font-size: 32px;">add_circle</span>
                      <div class="sprojects-add-project__text bodytext-11">
                          {% if user.role == "master_client" %}
                              プロジェクトを相談
                          {% else %}プロジェクトを作成{% endif %}
                      </div>
                  </a>
              </div>
          {% endif %}
          <div class="sprojects-filter">
            <div class="form-check custom-switch">
              <label class="form-check-label">
                <div class="form-check-group">
                  <input class="form-check-input switch-checkbox" type="checkbox" name="switch-chapter"
                         id="switch-chapter"><span class="switch-slider"></span>
                </div>
                <span class="switch-label" >{% trans 'See also the archive' %}</span>
              </label>
            </div>
            <div class="video-order">
              <div class="video-order-type video-order__asc"><i class="icon icon--sicon-asc"></i>
              </div>
              <div class="video-order-type video-order__desc active"><i class="icon icon--sicon-desc"></i>
              </div>
            </div>
            <div class="video-order-by">
              <div class="sselect-wrapper select-white">
                <select class="select" id="video-orderby" placeholder="Select Here" data-search="false"
                        data-search-text="Enter the keyword">
                  <option value="modified">最終更新日</option>
                  <option value="created">開始日</option>
                  <option value="rating">評価</option>
                  <option value="order">手動</option>
                </select>
              </div>
            </div>
          </div>


          <div class="project-list" data-total-page="{{ total_page }}">

            {% for project in projects %}
              {% with  user|get_product_user:project as product_user %}
                <div class="project-item-handle {% if not user|check_notification_product:project %}no-notification{% endif %}"
                     data-product-user-id="{{ product_user.pk }}">
                  <a href="

                          {% if user.role == 'master_admin' %}{% url 'app:message_owner'  project.pk %} {% else %}{% url 'app:top_project_detail' project.pk %}{% endif %}">
                    <div class="project-item {% if not request|is_pc %} on-mobile{% endif %}"
                         data-project-id="{{ project.pk }}">
                      {% include 'top/_product_banner.html' with project=project user=user type_page=type_page project_list=True is_pc=request|is_pc show_staff=True %}
                    </div>
                  </a>
                </div>
              {% endwith %}
            {% endfor %}
          </div>
          <div class="project-member-setting-modal modal fade" id="project-member-setting"
               role="dialog" style="display: none;">
            <!-- ajax will append data here -->
          </div>
        </div>
      </div>
        <div class="modal fade share-modal" role="dialog" id="shareModal" style="z-index: 9999;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title"></h6>
                        <button class="close" data-dismiss="modal"
                                type="button">閉じる
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="video-time-slider-item">
                            <div class="video-time-slider-content">
                                <div class="video-time-slider-start">00:00
                                </div>
                                <div class="video-time-slider-bar"></div>
                                <div class="video-time-slider-end hide">00:00
                                </div>
                            </div>
                            <div class="video-time-slider-label">
                                <div class="video-time-slider-label-start">
                                    開始位置を指定
                                </div>
                                <div class="video-time-slider-label-end hide">
                                    終了位置も指定
                                </div>
                            </div>
                        </div>
                        <div class="modal-share-link">
                            <div class="video-item-share-input">
                                <input class="video-share-link"
                                       id="video-share-link" type="text"
                                       name="video-share-link"
                                       placeholder=""
                                       value="">
                            </div>
                            <a class="button button--text button--text-primary video-item-share-btn"
                               href="javascript:;" role="button">URLをコピー</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" role="dialog" id="processingSceneModal" style="overflow: hidden;">
            <div class="modal-dialog modal-lg" role="document" style="width: 100%">
                <div class="modal-content" style="height: 100vh">
                    <div class="modal-body container" style="max-height: 100vh;">
                        <div class="project-video-item show-comment">
                            <div class="video-item-wrap">
                                <div class="video-item-list">
                                    <div class="video-item-component active"
                                         data-scene-id="">
                                        <div class="video-item-component-content">
                                            <div class="video-item-component-content-video">
                                                <div class="video-item-chapter"></div>
                                                <div class="video-item-variation"></div>
                                                <div class="video-item-slug"
                                                     share-url=""></div>
                                                <a class="video-button video-item-bookmark" href="javascript:;"></a>
                                                <a class="video-button video-item-share" href="javascript:void(0)"
                                                   data-toggle="modal"
                                                   data-target="#shareModal"></a>
                                                <div class="video-button video-item-button-top">とりあえずOK</div>
                                                <div class="video-button video-item-button-bottom">チェックバック</div>
                                                <div class="video-button video-item-button-left">もういちど</div>
                                                <video width="100%" height="auto" poster="" controls preload="none">
                                                    <source src="" type="video/mp4">
                                                </video>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="video-item-control">
                                    <div class="video-item-bullets-wrap">
                                        <div class="video-item-bullets">
                                            <div class="video-item-bullet-prev disable"
                                                 data-current_index="0"></div>
                                            <div class="video-item-bullet-list">
                                                <div class="video-item-bullet active"
                                                     data-index=""></div>
                                            </div>
                                            <div class="video-item-bullet-next"
                                                 data-current_index="0"></div>
                                        </div>
                                    </div>
                                    <div class="video-item-thumbnails">
                                        <div class="video-item-thumbnail-list">
                                            <div class="video-item-thumbnail active"
                                                 data-index="" data-id="">
                                                <video class="active" width="100%" height="auto" preload="none"
                                                       poster="">
                                                </video>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="video-item-collapse">
                                        <div class="video-item-collapse-button"></div>
                                    </div>
                                </div>
                                <br>
                            </div>
                            <div class="video-item-comment" data-scene-id="">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% include 'top/_modal_setting_setting.html'  with project_list=True %}
    </main>

    {% compress js inline %}

    <script>
        let default_thumb = '{% static 'images/messenger-thumb.png' %}';
        let user = {{ user.id }};

        $('.project-item').find('.project-item__more-left-top').on('click', function (e) {
            e.preventDefault();
            $(this).toggleClass('active');
        });
    </script>
    <script src="{% static 'js/isInViewport.min.js' %}"></script>
    <script src="{% static 'js/jquery.scopeLinkTags.js' %}"></script>
    <script src="{% static 'js/top_page_admin.js' %}"></script>
    <script src="{% static 'js/top_page_member.js' %}"></script>
    {% endcompress %}
{% endblock %}
{% block extra_script %}
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/locale/ja.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.2.5/moment-timezone-with-data.min.js" integrity="sha512-OvYMp/zgYHU6ojgDxmtCLsgHBtSrX34Cx/01Tv2bFfbJnlVMbIv5TVVTQ+0wU4GHVDIlHAMMsjFBpTzDnSqIiA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.sumoselect/3.0.2/jquery.sumoselect.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.8.1/min/dropzone.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.8.0/Chart.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.0.2/nouislider.min.js"></script>
    <script src="{% static 'js/unpkg_wavesurfer.js' %}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.4.0/bootbox.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
            integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
            crossorigin="anonymous"></script>
    <script src="{% url 'javascript-catalog' %}"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    {% compress js inline %}
    <script type="text/javascript" src="{% static 'js/action_banner.js' %}"></script>
    <script src="{% static 'js/utils.js' %}"></script>
    {% endcompress %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    {% compress js inline %}
    <script src="{% static 'js/sort_project.js' %}"></script>
    {% endcompress %}
{% endblock %}
