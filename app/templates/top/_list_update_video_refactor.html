{% load static %}
{% load util %}

{% comment %} <div class="cvideo cvideo__thumb-list-update"> {% endcomment %}
  <div class="project-delivery-item-content item-delivery-video" data-scene-title-id="{{ st.pk }}"
       data-status="{{ st.status }}" data-rating="{{ st|get_rating:role }}"
       data-title="{{ st.title }}" data-scene-id="{{ scene.pk }}">
       {% with scene|get_thumbnail as scene_link %}
       <div class="{% if 'audio' in scene_link %}navigation-scene{% endif %} u-col u-gap4">
       {% endwith %}
       <div class="cvideo__title heading-18-spacing">{{ st.title }}</div>
       <div class="cvideo__heading">
         <div class="cvideo__meta">
           <div class="cvideo__date-time">
             <div class="cvideo__date">{{ st.updated_at|format_deadline_with_weekday }}</div>
             {# <div class="cvideo__time">{{ st.updated_at|date:"H:i" }}</div> #}
           </div>
         </div>
       </div>
      </div>
    {% with scene|get_thumbnail as scene_link %}
      {% if "audio" not in scene_link %}
      <div class="cvideo__thumb cvideo__thumb-list-update">
        {% with video_info=scene.movie|get_video_url_with_fallback %}
        <video width="100%" height="100%" poster="{{ scene|get_thumbnail }}"
                preload="none" loading="lazy" style="border-radius: 6px;"
                data-video-src="{{ video_info.url }}"
                data-is-hls="{{ video_info.is_hls|yesno:"true,false" }}"
                data-fallback-src="{% if scene.movie %}{{ scene.movie.url }}{% endif %}">
          <source src="{{ video_info.url }}" type="video/mp4">
        </video>
        {% endwith %}
      </div>
      {% else %}
        <div class="d-flex flex-direction-column">
          <div class="s-audio s-audio--audio-wave s-audio--white audio-pc u-relative d-block u-w100" data-scene-id="{{ scene.pk }}">
            {% with audio_data=scene.movie|get_audio_url_with_waveform %}
            <div class="s-audio-source" 
                 data-link="{{ audio_data.url }}" 
                 title="{{ scene.real_name }}" 
                 data-waveColor="#d3d3d3" 
                 data-progressColor="#53565a"
                 data-waveform-url="{{ audio_data.waveform_url }}"
                 data-has-waveform="{{ audio_data.has_waveform|yesno:'true,false' }}"></div>
            {% endwith %}
            <div class="s-audio-waveform u-w100"></div>
            <div class="s-audio-time new-position"></div>
            <div class="s-audio-time-total"></div>
          </div>
          <div class="audio-control-custom u-row u-gap16">
            <div class="s-audio-control pin-time-audio nav-play-sp u-w100 u-row u-gap8 s-audio-control-progress-tab">
              <span class="material-symbols-rounded c-icon-play-audio u-fontsize-32 material-symbol-play">
                play_circle
              </span>
              <span class="material-symbols-rounded c-icon-pause-audio u-fontsize-32 material-symbol-pause">pause</span>
              <input class="s-audio-wave-zoom" type="range" min="1" max="100" value="1">
            </div>
          </div>
        </div>
      {% endif %}
    {% endwith %}
    {% with st|get_last_message_title_refactor:comments as last_comment %}
      {% if last_comment %}
        <div class="cvideo__last-msg">

          <!-- Comment file -->
          <div class="msg__wrap">
            <div class="msg__avatar">
              <img src="{{ last_comment.user|get_avatar:'small' }}" alt="" width="32" height="32">
            </div>
            {% if last_comment.comment and last_comment.comment != '' %}
              <div class="msg__info">
                <div class="bodytext-13 u-line-height-150">{{ last_comment.comment|linebreaksbr }}</div>
              </div>

            {% else %}

              <div class="msg__file-text-none">
                {% for folder in last_comment.folders.all %}
                  {% if not folder.parent %}
                    <div class="msg__file"><i class="icon icon--sicon-storage"></i>
                      <div class="msg__file-name">{{ folder.name }}</div>
                    </div>
                  {% endif %}
                {% endfor %}

                {% for file in last_comment.files.all %}
                  {% if not file.folder %}
                    <div class="msg__file">
                      <div class="msg__file-name">{{ file.real_name }}</div>
                    </div>
                  {% endif %}
                {% endfor %}
              </div>

            {% endif %}
          </div>
          {% if last_comment.comment and last_comment.comment != '' %}
            {% for folder in last_comment.folders.all %}
              {% if not folder.parent %}
                <div class="msg__file"><i class="icon icon--sicon-storage"></i>
                  <div class="msg__file-name">{{ folder.name }}</div>
                </div>
              {% endif %}
            {% endfor %}

            {% for file in last_comment.files.all %}
              {% if not file.folder %}
                <div class="msg__file">
                  <div class="msg__file-name">{{ file.real_name }}</div>
                </div>
              {% endif %}
            {% endfor %}
          {% endif %}
        </div>
      {% endif %}
    {% endwith %}

    <div style="display: flex; justify-content: flex-end;">
  {% if st.status not in '5,6' %}
    <div title=""
      class="{{ show_button }} project-chapter-video-done u-line-height-100 u-col-center u-gap2 label8 {% if role == 'admin' or view_only %}cannot-check{% endif %}">
      <span class="material-symbols-rounded u-fontsize-40">
        check_circle
      </span>OK
    </div>
  {% else %}
    <div title=""
         class="{{ show_button }} project-chapter-video-undone {% if view_only %}cannot-check{% endif %}">
         <span class="material-symbols-rounded u-text-blue">
          check_circle
        </span>
      </div>
  {% endif %}
    </div>
  </div>
{% comment %} </div> {% endcomment %}
