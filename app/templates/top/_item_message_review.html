{% load static %}
{% load util %}
{% with message.user_id|find_other_user_id:user_ids as other_id %}
<div class="mmessage-container refactor">
    <div class="mmessage {% if user.pk|check_user:message.user_id %} mmessage--sent {% else %} mmessage--received {% endif %} {% if message.is_near %}mmessage-near{% endif %}">
        <div class="u-col">
            <ul class="title-review u-row-between u-w100">
                <li class="bodytext-11">{{other_id|get_display_fullname}}さんへのレビュー</li>
            </ul>
            <div class="{% if user.pk|check_user:message.user_id %} c-message-ours {% else %} c-message-their {% endif %} bodytext-13">
                {% if message.review != '3' %}
                    <div class="material-symbols-rounded u-fontsize-16 {% if user.pk|check_user:message.user_id %} my-review {% endif %} {% if message.review == '1' %} u-text-blue {% else %} u-text-gray {% endif %} u-pb8"> {% if message.review == '1' %} thumb_up {% else %} thumb_down {% endif %}</div>
                {% endif %}
                <div class="c-quote u-w100 u-relative" style="display: block;">
                <span class="material-symbols-rounded c-icon-quote {% if message.status == '3' %} hide {% endif %}">format_quote</span>
                <div class="u-row u-items-start">
                    <p id="review-text-quote" class="bodytext-quote u-text-justify">{{ message.note}}</p>
                </div>
                <div class="u-row-end u-gap16 u-mt8 {% if message.status != '1' %} hide {% endif %}" id="reviewer" style="display: flex;">
                    <p class="heading-16 c-quote-line">{{message.user_id|get_display_fullname}}</p><span class="bodytext-11">{{message.user_id|get_postion}}</span>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endwith %}