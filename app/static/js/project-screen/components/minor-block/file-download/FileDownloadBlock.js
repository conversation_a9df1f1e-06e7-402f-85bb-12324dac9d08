import { PROJECT_DEFAULT_FILE_HOST, getVideoUrl, getOriginalFileUrl } from "../../../constant/index.js";

const FileDownloadBlock = ({ file }) => {
	switch (file?.type_file_name){
		case 'image':
			return `
			<div class="image-preview-comment active-view">
      	<img src="${ PROJECT_DEFAULT_FILE_HOST }/${file?.file}" alt="${file?.real_name}" loading="lazy">
      </div>`
		case 'video':
			// Determine video source and HLS attributes
			const videoUrl = getVideoUrl(file);
			const isHLS = file?.is_hls ? 'true' : 'false';
			const fallbackUrl = getOriginalFileUrl(file?.file);

			return `
			<div class="block-video-cmt" style="width: 100%">
        <video height="144px" preload="none" alt="${file?.real_name}" loading="lazy"
               data-video-src="${videoUrl}"
               data-is-hls="${isHLS}"
               data-fallback-src="${fallbackUrl}"
               ${isHLS === 'true' ? '' : `src="${videoUrl}"`}>
          ${isHLS === 'true' ? '' : `<source src="${videoUrl}" alt="${file?.real_name}" type="video/mp4"/>`}
        </video>
      </div>`
		case 'document':
			let fileInfo = {};
			let parsed = false;
			
			try {
				fileInfo = JSON.parse(file?.file_info);
				parsed = true;
			}catch(e){
				//data is not JSON
			}
			return `
			<div class="block-pdf-image">
				${parsed ? `<img src="${ PROJECT_DEFAULT_FILE_HOST }/${file?.file}" class="pdf-image" alt="pdf image">` : ''}
      </div>`
		default:
			return '';
	}
}
	
export default FileDownloadBlock;
