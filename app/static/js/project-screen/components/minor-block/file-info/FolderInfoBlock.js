import FileInfoBlock from "./FileInfoBlock.js";

const FolderInfoBlock = ({ folder, comments, comment_files }) => {
	let type_comment = 'messenger'
	let isMessenger = ['messenger','messenger_owner'].includes(type_comment);
	let extraClassName = '';
	let comment = comments.find(comment => comment.comment_id == folder?.message_id)

	if (isMessenger){
		if (folder?.message?.offer){
			extraClassName = 's-file--gray';
		}
	}else{
		if (!comment?.is_same_role){
			extraClassName = 's-file--gray';
		}
	}

  return `
    <div class="sfolder ${extraClassName}" data-message-id="${ comment?.comment_id }" data-folder-id="${ folder?.folder_id }">
      <ul class="mfolder">
        <div class="parent-folder">
          <i class="icon icon--sicon-storage"></i>
          <span class="hasSub" data-folder-id="${folder?.folder_id}">${ folder?.name }</span>
          ${folder?.has_child_folder ? `<i class="icon icon--sicon-download icon--sicon-folder-download pointer" ></i>` : ''}
        </div>
				<ul>
				${folder?.children?.map((child) => {
					if (!child?.file_id){
						return `
						<li class="mfolder__sub">
							<i class="icon icon--sicon-storage"></i>
							<span class="hasSub" data-folder-id="${child?.file_id ?? child?.folder_id}">${ child?.real_name ?? child?.name }</span>
							${child?.has_child_folder ? `<i class="icon icon--sicon-download icon--sicon-folder-download pointer"></i>` : ''}
						</li>`
					}else{
						return '';
					}
				}).join('')}
				${folder?.children?.map((child) => {
					return FileInfoBlock({file: child, comments})
				}).join('')}
				</ul>
      </ul>
    </div>`;
};

export default FolderInfoBlock;
