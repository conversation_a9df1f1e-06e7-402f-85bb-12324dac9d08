/* iframe helper for jquery.nicescroll v3.7.6 InuYaksa - MIT - https://nicescroll.areaaperta.com */
!function(e,t){function n(t){var n=e.createEvent("MouseEvents");n.initEvent("wheel",!0,!0),n.deltaMode=t.deltaMode,n.deltaX=t.deltaX,n.deltaY=t.deltaY,n.deltaZ=t.deltaZ,n.wheelDelta=t.wheelDelta,n.wheelDeltaX=t.wheelDeltaX,n.wheelDeltaY=t.wheelDeltaY,r.dispatchEvent(n)}function l(e){do{if(void 0!==$.data(e,"__nicescroll"))return e;e=e.parentNode||!1}while(e);return!1}var a=e.body,r=t.parent;if(r&&"createEvent"in e){a.addEventListener("wheel",n)}if(t.addEventListener){var i=function(){var t=e.createElement("style");return t.appendChild(e.createTextNode("")),e.head.appendChild(t),t.sheet}(),d=null,o=null,c=[];t.addEventListener("scroll",function(e){o&&t.scrollTo(c[0],c[1])}),t.addEventListener("load",function(){var e=!1;$.nicescroll.each(function(){var t=this;t.scrollstart(function(){e||i.insertRule("iframe { pointer-events: none !important; }",0),e=!0}),t.scrollend(function(){e&&i.deleteRule(0),e=!1})}),$("iframe").each(function(){this.addEventListener("mouseenter",function(e){var n=l(d=e.target);o=n,c=[t.scrollX,t.scrollY]}),this.addEventListener("mouseleave",function(e){d=o=null})})})}}(document,window);
