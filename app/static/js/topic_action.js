var $dropzoneUploadVideoTopic = [];
var $dropzoneUpLoadDetailedMaterials = [];
var $dropzoneUpLoadWork = [];
let listDataChoices = [];
let is_delete_file_video = false;
let current_x;
let current_y;
let current_height;
let current_width;
let current_img;
let current_file;
let file_real_name = '';
let thumbnail_real_name = '';
let video_real_name = '';
let is_delete_file = false;
let is_delete_video = false;
let selections = [];
let list_selection_delete = [];
let artist_id = '';
let selectionDataFormat = {
    'id': '',
    'title': '',
    'description': '',
    'sale_id': [],
    'selectors': [{
        'detail': '',
        'status': ''
    }],
    'toggles': [{
        'detail': '',
        'status': ''
    }],
    'order': ''
};

Dropzone.autoDiscover = false;

function playAudioTopicDetail() {
  $('.section-content__list-media').on(
    'click',
    '.gallery__item:not(.playing, .loading) .list-search__item-playpause',
    function () {
      const thumbnail = $(this).parent();
      const audio = thumbnail.find('audio');
      let type = thumbnail.data('type');

      $('.playing-navi').each(function (i,e) {
        $(e).find('audio')[0].pause();
        $(e).removeClass('playing-navi')
      })
      checkPlayPauseConditionForType(thumbnail);

      $('.gallery__item[data-type=' + type + ']').each(function () {
        $(this).removeClass('playing');
        $(this).removeClass('loading');
      });

      audio.on('canplay', function () {
        audio.off('canplay');
        thumbnail.removeClass('loading');
        thumbnail.addClass('playing');
      });

      toastr.info(thumbnail.attr('data-artist'), audio.attr('data-name'), {
        newestOnTop: true,
        timeOut: 5000,
        extendedTimeout: 10000,
      });

      audio[0].play();
      audio[0].muted = false;
      audio[0].loop = true;

      if (audio[0].readyState > 1) {
        thumbnail.removeClass('loading');
        thumbnail.addClass('playing');
      } else {
        thumbnail.removeClass('playing');
        thumbnail.addClass('loading');
      }

      let has_music = false; has_voice = false; has_sound = false;
      has_music = $('.section-content__list-media .gallery__item[data-type="music"].playing, .section-content__list-media .gallery__item[data-type="music"].loading').length > 0
      has_voice = $('.section-content__list-media .gallery__item[data-type="voice"].playing, .section-content__list-media .gallery__item[data-type="voice"].loading').length > 0
      has_sound = $('.section-content__list-media .gallery__item[data-type="sound_effect"].playing, .section-content__list-media .gallery__item[data-type="sound_effect"].loading').length > 0
      showToastPlayMix(has_music, has_voice, has_sound)
    }
  );

  $('.section-content__list-media').on(
    'click',
    '.playing .list-search__item-playpause',
    function () {
      const audio = $(this).parent().find('audio');
      audio.off('canplay');
      audio.each(function (i, e) {
        e.pause();
      });
      $(this).parent().removeClass('playing loading');
    }
  );
}

function playExhibitionWorks() {
  $('.exhibition-works-list__container').on(
    'click',
    '.gallery__item:not(.playing, .loading) .list-search__item-playpause',
    function () {
      const thumbnail = $(this).parent();
      const audio = thumbnail.find('audio');
      let type = thumbnail.data('type');

      $('.playing-navi').each(function (i,e) {
        $(e).find('audio')[0].pause();
        $(e).removeClass('playing-navi')
      })
      checkPlayPauseConditionForType(thumbnail);

      $('.gallery__item[data-type=' + type + ']').each(function () {
        $(this).removeClass('playing');
        $(this).removeClass('loading');
      });

      audio.on('canplay', function () {
        audio.off('canplay');
        thumbnail.removeClass('loading');
        thumbnail.addClass('playing');
      });

      toastr.info(thumbnail.attr('data-artist'), audio.attr('data-name'), {
        newestOnTop: true,
        timeOut: 5000,
        extendedTimeout: 10000,
      });

      audio[0].play();
      audio[0].muted = false;
      audio[0].loop = true;

      if (audio[0].readyState > 1) {
        thumbnail.removeClass('loading');
        thumbnail.addClass('playing');
      } else {
        thumbnail.removeClass('playing');
        thumbnail.addClass('loading');
      }

      let has_music = false; has_voice = false; has_sound = false;
      has_music = $('.exhibition-works-list__container .gallery__item[data-type="music"].playing, .exhibition-works-list__container .gallery__item[data-type="music"].loading').length > 0
      has_voice = $('.exhibition-works-list__container .gallery__item[data-type="voice"].playing, .exhibition-works-list__container .gallery__item[data-type="voice"].loading').length > 0
      has_sound = $('.exhibition-works-list__container .gallery__item[data-type="sound_effect"].playing, .exhibition-works-list__container .gallery__item[data-type="sound_effect"].loading').length > 0
      showToastPlayMix(has_music, has_voice, has_sound)
    }
  );

  $('.exhibition-works-list__container').on(
    'click',
    '.playing .list-search__item-playpause',
    function () {
      const audio = $(this).parent().find('audio');
      audio.off('canplay');
      audio.each(function (i, e) {
        e.pause();
      });
      $(this).parent().removeClass('playing loading');
    }
  );
}

function checkPlayPauseConditionForType(sample) {
  switch (sample.attr('data-type')) {
    case 'music':
        $('.playing[data-type="music"] audio, .playing[data-type="2mix"] audio, .loading[data-type="music"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
            $(e).parent().find('.list-search__item-playpause').trigger('click');
            e.pause();
        });
        break;
    case 'voice':
        $('.playing[data-type="voice"] audio, .playing[data-type="2mix"] audio, .loading[data-type="voice"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
            $(e).parent().find('.list-search__item-playpause').trigger('click');
            e.pause();
        });
        break;
    case 'sound_effect':
        $('.playing[data-type="sound_effect"] audio, .playing[data-type="2mix"] audio, .loading[data-type="sound_effect"] audio, .loading[data-type="2mix"] audio').each(function (i, e) {
            $(e).parent().find('.list-search__item-playpause').trigger('click');
            e.pause();
        });
        break;
    default:
        $('.playing audio, .loading audio').each(function (i, e) {
            $(e).parent().find('.list-search__item-playpause').trigger('click');
            e.pause();
        });
        break;
  }
}

// upload video
function dragDropVideoTopic() {
    let thisForm = $(
        '.topic-container__topic-form-1 .tocpic-form-1__upload-video'
    );
    if (thisForm.find('#dropzoneUpLoadVideoTopic').length) {
        var previewNode = thisForm.find(
            '.mattach-preview-container-form-upload-logo .mattach-template-form'
        );
        var previewTemplate = previewNode.parent().html();
        previewNode.parent().empty();
        Dropzone.autoDiscover = false;

        $dropzoneUploadVideoTopic = new Dropzone('#dropzoneUpLoadVideoTopic', {
            maxFilesize: 15000,
            timeout: 900000,
            autoDiscover: false,
            acceptedFiles: '.mp4, .avi, .MP4, .AVI, .mov, .MOV',
            previewsContainer:
                '.tocpic-form-1__upload-video .mattach-preview-container-form-upload-logo .mattach-previews-form',
            previewTemplate: previewTemplate,
            url: '/',
            autoProcessQueue: false,
            autoQueue: false,
            clickable: '#dropzoneUpLoadVideoTopic',
            maxFiles: 1,
            dictDefaultMessage:
                '<i class="icon icon--sicon-add-cirlce"></i>\n' +
                '<p>ファイルを選択</p>',
        });

        $dropzoneUploadVideoTopic.on('maxfilesexceeded', function (file) {
            $dropzoneUploadVideoTopic.removeAllFiles();
            $dropzoneUploadVideoTopic.addFile(file);
        });

        // $dropzoneUploadVideoTopic.on('removedfile', function (file) {
        //     real_name_information = '';
        // });

        $dropzoneUploadVideoTopic.on('addedfile', function (file, e) {
            if ($dropzoneUploadVideoTopic.files.length > 1) {
                $dropzoneUploadVideoTopic.removeAllFiles(true);
                $dropzoneUploadVideoTopic.addFile(file);
            }

            if (
                $dropzoneUploadVideoTopic.files &&
                $dropzoneUploadVideoTopic.files[0] &&
                $dropzoneUploadVideoTopic.files[0].name.match(
                    /\.(mp4|avi|MP4|AVI|mov|MOV)$/
                )
            ) {
                let file_dom = $(file.previewElement);
                real_name_information = file.name;
                let file_preview = thisForm
                    .find('.mattach-preview-container-form-upload-logo')
                    .find('.mcommment-file__name-form');
                for (let i = 0; i < file_preview.length; i++) {
                    $(file_preview[i]).text('');
                    $(file_preview[i]).append(
                        '<i class="icon icon--sicon-clip"></i>' + real_name_information
                    );
                    break;
                }
                file_name = this.files[0].name;
                if (thisForm.find('.account__file').length) {
                    thisForm.find('.account__file').hide();
                }

                uploadFileTopicS3(file_dom);
            } else if (!$dropzoneUploadVideoTopic.files) {
                return false;
            } else if (file.size > 1024 * 1024 * 100) {
                alert("100MB以下のファイルのみアップロードできます!");
                $dropzoneUploadVideoTopic.removeAllFiles(true);
            }else {
                alert('mp4, avi, movのみアップロードできます。');
                $dropzoneUploadVideoTopic.removeAllFiles(true);
            }
        });

        thisForm.find('.account__file .icon--sicon-close').on('click', function () {
            thisForm.find('.account__file').addClass('hide');
            is_delete_video = true
        });
    }
}

function dragDropWork() {
  let thisForm = $(
    '.topic-container__topic-form-3 .topic-form-3__upload-file-work'
  );
  if (thisForm.find('#dropzoneUpLoadWork').length) {
    var previewNode = thisForm.find(
      '.mattach-preview-container-form-upload-logo .mattach-template-form'
    );
    var previewTemplate = previewNode.parent().html();
    previewNode.parent().empty();
    Dropzone.autoDiscover = false;

    $dropzoneUpLoadWork = new Dropzone('#dropzoneUpLoadWork', {
      maxFilesize: 15000,
      timeout: 900000,
      autoDiscover: false,
      acceptedFiles: '.mp4, .avi, .MP4, .AVI, .mov, .MOV',
      previewsContainer:
        '.topic-form-3__upload-file-work .mattach-preview-container-form-upload-logo .mattach-previews-form',
      previewTemplate: previewTemplate,
      url: '/',
      autoProcessQueue: false,
      autoQueue: false,
      clickable: '#dropzoneUpLoadWork',
      maxFiles: 1,
      dictDefaultMessage:
        '<i class="icon icon--sicon-add-cirlce"></i>\n' +
        '<p>ファイルを選択</p>',
    });

    $dropzoneUpLoadWork.on('maxfilesexceeded', function (file) {
      $dropzoneUpLoadWork.removeAllFiles();
      $dropzoneUpLoadWork.addFile(file);
    });

    $dropzoneUpLoadWork.on('removedfile', function (file) {
      real_name_information = '';
    });

    $dropzoneUpLoadWork.on('addedfile', function (file, e) {
      if ($dropzoneUpLoadWork.files.length > 1) {
        $dropzoneUpLoadWork.removeAllFiles(true);
        $dropzoneUpLoadWork.addFile(file);
      }

      if (
        $dropzoneUpLoadWork.files &&
        $dropzoneUpLoadWork.files[0] &&
        $dropzoneUpLoadWork.files[0].name.match(/\.(mp4|avi|MP4|AVI|mov|MOV)$/)
      ) {
        let file_dom = $(file.previewElement);
        real_name_information = file.name;
        let file_preview = thisForm
          .find('.mattach-preview-container-form-upload-logo')
          .find('.mcommment-file__name-form');
        for (let i = 0; i < file_preview.length; i++) {
          $(file_preview[i]).text('');
          $(file_preview[i]).append(
            '<i class="icon icon--sicon-clip"></i>' + real_name_information
          );
          break;
        }
        file_name = this.files[0].name;
        if (thisForm.find('.account__file').length) {
          thisForm.find('.account__file').hide();
        }
      } else if (!$dropzoneUpLoadWork.files) {
        return false;
      } else {
        alert('mp4, avi, movのみアップロードできます。');
        $dropzoneUpLoadWork.removeAllFiles(true);
      }
    });

    thisForm.find('.account__file .icon--sicon-close').on('click', function () {
      thisForm.find('.account__file').addClass('hide');
      is_delete_file_video = true;
    });
  }
}

// upload file
function dragDropDetailedMaterials() {
  let thisForm = $(
    '.topic-container__topic-form-1 .topic-form-1__upload-detailed-materials'
  );
  if (thisForm.find('#dropzoneUpLoadDetailedMaterials').length) {
    var previewNode = thisForm.find(
      '.mattach-preview-container-form-upload .mattach-template-form'
    );
    var previewTemplate = previewNode.parent().html();
    previewNode.parent().empty();
    Dropzone.autoDiscover = false;

    $dropzoneUpLoadDetailedMaterials = new Dropzone(
      '#dropzoneUpLoadDetailedMaterials',
      {
        maxFilesize: 15000,
        timeout: 900000,
        autoDiscover: false,
        acceptedFiles: 'application/pdf',
        previewsContainer:
          '.topic-form-1__upload-detailed-materials .mattach-preview-container-form-upload .mattach-previews-form',
        previewTemplate: previewTemplate,
        url: '/',
        autoProcessQueue: false,
        autoQueue: false,
        clickable: '#dropzoneUpLoadDetailedMaterials',
        maxFiles: 1,
        dictDefaultMessage:
          '<i class="icon icon--sicon-add-cirlce"></i>\n' +
          '<p>ファイルを選択</p>',
      }
    );

    $dropzoneUpLoadDetailedMaterials.on('maxfilesexceeded', function (file) {
      $dropzoneUpLoadDetailedMaterials.removeAllFiles();
      $dropzoneUpLoadDetailedMaterials.addFile(file);
    });

    $dropzoneUpLoadDetailedMaterials.on('removedfile', function (file) {
      real_name_information = '';
    });

    $dropzoneUpLoadDetailedMaterials.on('addedfile', function (file, e) {
      if ($dropzoneUpLoadDetailedMaterials.files.length > 1) {
        $dropzoneUpLoadDetailedMaterials.removeAllFiles(true);
        $dropzoneUpLoadDetailedMaterials.addFile(file);
      }

      if (
        $dropzoneUpLoadDetailedMaterials.files &&
        $dropzoneUpLoadDetailedMaterials.files[0] &&
        $dropzoneUpLoadDetailedMaterials.files[0].name.match(
          /\.(pdf|PDF)$/
        )
      ) {
        let file_dom = $(file.previewElement);
        real_name_information = file.name;
        let file_preview = thisForm
          .find('.mattach-preview-container-form-upload-logo')
          .find('.mcommment-file__name-form');
        for (let i = 0; i < file_preview.length; i++) {
          $(file_preview[i]).text('');
          $(file_preview[i]).append(
            '<i class="icon icon--sicon-clip"></i>' + real_name_information
          );
          break;
        }
        file_name = this.files[0].name;
        if (thisForm.find('.account__file').length) {
          thisForm.find('.account__file').hide();
        }
      } else if (!$dropzoneUpLoadDetailedMaterials.files) {
        return false;
      } else {
        alert('PDFのみアップロードできます。');
        $dropzoneUpLoadDetailedMaterials.removeAllFiles(true);
      }
    });

      thisForm.find('.account__file .icon--sicon-close').on('click', function () {
          thisForm.find('.account__file').addClass('hide');
          is_delete_file = true;
      });
  }
}

function uploadAndCropThumbnail(typeImage) {
    let file_name;
    var $image = $('#image-crop');
    let imageDom = $('#id_thumbnail');

    if (typeImage === 'image') {
        imageDom = $('#id_image');
    }

    imageDom.attr({accept: 'image/*'});
    imageDom.on('change', function () {
        cropThumbnail(typeImage);
        if (
            this.files &&
            this.files[0] &&
            this.files[0].name.match(/\.(jpg|jpeg|png|gif|JPG|PNG|JPEG|GIF)$/)
        ) {
            if (this.files[0].size > 1024 * 1024 * 2) {
              alert("2MB以下のファイルのみアップロードできます!");
              this.removeFile(file);
              $(this).val('').clone(true);
            }
            file_name = this.files[0].name;
            let reader = new FileReader();
            reader.onload = function (e) {
                $image.attr('src', e.target.result);
                $('#modalCrop').modal('show');
            };
            reader.readAsDataURL(this.files[0]);
        } else if (this.files.length == 0) {
            return false;
        } else {
            alert(
                '画像をアップロードしてください。アップロードしたファイルは画像でないか、または壊れています。'
            );
            $(this).val('').clone(true);
        }
    });
}


function cropThumbnail(typeImage) {
    var $image = $('#image-crop');
    let dataCrop = {
        viewMode: 1,
        aspectRatio: 16 / 9,
        minCropBoxWidth: 200,
        minCropBoxHeight: 200,
        minContainerHeight: 400,
    };
    if (typeImage === 'image') {
        dataCrop = {
            viewMode: 1,
            aspectRatio: 113 / 160,
            minCropBoxWidth: 200,
            minCropBoxHeight: 200,
            minContainerHeight: 400,
        };
    }
    $('#modalCrop').modal({
        show: false,
        backdrop: 'static',
    });

    var cropBoxData;
    var canvasData;
    $('#modalCrop')
        .off()
        .on('shown.bs.modal', function () {
            $image.cropper({
                viewMode: dataCrop.viewMode,
                aspectRatio: dataCrop.aspectRatio,
                minCropBoxWidth: dataCrop.minCropBoxWidth,
                minCropBoxHeight: dataCrop.minCropBoxHeight,
                minContainerHeight: dataCrop.minContainerHeight,
                ready: function () {
                    $image.cropper('setCanvasData', canvasData);
                    $image.cropper('setCropBoxData', cropBoxData);
                },
            });
            // $(document).keypress(function (e) {
            //   var code = e.which; // recommended to use e.which, it's normalized across browsers
            //   if (code == 13) {
            //     e.preventDefault();
            //     $('.js-crop-and-upload').click();
            //     return false;
            //   }
            // });
            $('#modalCrop')
                .find('.modal-header button')
                .off('click').on('click', function () {
                    imageDom.val('');
                });
        })
        .on('hidden.bs.modal', function () {
            cropBoxData = $image.cropper('getCropBoxData');
            canvasData = $image.cropper('getCanvasData');
            $image.cropper('destroy');
        });

    // Enable zoom in button
    $('.js-zoom-in').off('click').click(function () {
        $image.cropper('zoom', 0.1);
    });

    // Enable zoom out button
    $('.js-zoom-out').off('click').click(function () {
        $image.cropper('zoom', -0.1);
    });

    $('.js-crop-and-upload').off('click').click(function () {
        let data_crop = {
            fillColor: '#fff'
        };
        var image_dom = $('.topic-upload__thumbnail img');
        let inputDom = $('#id_thumbnail')[0];
        if (typeImage === 'image') {
            image_dom = $('.topic-upload__image img');
            inputDom = $('#id_image')[0];
            data_crop = {
                fillColor: '#fff'
            }
        }
        var croppedImageDataURL = $image.cropper('getCroppedCanvas', data_crop).toDataURL("image/png");
        image_dom.attr('src', croppedImageDataURL);

        var img_file = dataURLtoFile(croppedImageDataURL, inputDom.files[0].name);
        let file = new File([img_file], inputDom.files[0].name, {lastModified:new Date().getTime()});

        let container = new DataTransfer();
        container.items.add(file);
        inputDom.files = container.files;

        checkDisaleButtonSubmitForm1();
        $('#modalCrop').modal('hide');
    });
}

function dataURLtoFile(dataUrl, filename) {
    var arr = dataUrl.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);

    while(n--){
        u8arr[n] = bstr.charCodeAt(n);
    }

    return new File([u8arr], filename, {type:mime});
}

function dragableChoice() {
    let startIndex;
    let endIndex;
  $('.list-choice-container')
    .sortable({
      axis: 'y',
      items: '> div, > span',
      handle: 'span.button-move_choice',
      containment: '.list-choice-container',
      tolerance: 'pointer',
      connectWith: '.list-choice-container',
      cancel: '.choice-content',
      helper: 'clone',
      start: function (event, ui) {
        startIndex = ui.item.index();
      },
      stop: function (event, ui) {
        endIndex = ui.item.index();
        // $(ui.item).css({'left': 'unset', 'top': 'unset'});
      },
      create:function(){
        var list=$(this);
        list.css("height","auto");
        list.height($(this).height());
      }
    }).disableSelection();
}

function dragableExhibitionWorks() {
  $('.exhibition-works-list__container')
    .sortable({
      axis: 'x',
      items: '> div, > span',
      handle: 'span.button-move_exhibition-works',
      containment: '.exhibition-works-list__container',
      tolerance: 'pointer',
      connectWith: '.exhibition-works-list__container',
      start: function (event, ui) {},
      stop: function (event, ui) {
        // $(ui.item).css({'left': 'unset', 'top': 'unset'});
      },
    }).disableSelection();
}

function actionCheckBox() {
  $(document).on('click', '.button-delete_radio', function (e) {
    e.preventDefault();
    let listRadio = $('.radio-component').get();
    let thisRadio = $(this).parents('.radio-component').get(0);
    let index = listRadio.indexOf(thisRadio);
    if($(this).parents('.radio-component').find('input[name=content-radio-box]').is(':checked')) {
      if(listRadio.length > 1) {
        if(index === listRadio.length - 1) {
          $(listRadio[0]).find('input[name=content-radio-box]').prop('checked', true);
        } else {
          $(listRadio[index + 1]).find('input[name=content-radio-box]').prop('checked', true);
        }
        $(this).parents('.radio-component').remove();
      }
    } else {
      if(listRadio.length > 1) {
        $(this).parents('.radio-component').remove();
      }
    }
  });

  $('.list-radio__add-action').on('click', function (e) {
    let index = $('.radio-component').get().length;
    let checkedRadio = '';
    if (!index) {
        checkedRadio = 'checked';
    }

    $(`<div class="radio-component">
        <label class="input-radio">
          <input type="radio" name="content-radio-box" index="${index}" data-value="" ${checkedRadio}/>
          <div class="check-mark" style="top: 0;"></div>
        </label>

        <input type="text" class="form-control" id="id_input-checkbox" 
        placeholder='' name="input-checkbox">

        <div class="radio-action">
          <div class="radio-action-button-container">
            <span class="button-move_radio">
              <i class="icon icon--sicon-equal"></i>
            </span>
            <span class="button-delete_radio">
              <i class="icon icon--sicon-trash"></i>
            </span>
          </div>
        </div>
      </div>`).insertBefore($(this));
  });
}

function actionToggleBox() {
  $(document).on('click', '.button-delete_toggle', function (e) {
    e.preventDefault();
    $(this).parents('.toggle-input-container').remove();
    $('.list-toggle-input').sortable("destroy");
    dragableListToggle();
  });

  $('.list-toggle__add-action').on('click', function (e) {
    $(
      '<div class="toggle-input-container"><div class="form-check custom-switch">' +
        '<label class="form-check-label"><div class="form-check-group" style="margin-bottom: 0px;">' +
        '<input class="form-check-input switch-checkbox switch-toggle-account" type="checkbox" name="switch-toggle-account"><span class="switch-slider"></span>' +
        '</div></label></div>' +
        '<input type="text" placeholder="" class="form-control" id="inputbox-toggle" name="inputbox-toggle">' +
        '<div class="toggle-input-action"><div class="toggle-input-button-container">' +
        ' <span class="button-move_toggle"><i class="icon icon--sicon-equal"></i></span> '+
        '<span class="button-delete_toggle"><i class="icon icon--sicon-trash"></i>' +
        '</span></div></div></div>'
    ).insertBefore($(this));

    $('.list-toggle-input').sortable("destroy");
    dragableListToggle();
  });
}

function actionChoice() {
  $('.list-choices__btn-add-choice').on('click', function () {
    $(this).parents('.topic-container__topic-form-1').hide();
    $('.topic-container__topic-form-2').show();
    $('html, body').animate({ scrollTop: 0 }, 500);
  });

  $(document).on('click', '.button-delete_choice', function (e) {
    e.preventDefault();
    var selection_id = $(this).parents('.choice-component').find('.choice-content').attr('data-selection');
    if (selection_id) {
        list_selection_delete.push(selection_id);
    }
    // selections = selections.filter(function(item) {
    //   return (item.id).toString() !== id.toString();
    // })

    $(this).parents('.choice-component').remove();

    $('.list-choice-container').sortable("destroy");
    dragableChoice();
  });

  // submit form 2
  $(document).on('click', '.button-submit-form2',function (e) {
    let value = $(this).text();
      e.preventDefault();
      let container = $('.topic-form-1__choices-container');
      let component = container.find('.choice-content[data-choice-id="'+ $(this).parents('.topic-container__topic-form-2').attr('data-choice-id') +'"]');
      let formContainer = $(this).parents('.topic-container__topic-form-2');
    if($(this).parents('.topic-container__topic-form-2').attr('data-choice-id')) {
        // to edit
        if(checkValidateForm2()) {
          // cleanDataSelection(formContainer, $(this).parents('.topic-container__topic-form-2').attr('data-choice-id'));
          component.find('.choice-title').html(formContainer.find('input[name="choice_title"]').val().trim());
          component.find('.choice-description').html(formContainer.find('textarea[name="explanation"]').val().trim());

          component.find('.choice-list-media__component').remove();
          component.find('.data-radio-container').empty();
          component.find('.data-toggles-container').empty();

          formContainer.find('.exhibition-works-list__container .exhibition-works__component-container').each(function() {
            var style = $(this).find('.gallery__item').attr('style');
            var avatarColor = $(this).find('.exhibition-works__component-action .exhibition-works__component-action-setcolor .avatar-color').attr('style');
            var colorValue = $(this).find('.value-color').text();

            component.find('.choice-list-media').append(`
            <div class="choice-list-media__component" style="${avatarColor}"
            data-thumbnail="${style}" data-sound="${$(this).find('audio').attr('src')}" 
            data-color="${avatarColor}"
            data-type="${$(this).find('.gallery__item').attr('data-type')}" data-artist="${$(this).find('.gallery__item').attr('data-artist')}"
            data-sale="${$(this).attr('data-sale')}" data-name="${$(this).find('audio').attr('data-name')}">
              <div class="choice-list-color__style">${style}</div>
              <div class="choice-list-color__value">${colorValue}</div>
            </div>`);
          });

          formContainer.find('.list-radio .radio-component').each(function() {
            if($(this).find('input[name="input-checkbox"]').val().trim() !== ''){
              component.find('.data-radio-container').append(`<div class="data-radio-component" ${$(this).find('input[type="radio"]').is(':checked') ? 'data-checked="true"' : ''}>
              ${$(this).find('input[name="input-checkbox"]').val().trim()}</div>`);
            }
          });

          formContainer.find('.list-toggle-input .toggle-input-container').each(function() {
            if($(this).find('input[name="inputbox-toggle"]').val().trim() !== ''){
              component.find('.data-toggles-container').append(`<div class="data-toggle-component" ${$(this).find('input[type="checkbox"]').is(':checked') ? 'data-checked="true"' : ''}>
              ${$(this).find('input[name="inputbox-toggle"]').val().trim()}</div>`);
            }
          });
        } else {
          let id = $(this).parents('.topic-container__topic-form-2').attr('data-choice-id');
          selections = selections.filter(function(item) {
            return (item.id).toString() !== id.toString();
          });

          $(`.choice-component .choice-content[data-choice-id="${id}"]`).parents('.choice-component').remove();

        }

        $(this).parents('.topic-container__topic-form-2').removeAttr('data-choice-id');
    } else {
        // add new
        if(checkValidateForm2()){
          let index = $('.list-choice-container .choice-component').get().length + 1;
          let selectionTitle = formContainer.find('input[name="choice_title"]').val().trim();
          let selectionExplanation = formContainer.find('textarea[name="explanation"]').val().trim();

          // cleanDataSelection(formContainer, index);

          $(`<div class="choice-component">
          <div class="choice-content" data-choice-id="${index}">
              <div class="choice-title">${selectionTitle}</div>
              <div class="choice-description">${selectionExplanation}</div>
              <div class="choice-list-media"></div>
                <div class="data-container data-radio-container"></div>
                <div class="data-container data-toggles-container"></div>
              </div>
              <div class="choice-action">
                  <div class="choice-action__button-container">
                      <span class="button-move_choice">
                          <i class="icon icon--sicon-equal"></i>
                      </span>
                      <span class="button-delete_choice">
                          <i class="icon icon--sicon-trash"></i>
                      </span>
                  </div>
              </div>
          </div>`).insertBefore('.list-choices__btn-add-choice');

          component = $(document).find('.choice-content[data-choice-id="'+ index +'"]');

          formContainer.find('.exhibition-works-list__container .exhibition-works__component-container').each(function() {
            var style = $(this).find('.gallery__item').attr('style');
            var avatarColor = $(this).find('.exhibition-works__component-action .exhibition-works__component-action-setcolor .avatar-color').attr('style');
            var colorValue = $(this).find('.value-color').text();

            component.find('.choice-list-media').append(`

                <div class="choice-list-media__component" style="${avatarColor}"
                data-thumbnail="${style}" data-sound="${$(this).find('audio').attr('src')}"
                data-color="${avatarColor}"
                data-type="${$(this).find('.gallery__item').attr('data-type')}" data-artist="${$(this).find('.gallery__item').attr('data-artist')}"
                data-sale="${$(this).attr('data-sale')}" data-name="${$(this).find('audio').attr('data-name')}">
                                <div class="choice-list-color__style">${style}</div>
                <div class="choice-list-color__value">${colorValue}</div>


                </div>`);
          });

          formContainer.find('.list-radio .radio-component').each(function() {
              if($(this).find('input[name="input-checkbox"]').val().trim()!==''){
                  component.find('.data-radio-container').append(`<div class="data-radio-component" ${$(this).find('input[type="radio"]').is(':checked') ? 'data-checked="true"' : ''}>
                  ${$(this).find('input[name="input-checkbox"]').val().trim()}</div>`);
              }
          });

          formContainer.find('.list-toggle-input .toggle-input-container').each(function() {
              if($(this).find('input[name="inputbox-toggle"]').val().trim()){
                  component.find('.data-toggles-container').append(`<div class="data-toggle-component" ${$(this).find('input[type="checkbox"]').is(':checked') ? 'data-checked="true"' : ''}>
                  ${$(this).find('input[name="inputbox-toggle"]').val().trim()}</div>`);
              }
          });
        }
    }

    clearForm2();

    $(this).parents('.topic-container__topic-form-2').hide();
    $('.topic-container__topic-form-1').show();
    setTimeout(function () {
      $('.list-choices__btn-add-choice')
        .get(0)
        .scrollIntoView({ behavior: 'smooth' });
    }, 50);
    $('.list-choice-container').sortable( "destroy" );
    dragableChoice();
  });

  // remove selection
  $(document).on('click', '.button-delete_exhibition-works',function(e) {
      e.preventDefault();
      $(this).parents('.exhibition-works__component-container').remove();
  });

  // click to edit selection
  $(document).on('click', '.choice-content',function(e) {
    $('html, body').animate({ scrollTop: 0 }, 500);
    $('.topic-container__topic-form-2').attr('data-choice-id', $(this).attr('data-choice-id'));
    $('.topic-container__topic-form-2 input[name="choice_title"]').val($(this).find('.choice-title').text().trim());
    $('.topic-container__topic-form-2 textarea[name="explanation"]').val($(this).find('.choice-description').text().trim());
    if(!!$('.exhibition-works-list__container .exhibition-works__component-container').get().length){
        $('.exhibition-works-list__container .exhibition-works__component-container').remove();
    }
    $(this).find('.choice-list-media .choice-list-media__component').each(function(){
      let mediaDom = $(this);
      let style = mediaDom.attr('data-thumbnail');
      let colorStyle = mediaDom.attr('data-color');
      let previewAlbum = mediaDom.attr('data-file-type') === 'audio' ? '' : 'btn-preview-album'
      let setColorValue = $(this).find('.choice-list-color__value').text();
        $('<div class="exhibition-works__component-container" data-sale="'+ mediaDom.attr('data-sale') +'">'+
        `<div class="exhibition-works__component-content gallery__item" data-type="`+ mediaDom.attr('data-type') +`" data-artist="`+ mediaDom.attr('data-artist') +`"`+
        `style="${style}">`+
            `<div class="list-search__item-playpause ${previewAlbum}"></div>`+
            `<audio preload="none" src="`+ $(this).attr('data-sound') +`" class="gallery__item-banner" data-file-type="${mediaDom.attr('data-file-type')}" data-name="`+ mediaDom.attr('data-name') +`"></audio></div>`+
          `<div class="exhibition-works__component-action">` +
          `<div class="exhibition-works__component-action-setcolor">` +
          `<div class="avatar-color" style="${colorStyle}"></div>` +
          `<div class="caption--11 value-color">${setColorValue}</div>` +
          `</div>` +
          `<div class="exhibition-works__component-action-container">`+
            `<span class="button-move_exhibition-works"><i class="icon icon--sicon-equal"></i></span>`+
            `<span class="button-delete_exhibition-works"><i class="icon icon--sicon-trash"></i></span>`+
          `</div></div></div></div>`).insertBefore('.exhibition-works__button-add');
    });

    if(!!$('.list-radio .radio-component').get().length){
        $('.list-radio .radio-component').remove();
    }

    $(this).find('.data-radio-container .data-radio-component').each(function(index){
        $(`<div class="radio-component"><label class="input-radio">`+
        `<input type="radio" name="content-radio-box" index="${index}" data-value="" ${$(this).attr('data-checked') === "true" ? "checked": ""}/>`+
        `<div class="check-mark" style="top: 0;"></div></label>`+
        `<input type="text" class="form-control" id="id_input-checkbox" placeholder="" name="input-checkbox"`+
        `value="${$(this).text().trim()}">`+
        `<div class="radio-action"><div class="radio-action-button-container">
        <span class="button-move_radio">
                        <i class="icon icon--sicon-equal"></i>
                      </span><span class="button-delete_radio">`+
        `<i class="icon icon--sicon-trash"></i></span></div></div></div>`).insertBefore('.list-radio__add-action');
    });


    if(!!$('.list-toggle-input .toggle-input-container').get().length){
        $('.list-toggle-input .toggle-input-container').each(function(){
            $(this).remove();
        });
    }

    $(this).find('.data-toggles-container .data-toggle-component').each(function(index){
        $(`<div class="toggle-input-container"><div class="form-check custom-switch"><label class="form-check-label">
            <div class="form-check-group" style="margin-bottom: 0px;">
            <input ${$(this).attr('data-checked') === "true" ? "checked": ""} class="form-check-input switch-checkbox switch-toggle-account" 
            type="checkbox" name="switch-toggle-account"><span class="switch-slider"></span>
            </div></label></div>
        <input type="text" placeholder="" value="${$(this).text().trim()}" class="form-control" id="inputbox-toggle" name="inputbox-toggle">
        <div class="toggle-input-action"><div class="toggle-input-button-container">
        <span class="button-move_toggle">
                        <i class="icon icon--sicon-equal"></i>
                      </span>
        <span class="button-delete_toggle"><i class="icon icon--sicon-trash"></i></span>
        </div></div></div>`).insertBefore('.list-toggle__add-action');
    });

    $(this).parents('.topic-container__topic-form-1').hide();
    $('.topic-container__topic-form-2').show();
  });

  dragableListRadio();
  dragableListToggle();
}

function checkValidateForm2 (){
  let title = $('.topic-container__topic-form-2 input[name="choice_title"]').val().trim();
  let description = $('.topic-container__topic-form-2 textarea[name="explanation"]').val().trim();
  let inputRadio = $('.topic-container__topic-form-2 input[name="input-checkbox"]').get();
  let inputToggle = $('.topic-container__topic-form-2 input[name="inputbox-toggle"]').get();
  let listMedia = $('.topic-container__topic-form-2 .exhibition-works__component-content').get();
  let checkRadio = false;
  let checkToggle = false;

  inputRadio.forEach(item => {
    if($(item).val().trim() !== '') {
      checkRadio = true;
    }
  });

  inputToggle.forEach(item => {
    if($(item).val().trim() !== '') {
      checkToggle = true;
    }
  });
    if(title === '' && description === '' && !checkRadio && !checkToggle && listMedia.length === 0) {
      return false;
    } else {
      return true;
    }
}

function cleanDataSelection(formContainer, index) {
    let selectionNewDataFormat = {
        'id': '',
        'title': '',
        'description': '',
        'sale_id': [],
        'selectors_checkbox': [{
            'detail': '',
            'status': ''
        }],
        'toggles': [{
            'detail': '',
            'status': ''
        }],
        'order': ''
    };
    let hasSelection = false;

    for (i = 0; i < selections.length; i++) {
        if (selections[i]['id'] == parseInt(index)) {
            selectionNewDataFormat = selections[i];
            hasSelection = true;
        }
    }
    let selectionTitle = formContainer.find('input[name="choice_title"]').val().trim();
    let selectionExplanation = formContainer.find('textarea[name="explanation"]').val().trim();
    selectionNewDataFormat['title'] = selectionTitle;
    selectionNewDataFormat['description'] = selectionExplanation;
    selectionNewDataFormat['id'] = index;
    let listSaleId = [];
    $('.topic-container__topic-form-2 .exhibition-works__component-container').each(function () {
        let sale_id = $(this).attr('data-sale');
        if (sale_id) {
            listSaleId.push(sale_id);
        }
    });

    selectionNewDataFormat['sale_id'] = listSaleId;
    let toggles = [];
    $('.list-toggle-input .toggle-input-container').each(function () {
        let detail = $(this).find('input[name="inputbox-toggle"]').val().trim();
        if (detail === '') {
            return;
        }
        let status = $(this).find('.switch-checkbox').is(':checked') ? 'on' : 'off';
        let toggle = {
            'detail': detail,
            'status': status
        };
        toggles.push(toggle)
    });

    selectionNewDataFormat['toggles'] = toggles;

    let selectors_checkbox = [];

    $('.list-radio .radio-component').each(function () {
        let detail = $(this).find('input[name="input-checkbox"]').val().trim();
        if (detail === '') {
            return;
        }
        let status = $(this).find('input[name="content-radio-box"]').is(':checked') ? 'on' : 'off';
        let selector_checkbox = {
            'detail': detail,
            'status': status
        };
        selectors_checkbox.push(selector_checkbox)
    });

    selectionNewDataFormat['selectors_checkbox'] = selectors_checkbox;
    if (!hasSelection) {
            selections.push(selectionNewDataFormat);

    }
}

function dragableListRadio() {
  $('.list-radio').sortable({  axis: 'y',
    items: '> div, > span',
    handle: 'span.button-move_radio',
    containment: '.list-radio',
    tolerance: 'pointer',
    connectWith: '.list-radio',
    start: function (event, ui) {
      startIndex = ui.item.index();
    },
    stop: function (event, ui) {
      endIndex = ui.item.index();
      // $(ui.item).css({'left': 'unset', 'top': 'unset'});
    },
  }).disableSelection();
}

function dragableListToggle() {
  $('.list-toggle-input').sortable({  axis: 'y',
    items: '> div, > span',
    handle: 'span.button-move_toggle',
    containment: '.list-toggle-input',
    tolerance: 'pointer',
    connectWith: '.list-toggle-input',
    helper: 'clone',
    start: function (event, ui) {
      startIndex = ui.item.index();
    },
    stop: function (event, ui) {
      endIndex = ui.item.index();
      // $(ui.item).css({'left': 'unset', 'top': 'unset'});
    },
    create:function(){
      var list=$(this);
      list.css("height","auto");
      list.height($(this).height());
    }
  }).disableSelection();
}

function actionExhibitionWork() {
    $(document).on('click', '.exhibition-works__button-add', function () {
    // $(this).parents('.topic-container__topic-form-2').hide();
    // $('.topic-container__topic-form-3').show();
    // $('html, body').animate({ scrollTop: 0 }, 500);

    $('#modal-add-sub-work').modal('show');
    $('#modal-add-sub-work').find('.modal-content').addClass('for-create-topic');
  });

    $(document).on('input', '#new-work__add-work', function (e) {
      if (e.target.value.trim() !== '') {
          $(this).parents('.sform-group__append-before').find('.search-delete').css({"display": "block"});
      }
    });

    $('.search-delete').on('click', function () {
      resetModalSearchSale();
      $(this).css({"display": "none"});
    });

    $(document).on('hidden.bs.modal', '#modal-add-sub-work', function () {
        $('#modal-add-sub-work').attr('data-list-id', '');
        resetModalSearchSale();
    });

    function resetModalSearchSale() {
        $('#new-work__add-work').val('');
        $('#new-work__add-work').attr('disabled', false);
        $(this).css({"display": "none"});
        $('#modal-add-sub-work .list-search-work.mscrollbar').empty();
        $('#modal-add-sub-work .btn-popup-send').addClass('disabled');
    }

    // search artist
    $(document).on('keypress', '#new-work__add-work', function (e) {
        if (e.which === 13) {
            let listSaleId = [];
            $('.exhibition-works-list__container .exhibition-works__component-container').each(function () {
                let sale_id = $(this).attr('data-sale');
                if (sale_id) {
                    listSaleId.push(sale_id)
                }
            });
            let keyWord = $(this).val().trim();

            ajaxGetSaleContentToAddSelection(listSaleId, keyWord)
        }
    });

    function ajaxGetSaleContentToAddSelection(listSaleId, keyWord) {
        let data = {
            'list_sale_id': listSaleId,
            'key_word': keyWord
        };
        if (artist_id !== '') {
            data['artist_id'] = artist_id
        }
        $.ajax({
            method: 'POST',
            url: '/gallery/get_sale_content_to_add_selection',
            data: data,
            success: function (data) {
                $('#modal-add-sub-work .list-search-work.mscrollbar').empty();
                $('#modal-add-sub-work .list-search-work.mscrollbar').prepend(data.html);
                // dragableNewWorks()
            },
            error: function () {
                toastr.error('エラーが発生しました')
            },
            complete: function () {
                $('#modal-add-sub-work .btn-popup-send').addClass('disabled')
            }
        })
    };

    // action button add sale content

    $(document).on('change', '.check-to-add-into-list', function () {
        if ($('input.check-to-add-into-list:checked').length) {
            $('#modal-add-sub-work .btn-popup-send').removeClass('disabled');
            $('#new-work__add-work').attr('disabled', true);
        } else {
            $('#modal-add-sub-work .btn-popup-send').addClass('disabled');
            $('#new-work__add-work').attr('disabled', false);
        }
    });

    // ajax add sale content

    $(document).on('click', '#modal-add-sub-work .btn-popup-send', function () {
        let buttonDom = $(this);
        if (buttonDom.hasClass('disabled')) {
            return
        }
        buttonDom.addClass('disabled');
        let listId = $('#modal-add-sub-work').attr('data-list-id');
        let arr_order = [];

        $('#modal-add-sub-work .list-new-works__item-container').each(function (index, item) {
            if ($(this).find('.check-to-add-into-list').is(':checked')) {
                arr_order.push(item.getAttribute('data-sale-content-id'));
            }
        });
        ajaxAddSaleContentToSelection(arr_order, buttonDom)
    });

    function ajaxAddSaleContentToSelection(arr, buttonDom) {
        $.ajax({
            method: 'POST',
            url: '/gallery/add_sale_content_into_selection',
            data: {
                'arr': arr
            },
            success: function (data) {
                $('.topic-container__topic-form-2 .exhibition-works-list__container.mscrollbar').prepend(data.html);
            },
            error: function () {
                toastr.error('エラーが発生しました')
            },
            complete: function () {
                $('#modal-add-sub-work').modal('hide');
                buttonDom.removeClass('disabled');
            }
        })
    }

  $('.button-submit-form3').on('click', function () {
    $(this).parents('.topic-container__topic-form-3').hide();
    $('.topic-container__topic-form-2').show();
    setTimeout(function () {
      $('.exhibition-works-list__container').get(0).scrollIntoView({ behavior: 'smooth', block: 'end' });
    }, 50);

    $('.topic-container__topic-form-3 .topic-form-3__upload-file-work').find('.mcommment-file').addClass('hide');
    $('input[name="work_title"]').val('');
    $('input[name="artist_name"]').val('');
    $('input[name="artist_name"]').val('');
    $('input[name="content-work"]').each(function() {
        if($(this).prop('checked').length) {
            $this.prop('checked', false);
        }
    });
    $('input[name="content-work"][index="0"]').prop('checked', true);
  });

  // Event set color
    $(document).on('click', '.exhibition-works__component-action-setcolor .value-color', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let item_id = $(this).parents('.exhibition-works__component-container').attr('data-sale');
        $('#modal-setcolor').attr('data-sale', item_id);
        $('#modal-setcolor').find('#id-input-color').val($(this).text());
        $('#modal-setcolor').modal('show');
    });

    var reg = /^[0-9A-F]{6}$/i;
    $(document).on('input', '#id-input-color', function () {
        let colorDom = this.value.trim();
        if (this.value.length > 6) {
            this.value = this.value.slice(0, 6);
        }
        if (reg.test(colorDom)) {
            $('#submit--setcolor').removeClass('disabled');
        } else {
            $('#submit--setcolor').addClass('disabled');
        }
    });

    $(document).on('click', '#submit--setcolor', function (e) {
        let item_id = $('#modal-setcolor').attr('data-sale');
        var color = $(this);
        let value = color.text();
        setColorAlbum(value, item_id);
    })
}

function checkDisaleButtonSubmitForm1() {
    let thumbnail_length = $('input[name="thumbnail"]')[0].files.length;
    let vertical_thumbnail_length = $('input#id_image[name="image"]')[0].files.length;
    let topic = $('input[name="title"]').val().trim();
    let no_vert_thumb = vertical_thumbnail_length == 0 && $('.topic-upload__image').attr('data-src') !== 'true'
    let no_thumb = thumbnail_length == 0 && $('.topic-upload__thumbnail').attr('data-src') !== 'true'
    if (no_thumb || topic === '' || no_vert_thumb) {
        $('.button-submit-form1').addClass('disabled');
    } else {
        $('.button-submit-form1').removeClass('disabled');
    }
}

function hashTag() {
  let value = $('input[name="hashtag"]').val();
  $('input[name="hashtag"]').on('focus', function(e) {
    if(value === '') {
      $('input[name="hashtag"]').val('')
      $('input[name="hashtag"]').val('#')
    }
  });

  $('input[name="hashtag"]').on('input', function(e) {
    if($('input[name="hashtag"]').val().trim()[0] !== '#') {
      $('input[name="hashtag"]').val('#' + $('input[name="hashtag"]').val().trim());
    }
  });

  $('input[name="hashtag"]').on('keyup', function(e) {
    if(e.which === 32) {
      $('input[name="hashtag"]').val($('input[name="hashtag"]').val()+ '#');
      let valueInputLength = $('input[name="hashtag"]').val().trim().length;
      $('input[name="hashtag"]').get(0).setSelectionRange(valueInputLength, valueInputLength);
    }
  })
}

function validateFrom1() {
  $('input[name="title"]').on('input', function(){
    checkDisaleButtonSubmitForm1();
  });
}

function checkValidateFrom1() {
    $('.error-border').removeClass('error-border');
    $('.errorlist').remove();
    let countError = 0;
    if (!topic_id && !$('#id_thumbnail')[0].files.length) {
        countError++;
    }
    if ($('#form-create_topic #id_title').val().trim() === '') {
        let titleDom = $(this);
        countError++;
        titleDom.focus();
        titleDom.addClass('error-border');
        $('<ul class="errorlist">' +
            '<li>このフィールドは必須です。</li>' +
            '</ul>').insertAfter(titleDom);
    }
    return countError < 1 ? true : false
}

function appendDataIntoSelections(formContainer) {
    let selectionNewDataFormat = {
        'id': '',
        'title': '',
        'description': '',
        'sale_arr': [{
            'sale_id': '',
            'default_thumbnail': '',
            'order': ''
        }],
        'selectors_checkbox': [{
            'detail': '',
            'status': ''
        }],
        'toggles': [{
            'detail': '',
            'status': ''
        }],
        'order': ''
    };

    let selectionTitle = formContainer.find('.choice-title').text();
    let selectionExplanation = formContainer.find('.choice-description').text();
    let selectionId = formContainer.find('.choice-content').attr('data-selection');
    let selectionOrder = formContainer.index();
    selectionNewDataFormat['title'] = selectionTitle;
    selectionNewDataFormat['description'] = selectionExplanation;
    selectionNewDataFormat['id'] = selectionId;
    selectionNewDataFormat['order'] = selectionOrder;

    let listSale = [];
    formContainer.find('.choice-list-media .choice-list-media__component').each(function () {
        let sale_id = $(this).attr('data-sale');
        if (sale_id) {
            let default_thumbnail = $(this).find('.choice-list-color__value').text().trim() ? $(this).find('.choice-list-color__value').text() : 'C4C4C4';
            let sale = {
                'sale_id': sale_id,
                'default_thumbnail': default_thumbnail,
                'order': $(this).index()
            };
            listSale.push(sale);
        }
    });

    selectionNewDataFormat['sale_arr'] = listSale;

    let selectors_checkbox = [];

    formContainer.find('.data-container.data-radio-container .data-radio-component').each(function () {
        let detail = $(this).text().trim();
        if (detail === '') {
            return;
        }
        let status = $(this).attr('data-checked') ? 'on' : 'off';
        let selector_checkbox = {
            'detail': detail,
            'status': status
        };
        selectors_checkbox.push(selector_checkbox)
    });
    selectionNewDataFormat['selectors_checkbox'] = selectors_checkbox;
    let toggles = [];

    formContainer.find('.data-container.data-toggles-container .data-toggle-component').each(function () {
        let detail = $(this).text().trim();
        if (detail === '') {
            return;
        }
        let status = $(this).attr('data-checked') ? 'on' : 'off';

        let toggle = {
            'detail': detail,
            'status': status
        };
        toggles.push(toggle)
    });
    selectionNewDataFormat['toggles'] = toggles;

    selections.push(selectionNewDataFormat);
}

function cleanDataSelections() {
    selections = [];

    $('.list-choice-container .choice-component').each(function () {
        appendDataIntoSelections($(this))
    })
}

function submitForm1() {
    // TODO: submit form here
    $('.button-submit-form1').on('click', function (e) {
        e.preventDefault();
        let buttonDom = $(this);
        cleanDataSelections();
        if (!buttonDom.hasClass('disabled') && checkValidateFrom1()) {
            let form = $('#form-create_topic');
            let dataForm = new FormData(form[0]);
            dataForm.append('thumbnail', $('#id_thumbnail')[0].files[0]);
            if ($('#id_thumbnail')[0].files.length) {
                dataForm.append('thumbnail_real_name', $('#id_thumbnail')[0].files[0].name);
            }
            if ($('#id_image')[0].files.length) {
                dataForm.append('image', $('#id_image')[0].files[0]);
                dataForm.append('image_real_name', $('#id_image')[0].files[0].name);
            }
            if ($dropzoneUploadVideoTopic.files.length && $dropzoneUploadVideoTopic.files[0]) {
                dataForm.append('video', $dropzoneUploadVideoTopic.files[0]);
                dataForm.append('video_real_name', $dropzoneUploadVideoTopic.files[0].name);
            }
            if ($dropzoneUpLoadDetailedMaterials.files.length && $dropzoneUpLoadDetailedMaterials.files[0]) {
                dataForm.append('file', $dropzoneUpLoadDetailedMaterials.files[0]);
                dataForm.append('file_real_name', $dropzoneUpLoadDetailedMaterials.files[0].name);
            }
            if (selections.length) {
                dataForm.append('selections', JSON.stringify(selections));
            }
            buttonDom.addClass('disabled');
            if (artist_id) {
                dataForm.append('artist_id', artist_id);
            }
            if (topic_id) {
                dataForm.append('topic_id', topic_id);
                dataForm.append('is_delete_file', is_delete_file);
                dataForm.append('is_delete_video', is_delete_video);
                dataForm.append('list_selection_delete', list_selection_delete);
                ajaxUpdateTopic(dataForm, buttonDom)
            } else {
                ajaxCreateTopic(dataForm, buttonDom)
            }
        }
    })
}

function ajaxCreateTopic(dataForm, buttonDom) {
    $.ajax({
        type: "POST",
        url: '/gallery/create_topic_form_create',
        datatype: "json",
        data: dataForm,
        cache: false,
        processData: false,
        contentType: false,
        success: function (data) {
            window.location.href = data.url_success;
        },
        error: function (response) {
            buttonDom.removeClass('disabled')
        },
        complete: function (data) {
        }
    })
}

function ajaxUpdateTopic(dataForm, buttonDom) {
    $.ajax({
        type: "POST",
        url: '/gallery/ajax_update_topic_gallery',
        datatype: "json",
        data: dataForm,
        cache: false,
        processData: false,
        contentType: false,
        success: function (data) {
            window.location.href = data.url_success
        },
        error: function (response) {
            buttonDom.removeClass('disabled')
        },
        complete: function (data) {
        }
    })
}

function uploadFileTopicS3(file_dom) {
    file_dom.find('.determinate').css('width', '0%');
    let countProgress = 0;
    var file_loading = setInterval(function () {
        countProgress += 5;
        let percentComplete = countProgress + '%';
        file_dom.find('.determinate').css('transition', '0');
        file_dom.find('.determinate').css('transition', '1s');
        file_dom.find('.determinate').css('width', percentComplete);
        if (countProgress == 100) {
            clearInterval(file_loading);
        }
    }, 10);
}

function cancelForm2() {
  $('.button-cancel-form2').on('click', function(e) {
    e.preventDefault();
    let container = $('.topic-form-1__choices-container');
    let component = container.find('.choice-content[data-choice-id="'+ $(this).parents('.topic-container__topic-form-2').attr('data-choice-id') +'"]');
    let formContainer = $(this).parents('.topic-container__topic-form-2');
    clearForm2();

    $(this).parents('.topic-container__topic-form-2').hide();
    $('.topic-container__topic-form-1').show();
    setTimeout(function () {
      $('.list-choices__btn-add-choice')
        .get(0)
        .scrollIntoView({ behavior: 'smooth' });
    }, 50);
  })
}

function clearForm2() {
  $('.topic-container__topic-form-2 input[name="choice_title"]').val('');
    $('.topic-container__topic-form-2 textarea[name="explanation"]').val('');
    $('.exhibition-works-list__container .exhibition-works__component-container').remove();
    $('.list-radio .radio-component').remove();
    $('.list-toggle-input .toggle-input-container').remove();

    $(`<div class="radio-component">
    <label class="input-radio">
      <input type="radio" name="content-radio-box" index="0" data-value="" checked/>
      <div class="check-mark" style="top: 0;"></div>
    </label>
    <input type="text" class="form-control" id="id_input-checkbox" 
    placeholder='この作品のアーティストに新規制作を依頼する' name="input-checkbox">
    <div class="radio-action">
      <div class="radio-action-button-container">
        <span class="button-move_radio">
          <i class="icon icon--sicon-equal"></i>
        </span>
        <span class="button-delete_radio">
          <i class="icon icon--sicon-trash"></i>
        </span>
      </div>
    </div>
  </div>
  
  <div class="radio-component">
    <label class="input-radio">
      <input type="radio" name="content-radio-box" index="1" data-value=""/>
      <div class="check-mark" style="top: 0;"></div>
    </label>
    <input type="text" class="form-control" id="id_input-checkbox" 
    placeholder='この項目は依頼しない' name="input-checkbox">
    <div class="radio-action">
      <div class="radio-action-button-container">
        <span class="button-move_radio">
          <i class="icon icon--sicon-equal"></i>
        </span>
        <span class="button-delete_radio">
          <i class="icon icon--sicon-trash"></i>
        </span>
      </div>
    </div>
  </div>`).insertBefore('.list-radio__add-action');

  $(`<div class="toggle-input-container">
  <div class="form-check custom-switch">
    <label class="form-check-label">
      <div class="form-check-group" style="margin-bottom: 0px;">
        <input class="form-check-input switch-checkbox switch-toggle-account" type="checkbox" name="switch-toggle-account"><span class="switch-slider"></span>
      </div>
    </label>
  </div>
  <input type="text" placeholder='英語翻訳も依頼する' class="form-control" id="inputbox-toggle" name="inputbox-toggle">
  <div class="toggle-input-action">
    <div class="toggle-input-button-container">
      <span class="button-move_toggle">
                        <i class="icon icon--sicon-equal"></i>
                      </span>
      <span class="button-delete_toggle">
        <i class="icon icon--sicon-trash"></i>
      </span>
    </div>
    </div>
  </div>`).insertBefore('.list-toggle__add-action');
}

function actionFormCreateTopic() {
    actionChoice();
    playAudioTopicDetail();
    actionVideoTopic();
    dragDropVideoTopic();
    dragDropDetailedMaterials();
    uploadAndCropThumbnail('image');
    uploadAndCropThumbnail('thumbnail');
    dragableChoice();
    checkDisaleButtonSubmitForm1();
    validateFrom1();
    submitForm1();
    hashTag();
    cancelForm2();
    // validateForm2();

    actionExhibitionWork();
    dragDropWork();
    playExhibitionWorks();
    dragableExhibitionWorks();
    actionCheckBox();
    actionToggleBox();
}
function resetFormCreateTopic() {
    $(document).on('click', '.topic-form-1__footer-container .btn.btn--tertiary', function () {
        $('input, textarea').val('');
        $dropzoneUpLoadDetailedMaterials.removeAllFiles();
        $dropzoneUploadVideoTopic.removeAllFiles();
        $('#id_thumbnail').val('');
        $('#id_image').val('');
        $('.topic-upload__thumbnail img').attr('src', '#');
        $('.topic-form-1__choices-container .list-choice-container .choice-component').remove();
        selections = [];
        $('#button-submit-form1').addClass('disabled')
    })
}

function setColorAlbum(old_color, item_id) {
    let input_text = $('#modal-setcolor').find('#id-input-color').val();
    var regHex = /^([0-9a-f]{3}){1,2}$/i;
    input_text = input_text.trim();
    $('.errorlist').remove();
    $('#id-input-color').removeClass('error-border');

    if (input_text === old_color) {
        $('#modal-setcolor').modal('hide');
        return
    }
    if (regHex.test(input_text)) {
        let colorDom = $('.exhibition-works__component-container[data-sale^=' + item_id + ']');
        $('#modal-setcolor').modal('hide');
        colorDom.find('.value-color').text(input_text);
        colorDom.find('.avatar-color').css('background-color', '#' + input_text);
    } else {
        $('#id-input-color').addClass('error-border');
        $(`<ul class="errorlist"><li>この項目は必須です。</li></ul>`).insertAfter('#id-input-color');
    }
    return false;
}

var topic_id = '';

$(document).ready(function () {
    
    const url_string = window.location.href;
    var url = new URL(url_string);
    topic_id = url.searchParams.get('topic_id');
    if (topic_id) {
        $('.button-submit-form1').removeClass('disabled')
    }
    actionFormCreateTopic();
    resetFormCreateTopic();
    showAlbumPreview();
    artist_id = $('#form-create_topic').attr('data-artist-id') ? $('#form-create_topic').attr('data-artist-id') : '';
    removeOverLayModal();
});

function actionVideoTopic() {
  $(document).on('mouseenter mouseleave', '.list-topics__content-top, .topic-content__media', function (e) {
    if($('.audio-navi').is('.showing')) {
      return
    }
    if ($(this).find('video').length) {
        if (e.type === 'mouseenter') {
          let current_hover_id = hovering_topic_id;
          let target = $(this)
          hovering_topic = true
          setTimeout(() => {
            if(!hovering_topic || current_hover_id != hovering_topic_id || $('.audio-navi.showing').length) {
                return
            }
            if(!hover_topic_zooming) {
                let video = target.find('video').get(0);
                if(target.is('.topic-content__media')) {
                    $(video).prop('height', $(video).height());
                }
                pauseAll();
                video.play();
                video.removeAttribute('controls');
                video.muted = false;

                hovering_topic = true;
                close_full_screen_topic = false;
            }
          }, 200)
        } else {
            if(!hover_topic_zooming && !close_full_screen_topic) {
                let video = $(this).find('video').get(0);
                video.pause();
                hovering_topic = false;
                hovering_topic_id++;
                $(this).css({'position': 'relative', 'top': 'auto', 'left': 'auto'})
                $(this).removeClass('hover-zoom-netflix hover-zoom-netflix-center hover-zoom-netflix-prepare')
                $(this).find('.list-new-works__content_hover').addClass('hide');
            }
        }
    }
  });

  $(document).on('click', '.topic-netflix-overlay', function() {
      $('.hover-zoom-netflix').removeClass('hover-zoom-netflix-center');
      $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare')

      setTimeout(() => {
          $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-2')
          $('.hover-zoom-netflix').removeClass('hover-zoom-netflix');
          $('.hover-zoom-netflix-prepare').find('.list-new-works__content_hover').addClass('hide');
          $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-3')
          $('.hover-zoom-netflix-prepare').removeClass('hover-zoom-out-netflix-prepare');
      }, 200)

      setTimeout(() => {
          $('.hover-zoom-out-netflix-prepare-2').css({'position': 'relative', 'top': 'auto', 'left': 'auto'})
          $('.hover-zoom-out-netflix-prepare-2').removeClass('hover-zoom-netflix-prepare hover-zoom-out-netflix-prepare hover-zoom-out-netflix-prepare-2 hover-zoom-out-netflix-prepare-3');
      }, 800)

      hover_topic_zooming = false;
      hovering_topic_id++;
      hovering_topic = false;
      close_full_screen_topic = true;
      $('body .topic-netflix-overlay').remove();
  })

  $(document).on('click', '.list-topics__content-top video, .topic-content__media video', function() {
    this.play()
    close_full_screen_topic = true;
    hovering_topic_id++;
    if (this.requestFullscreen) {
      this.requestFullscreen();
    } else if (this.webkitRequestFullscreen) { /* Safari */
      this.webkitRequestFullscreen();
    } else if (this.msRequestFullscreen) { /* IE11 */
      this.msRequestFullscreen();
    }
  });

  document.addEventListener("fullscreenchange", function() {
    if (!document.fullscreenElement) {
        $('.video-netflix-overlay:not(.topic-netflix-overlay)').trigger('click');
        $('.topic-netflix-overlay').trigger('click');
        $('.list-topics__content-bottom video, .section-content__list-media video').css('display', 'none')
    }
  });
}
