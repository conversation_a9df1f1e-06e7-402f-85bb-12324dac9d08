$(document).ready(function () {
    // contactArtist();
    createLinkForAddMessage();
    validateBudget();
    confirmStep();
    initCursorSaleTitle()
});

function initCursorSaleTitle() {
    $(document).on('mouseenter mouseleave', '.sample-audio-title', function (e) {
        if (is_logged_in !== 'True' || user_role === 'master_client') {
            let itemSaleContent = $(this).parents('.sample-audio-item');
            let saleInfo = $(this);
            let sale_type = saleInfo.attr('data-sale-type');
            let saleText = itemSaleContent.find('.sample-audio-sale-type').text();
            if (sale_type === '4' && saleText === '') {
                $(this).css('cursor', 'auto');
            }
        }
    })
}


function contactArtist() {

    $(document).on('click', '.list-new-works__content, .sample-audio-container .sample-audio-info', function (e) {
        e.preventDefault();
        let page = $(this).parents('.sample-audio-item').length ? 'profile' : 'gallery';
        let itemSaleContent, cloned, itemSaleContentCloned, description, credit, created_year, album_title, artist_name,
            sale_type, modalNewWork, saleItemModal;
        if (page === 'profile') {
            itemSaleContent = $(this).parents('.sample-audio-item');
            let saleInfo = $(this).find('.sample-audio-title');
            sale_type = saleInfo.attr('data-sale-type');
            let saleText = itemSaleContent.find('.sample-audio-sale-type').text();
            if (sale_type === '4' && saleText === '') {
                return
            }
        } else {
            itemSaleContent = $(this).parents('.list-new-works__item-container');
            sale_type = itemSaleContent.find('.list-new-works__content').attr('data-sale-type');
            let saleText = itemSaleContent.find('.list-new-works__hint').text();
            if ((sale_type === '4' && saleText === '') || (is_logged_in === 'True' && user_role.includes('curator', 'admin', 'master_admin'))) {
                let newUrl = itemSaleContent.find('.list-new-works__sub-title').attr('data-url');
                window.open(newUrl, '_blank');
                return
            }
        }

        if (is_logged_in !== 'True' || user_role === 'master_client') {
            let modalContractArtist = $('#modal-contact-artist');
            modalContractArtist.find('.btn-popup-send').removeClass('disabled');
            modalNewWork = modalContractArtist.find('.list-new-works__sub-item');
            modalNewWork.empty();

            if ($(this).parents('.sample-audio-item').length) {
                itemSaleContent = $(this).parents('.sample-audio-item');
                cloned = itemSaleContent.find('.sample-audio-thumbnail').clone();
                let saleInfo = $(this).find('.sample-audio-title');
                description = saleInfo.attr('data-content-desc');
                credit = saleInfo.attr('data-credit');
                created_year = saleInfo.attr('data-created-year');
                album_title = saleInfo.attr('data-content-title');
                artist_name = $('.profile').attr('data-artist-name');
                modalNewWork.append(`<div class="list-new-works__item-container" style="width: auto; margin: 0px;"></div>`);
                modalNewWork.find('.list-new-works__item-container').attr('data-artist', $('.profile').attr('data-user'));
                modalNewWork.find('.list-new-works__item-container').attr('data-sale-id', saleInfo.parents('.sample-audio-item').attr('data-sale-content-id'));
                initDescription(saleInfo);

            } else {
                cloned = itemSaleContent.find('.list-new-works__media').clone();
                itemSaleContentCloned = itemSaleContent.clone();
                description = $(this).attr('data-description');
                credit = $(this).attr('data-credit');
                created_year = $(this).attr('data-created-year');
                album_title = itemSaleContent.find('.list-new-works__title')[0].innerText;
                artist_name = itemSaleContent.find('.list-new-works__media').attr('data-artist');
                modalNewWork.append(itemSaleContentCloned);
                initDescription($(this).parents('.list-new-works__item-container'));
            }

            saleItemModal = modalNewWork.find('.list-new-works__item-container');

            saleItemModal.empty();
            saleItemModal.css({'width': 'auto', 'margin': '0'});
            saleItemModal.append(cloned);
            saleItemModal.append(`
        <div class="album_info_in_modal">
            <div class="album_title_in_modal">
                <span class="album_title_in_modal-title">${album_title}</span>
                <span class="album_title_in_modal-hint">${created_year}</span>
            </div>
            <div class="album_desc_in_modal">
                <p class="album_title_in_modal-subtitle break-line">${artist_name}</p>
                <p class="album_title_in_modal-title-hint break-line">${credit}</p>
                <p class="album_title_in_modal-subtitle break-line">${description}</p>
            </div>
        </div>`);
            modalNewWork.find('.album_info_in_modal').attr('data-sale-type', sale_type);
            modalContractArtist.modal('show');
        }
    });
    $(document).on('hidden.bs.modal', '#modal-contact-artist', function () {
       let modalDom = $(this);
       modalDom.find('input, textarea').val('');
    });
}


function initDescription(saleDom) {
    let saleType, valueTextaera, type, maxPrice, minPrice, customizable_setting;

    if (saleDom.hasClass('sample-audio-title')) {
        saleType = saleDom.attr('data-sale-type');
        type = saleDom.attr('data-content-type');
        minPrice = saleDom.attr('data-content-auctions-start-price');
        maxPrice = saleDom.attr('data-content-auctions-end-price');
        customizable_setting = saleDom.attr('data-customizable-sale-setting')
    } else {
        saleType = saleDom.find('.list-new-works__content').attr('data-sale-type');
        type = saleDom.find('.list-new-works__media').attr('data-type');
        maxPrice = saleDom.find('.list-new-works__content').attr('data-max-price');
        minPrice = saleDom.find('.list-new-works__content').attr('data-min-price');
        customizable_setting = saleDom.find('.list-new-works__content').attr('data-customizable-sale-setting')
    }

    switch (saleType) {
        case '1':
            valueTextaera = 'この作品を購入したいのですが、いかがでしょうか。';
            break;
        case '2':
            valueTextaera = 'この作品を' + customizable_setting + 'したいのですが、いかがでしょうか。';
            break;
        case '3':
            valueTextaera = 'この作品を非独占でそのまま利用したいのですが、いかがでしょうか。';
            break;
        default:
            valueTextaera = 'この作品のテイストで新規制作を依頼したいのですが、いかがでしょうか。';
            break;
    }

    $('#modal-contact-artist textarea').attr('placeholder', valueTextaera);
    $('#modal-contact-artist textarea').val(valueTextaera);

    let initPrice;
    if (maxPrice > 0) {
        initPrice = maxPrice;
    } else if (minPrice > 0) {
        initPrice = minPrice;
    } else if (type === 'music') {
        initPrice = 100000;
    } else if (type === 'sound_effect' || type === 'voice') {
        initPrice = 50000;
    }

    if (initPrice) {
        initPrice = parseInt(initPrice);
        $('#modal-contact-artist #id_budget').val(initPrice);
        $('#modal-contact-artist #id_budget').trigger('input')
    } else {
        $('#modal-contact-artist #id_budget').val('');
    }
}


var artist_id;
var sale_name;
var sale_id;
function createLinkForAddMessage() {
    // user has action click in sale content
    if (is_logged_in !== 'True' || user_role === 'master_client') {
        $(document).on('mouseenter mouseleave', '.gallery__new-works-container .list-new-works__content, .sample-audio-item .sample-audio-info', function (e) {
            $(this).css('cursor', 'pointer');
        })
    }
    contactArtist();

    $(document).on('click', '#modal-contact-artist .btn-popup-send', function () {
        let modalContact = $('#modal-contact-artist');
        let modalConfirm = $('#modal-confirm-step');
        let messageDom = modalContact.find('#id_description');
        let messageValue = messageDom.val().trim().replaceAll('\n', '\\n');
        let budgetValue = modalContact.find('#id_budget').val().replaceAll(',', '').trim();
        budgetValue = budgetValue !== '' ? parseInt(budgetValue) : 0;
        artist_id = $('#modal-contact-artist .list-new-works__item-container').attr('data-artist');
        let sale_item_id = $('#modal-contact-artist .list-new-works__item-container').attr('data-sale-id');
        sale_id = sale_item_id;
        sale_name = $('#modal-contact-artist .list-new-works__item-container .album_title_in_modal-title').text() + '/' + $('#modal-contact-artist .list-new-works__item-container .album_title_in_modal-subtitle').eq(0).text();

        if (!sale_item_id || !artist_id || messageValue === '' || sale_name === '') {
            return
        }
        let sale_type = $('#modal-contact-artist .list-new-works__item-container .album_info_in_modal').attr('data-sale-type');

        if (is_logged_in !== 'True' || sale_type === '4') {
            redirectScreenCreateProject('1', messageValue, artist_id, budgetValue, sale_id, sale_name);
        } else if (is_logged_in === 'True' && user_role === 'master_client' && sale_type !== '4') {
            // get value for model confirm
            let titleAlbum = $('#modal-contact-artist .list-new-works__item-container .album_title_in_modal-title').text();
            let artistName = $('#modal-contact-artist .list-new-works__item-container .album_title_in_modal-subtitle').eq(0).text();
            modalConfirm.find('#id_album_name').text('(' + titleAlbum + '/' + artistName + ')');
            modalConfirm.find('#id_description_value').text(messageValue);
            let idBudgetValue = $('#id_budget').val().trim() !== '' ? $('#id_budget').val().trim() : 0;
            modalConfirm.find('#id_budget_value').text(idBudgetValue);
            modalConfirm.modal('show');
        }
    })
}

function validateBudget() {
    // Budget
    $(document).on('input', '#id_budget', function (e) {
        let intValue = this.value.replaceAll(',', '');
        if (intValue.length > this.maxLength) {
            this.value = intValue.slice(0, this.maxLength - 4);
        }
        this.value = intValue.replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    });

    // description
    $(document).on('input', '#modal-contact-artist #id_description', function (e) {
        let messageValue = $('#id_description').val().trim();
        if (messageValue) {
            $('#modal-contact-artist .btn-popup-send').removeClass('disabled')
        } else {
            $('#modal-contact-artist .btn-popup-send').addClass('disabled')
        }
    });
}

function confirmStep() {
    $('#submit-confirm').on('click', function (e) {
        $('.errorlist').remove();
        $('.error-border').removeClass('error-border');
        e.preventDefault();
        let button_dom = $('#submit-confirm');
        if (!button_dom.hasClass('disable')) {
            let form = $('#contact-artist');

            // submit form
            button_dom.addClass('disable');

                e.preventDefault();

                let budgetValue = $('#id_budget').val().replaceAll(',', '').trim();
                budgetValue = budgetValue !== '' ? parseInt(budgetValue) : 0;
                $('#id_budget').val(budgetValue);
                var formData = new FormData(form[0]);

                formData.append('artist_id', artist_id);
                formData.append('sale_id', sale_id);
                formData.append('sale_name', sale_name);
                formData.append('add_by_sale', 'true');
                formData.append('disclosure_rule', 'can_public');
                formData.append('contract_type', 'buyout');
                formData.append('ownership_type', 'provide_from_us');

                $.ajax({
                    type: 'POST',
                    url: '/direct/create_offer',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        if (data.status === 'success') {
                            $('#modal-contact-artist').modal('hide');
                            $('#submit-ok').parents('a').attr('href', data.url);
                            $('#modal-create-success').modal('show');
                        }
                    },
                    statusCode: {
                        400: function (data) {
                            let str = data.responseText;
                            str = str.replaceAll(')(', ', ').replaceAll(')', '').replaceAll('(', '');
                            let array = str.split(',');
                            var items = [];

                            for (i = 0; i <= array.length - 2; i += 2) {
                                items[array[i]] = array[i + 1]
                            }

                            for (const [key, value] of Object.entries(items)) {
                                console.log(key);
                                let dom = $('#id_' + key.replaceAll(/'/g, '').trim());
                                let new_value = value.replace('[', '').replace(']', '').replaceAll(/'/g, '').trim();
                                $(`<ul class="errorlist"><li>${new_value}</li></ul>`).insertAfter(dom);
                                dom.addClass('error-border')
                            }
                        }
                    },
                    complete: function () {
                        $('#modal-confirm-step').modal('hide');
                        button_dom.removeClass('disable');
                    }
                });

        }
        return
    })
}
