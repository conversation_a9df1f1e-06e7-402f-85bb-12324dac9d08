/**
 * MediaConvert Utilities for HLS Video Support
 * Handles video conversion and HLS playback across the application
 */

// Initialize HLS video player for converted media files
function initializeHLSVideo(video) {
    if (!video) return;

    const videoSrc = video.getAttribute('data-video-src');
    const isHLS = video.getAttribute('data-is-hls') === 'true';
    const fallbackSrc = video.getAttribute('data-fallback-src');

    if (isHLS && videoSrc && videoSrc !== 'undefined') {
        if (typeof Hls !== 'undefined' && Hls.isSupported()) {
            const hls = new Hls({
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90
            });

            hls.loadSource(videoSrc);
            hls.attachMedia(video);

            // Store HLS instance for cleanup
            video._hlsInstance = hls;

            // Restore saved position and playback state after HLS is ready
            hls.on(Hls.Events.MANIFEST_PARSED, function() {
                setTimeout(function() {
                    // Try to restore from video element first (legacy method)
                    if (video._savedCurrentTime && video._savedCurrentTime > 0) {
                        console.log('HLS restore: Restoring position from video element', video._savedCurrentTime, 'was playing:', video._wasPlaying);
                        video.currentTime = video._savedCurrentTime;
                        
                        if (video._wasPlaying) {
                            video.play().catch(e => {
                                console.warn('Could not resume video playback:', e);
                            });
                        }
                        
                        // Clear saved data
                        video._savedCurrentTime = null;
                        video._wasPlaying = false;
                    } else {
                        // Try to restore from position manager
                        videoPositionManager.restorePosition(video);
                    }
                }, 100);
            });

            // Error handling
            hls.on(Hls.Events.ERROR, function(event, data) {
                console.warn('HLS Error:', data);
                if (data.fatal) {
                    if (fallbackSrc && fallbackSrc !== 'undefined' && fallbackSrc !== 'null') {
                        // Fallback to original video if HLS fails and fallback is available
                        console.log('Falling back to original video:', fallbackSrc);
                        video.src = fallbackSrc;
                        if (video._hlsInstance) {
                            video._hlsInstance.destroy();
                            video._hlsInstance = null;
                        }
                        
                        // Restore position on fallback video too
                        if (video._savedCurrentTime && video._savedCurrentTime > 0) {
                            video.addEventListener('loadedmetadata', function() {
                                video.currentTime = video._savedCurrentTime;
                                video._savedCurrentTime = null;
                                
                                if (video._wasPlaying) {
                                    video.play().catch(e => {
                                        console.warn('Could not resume fallback video playback:', e);
                                    });
                                    video._wasPlaying = false;
                                }
                            }, { once: true });
                        }
                    } else {
                        // No fallback available, try to reload HLS or show error
                        console.error('HLS failed and no fallback available');
                        // Optionally reload HLS after a delay
                        setTimeout(() => {
                            if (video._hlsInstance) {
                                video._hlsInstance.loadSource(videoSrc);
                            }
                        }, 2000);
                    }
                }
            });
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            // Safari native HLS support
            video.src = videoSrc;
            
            // Restore position for Safari native HLS too
            video.addEventListener('loadedmetadata', function() {
                // Try to restore from video element first (legacy method)
                if (video._savedCurrentTime && video._savedCurrentTime > 0) {
                    video.currentTime = video._savedCurrentTime;
                    
                    if (video._wasPlaying) {
                        video.play().catch(e => {
                            console.warn('Could not resume Safari HLS playback:', e);
                        });
                    }
                    
                    // Clear saved data
                    video._savedCurrentTime = null;
                    video._wasPlaying = false;
                } else {
                    // Try to restore from position manager
                    videoPositionManager.restorePosition(video);
                }
            }, { once: true });
        }
    }
}

// Initialize all HLS videos on the page
function initializeAllHLSVideos() {
    const videos = document.querySelectorAll('video[data-is-hls="true"]');
    videos.forEach(function(video) {
        if (!video._hlsInstance) {
            initializeHLSVideo(video);
        } else {
            // If HLS instance exists but video seems to have lost its source, reinitialize
            if (video._hlsInstance && !video.src && video.getAttribute('data-video-src')) {
                console.log('HLS instance exists but video has no source, reinitializing...');
                // Save current position before reinitializing
                if (video.currentTime > 0) {
                    video._savedCurrentTime = video.currentTime;
                    video._wasPlaying = !video.paused;
                }
                cleanupHLSVideo(video);
                initializeHLSVideo(video);
            }
        }
    });
}

// Auto-initialize HLS videos when DOM changes
function setupHLSVideoObserver() {
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // Check if the added node is a video or contains videos
                            const videos = node.tagName === 'VIDEO' ? [node] : node.querySelectorAll ? node.querySelectorAll('video[data-is-hls="true"]') : [];
                            videos.forEach(function(video) {
                                // Add small delay to ensure DOM is fully rendered and avoid duplicate initialization
                                setTimeout(function() {
                                    if (!video._hlsInstance && video.getAttribute('data-is-hls') === 'true') {
                                        initializeHLSVideo(video);
                                    }
                                }, 50);
                            });
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
}

// Simple video position preservation for slick slider
const videoPositionManager = {
    positions: new Map(),
    
    savePosition: function(video) {
        if (video && video.currentTime > 0) {
            const videoId = this.getVideoId(video);
            this.positions.set(videoId, {
                currentTime: video.currentTime,
                wasPlaying: !video.paused,
                timestamp: Date.now()
            });
            console.log('Saved video position:', videoId, video.currentTime);
        }
    },
    
    restorePosition: function(video) {
        if (video) {
            const videoId = this.getVideoId(video);
            const savedData = this.positions.get(videoId);
            
            if (savedData && savedData.currentTime > 0) {
                // Only restore if saved recently (within 30 seconds)
                if (Date.now() - savedData.timestamp < 30000) {
                    video.currentTime = savedData.currentTime;
                    console.log('Restored video position:', videoId, savedData.currentTime);
                    
                    if (savedData.wasPlaying) {
                        video.play().catch(e => {
                            console.warn('Could not resume video playback:', e);
                        });
                    }
                }
                // Clean up old position data
                this.positions.delete(videoId);
            }
        }
    },
    
    getVideoId: function(video) {
        // Create unique identifier for video based on its source or data attributes
        return video.getAttribute('data-video-src') || 
               video.getAttribute('src') || 
               video.getAttribute('data-scene-id') || 
               video.id || 
               'video_' + Array.prototype.indexOf.call(document.querySelectorAll('video'), video);
    }
};

// Initialize HLS videos when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeAllHLSVideos();
    setupHLSVideoObserver();
    setupSlickHLSHandlers();
});


// Clean up HLS instance for a video element
function cleanupHLSVideo(video) {
    if (video && video._hlsInstance) {
        // Store current time before destroying HLS instance
        if (video.currentTime > 0) {
            // Save to video element (legacy method)
            video._savedCurrentTime = video.currentTime;
            video._wasPlaying = !video.paused;
            console.log('HLS cleanup: Saved position', video.currentTime, 'was playing:', video._wasPlaying);
            
            // Save to position manager
            videoPositionManager.savePosition(video);
        }
        
        video._hlsInstance.destroy();
        video._hlsInstance = null;
    }
}

// Clean up all HLS instances in a container
function cleanupAllHLSVideos(container) {
    const videos = container ? container.querySelectorAll('video[data-is-hls="true"]') : document.querySelectorAll('video[data-is-hls="true"]');
    videos.forEach(cleanupHLSVideo);
}

// Setup Slick carousel event handlers for HLS videos
function setupSlickHLSHandlers() {
    // Handle slick beforeChange event to cleanup HLS instances
    $(document).on('beforeChange', '.slick-slider', function(event, slick, currentSlide, nextSlide) {
        // Only cleanup if actually changing slides (not the same slide)
        if (currentSlide !== nextSlide) {
            console.log('Slick beforeChange: cleaning up slide', currentSlide, 'moving to', nextSlide);
            const $currentSlide = $(slick.$slides[currentSlide]);
            const videos = $currentSlide.find('video[data-is-hls="true"]');
            videos.each(function() {
                // Save position before cleanup
                videoPositionManager.savePosition(this);
                cleanupHLSVideo(this);
            });
        }
    });

    // Handle slick afterChange event to initialize HLS videos
    $(document).on('afterChange', '.slick-slider', function(event, slick, currentSlide) {
        console.log('Slick afterChange: initializing slide', currentSlide);
        
        // Initialize HLS videos in the new active slide
        setTimeout(function() {
            const $currentSlide = $(slick.$slides[currentSlide]);
            const videos = $currentSlide.find('video[data-is-hls="true"]');
            videos.each(function() {
                if (!this._hlsInstance) {
                    initializeHLSVideo(this);
                }
            });
        }, 100);
    });

    // Handle slick init event to initialize HLS videos in the first slide
    $(document).on('init', '.slick-slider', function(event, slick) {
        console.log('Slick init: initializing first slide');
        setTimeout(function() {
            const $activeSlide = $(slick.$slides[slick.currentSlide]);
            const videos = $activeSlide.find('video[data-is-hls="true"]');
            videos.each(function() {
                if (!this._hlsInstance) {
                    initializeHLSVideo(this);
                }
            });
        }, 100);
    });
}

// Utility function to wait for HLS functions to be available
function waitForHLSFunction(functionName, callback, maxRetries = 5, delay = 500) {
    let retries = 0;

    function tryCallback() {
        if (typeof window[functionName] === 'function') {
            callback();
        } else if (retries < maxRetries) {
            retries++;
            console.warn(`${functionName} not available, retrying (${retries}/${maxRetries})...`);
            setTimeout(tryCallback, delay);
        } else {
            console.error(`${functionName} not available after ${maxRetries} retries`);
        }
    }

    tryCallback();
}

// Export functions for use in other modules
window.initializeHLSVideo = initializeHLSVideo;
window.initializeAllHLSVideos = initializeAllHLSVideos;
window.setupHLSVideoObserver = setupHLSVideoObserver;
window.cleanupHLSVideo = cleanupHLSVideo;
window.cleanupAllHLSVideos = cleanupAllHLSVideos;
window.setupSlickHLSHandlers = setupSlickHLSHandlers;
window.waitForHLSFunction = waitForHLSFunction;
window.videoPositionManager = videoPositionManager;