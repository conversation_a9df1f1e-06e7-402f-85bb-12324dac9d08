var socket;
$(document).ready(function () {
    if (is_logged_in === 'True') {
        function connect() {
            let sk_protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            endpoint = sk_protocol + '//' + window.location.host + '/ws/users_online';
            socket = new WebSocket(endpoint);
            socket.onopen = function (e) {
                console.log("Ping");
            };

            socket.onmessage = function (e) {
                data = JSON.parse(e.data);
                if (data.command === 'user_online') {
                    array_ids = data.on_ids.split(",");
                    $('.messenger__item').find('.messenger__user-active ').addClass('hide');
                    for (let i = 0; i < array_ids.length; i++) {
                        $('.messenger__item[data-user=' + array_ids[i] + ']').find('.messenger__user-active ').removeClass('hide');
                    }
                }
            };

            socket.onerror = function (e) {
                console.log("error", e);
                socket.close()
            };


            socket.onclose = function (e) {
                console.log("close");
                setTimeout(function () {
                    connect();
                }, 1000);
            };
        }

        connect();
    }
});
