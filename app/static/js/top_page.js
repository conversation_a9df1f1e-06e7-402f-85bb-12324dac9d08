let scenes = [];
let wavesurfer_arr = [];
let wavesurfers = [];

let project_id_load_more = false;
let project_item_load_more = false;
let total_page = 1;
let page = 1;
let ajax_calling = false;
let new_projects = [];
let update_projects = [];
let current_tab = 'new';
let bullet_width = 15;
let min_index = 0;
let show_items = 9;
let variation_current = 1;
let in_animation = false;
let last_load_product_scene = false;
let list_product_scene_ids = [];
let total_load = 1;
let count_product_scene = 1;
let first_open = false;
let delivery_chapter = false;
let todolist_chapter = false;
const width_responsive = 695;

function initPinTimeButton() {
    // $('.video-comment-input-pin, .mcomment-pin').off().on('click', function (e) {
    //     $(this).toggleClass('active');
    // });
}

function getCursorPosition(textarea) {
    var pos = 0;
    if ('selectionStart' in textarea) {
        pos = textarea.selectionStart;
    } else if ('selection' in document) {
        textarea.focus();
        var sel = document.selection.createRange(),
            selLength = document.selection.createRange().text.length;
        sel.moveStart('character', -textarea.value.length);
        pos = sel.text.length - selLength;
    }
    return pos;
}

function sumoSelectInit() {
    $('.sumo-select select').each(function () {
        $(this).SumoSelect();
    });

    $('.custom-scrollbar').mCustomScrollbar({
        theme: 'minimal-dark'
    });

    $('.custom-scrollbar-horizontal').mCustomScrollbar({
        theme: 'minimal-dark',
        axis: 'x'
    });

    $('.video-item-comment').not('.mCustomScrollbar').mCustomScrollbar({
        theme: "light-2",
        scrollbarPosition: "outside",
        autoHideScrollbar: true,
        autoExpandScrollbar: true,
        scrollInertia: 100,
        mouseWheel:{ scrollAmount: 200 }
    });
}

function mScrollbarInit() {
}

function sliderInit(target) {
    //target is a project-video-item
    var $project_video_item = target;
    var $project_video_component = $project_video_item.find('.video-item-component.active');
    var video_element = $('video', $project_video_component).get(0);
    var videoInterval = [];
    var pinStopInterval = [];
    if (video_element) {
        videoInterval = setInterval(function () {
            if (video_element.readyState === 4) {
                $project_video_item.find('.video-item-comment-content .video-time-slider-bar').each(function (i, el) {
                    var min_value = 0;
                    var max_value = video_element.duration;
                    var min_current = 0;
                    var max_current = video_element.duration;
                    if (!$(el).hasClass('noUi-target')) {
                        var slider_item = $(this).parents('.video-time-slider-item');

                        $project_video_item.find('.video-time-slider-start').text(formatTime(min_current));
                        $project_video_item.find('.video-time-slider-end').text(formatTime(max_current));

                        var start = [min_current, max_current];

                        noUiSlider.create(el, {
                            animate: true,
                            animationDuration: 0,
                            start: start,
                            connect: false,
                            range: {
                                'min': min_value,
                                'max': max_value
                            }
                        }).on('slide', function (values, handle, unencoded, tap, positions) {
                            var min = parseInt(values[0]);
                            var max = parseInt(values[1]);
                            var currentHandle = handle === 0 ? 'min' : 'max';

                            if (currentHandle === 'max') {
                                $project_video_item.find('.video-time-slider-end').text(formatTime(max));
                            } else if (currentHandle === 'min') {
                                $project_video_item.find('.video-time-slider-start').text(formatTime(min));
                            }
                        });

                        setHandleForSlider(el, slider_item);
                    } else {
                        $project_video_item.find('.video-time-slider-start').text(formatTime(min_current));
                        $project_video_item.find('.video-time-slider-end').text(formatTime(max_current));
                        el.noUiSlider.updateOptions({
                            start: [min_current, max_current],
                            range: {
                                'min': min_value,
                                'max': max_value
                            }
                        });
                    }
                });

                clearInterval(videoInterval);
            }
        }, 300);
    }
}

function buttonSendInit(target_button) {
    target_button.find('.video-comment-input-text.cs-textarea').off('keyup').keyup(function () {
        var comment_content = $(this).val();
        if (!comment_content) {
            let button = $(this).parents('.video-comment-message').find('.button.button--text.button--text-primary');
            button.addClass('button--disabled');
            $(this).parents('.video-comment-message').find('.video-comment-input-label').addClass('button--disabled');
        } else {
            let button = $(this).parents('.video-comment-message').find('.button.button--text.button--text-primary.button--disabled');
            button.removeClass('button--disabled');
            $(this).parents('.video-comment-message').find('.video-comment-input-label.button--disabled').removeClass('button--disabled');
        }
        autoResize(this);
    });

    target_button.find('.video-comment-button-send').off('click').on('click', function () {
        if (!$(this).find('a.button--disabled').length) {
            autoResize($(this).siblings('.video-comment-input').find('textarea')[0]);
            $(this).find('a').addClass('button--disabled');
            let target = $(this);
            let scene_id = $(this).parents('.project-video-item.show-comment').find('.video-item-list.active .video-item-component.active').attr('data-scene-id');
            let comment = $(this).parents('.video-comment-message').find('.video-comment-input-text.cs-textarea').val();
            let parent_id = $(this).parents('.video-comment-item').data('parent-id');
            let scene_title_id = $(this).parents('.project-video-item').data('scene-title-id');
            let messenger_attach_element = $(this).parents('.video-comment-item').find('input.video-comment-input-attach').first();
            let messenger_attach = messenger_attach_element.get(0).files[0];
            let duration = messenger_attach_element.first().attr('data-duration');
            let data = new FormData();
            data.append('file', messenger_attach);
            data.append('duration', duration);
            data.append('type', 'scene');

            let pin_time = null;
            let pin_button = $(this).parents('.video-comment-message').find('.video-comment-input-pin.active');

            let is_parent = true;
            let have_pin = false;

            if (!parent_id) {
                if (!pin_button.length) {
                    let values = {
                        'scene_title_id': scene_title_id,
                        'comment': comment
                    };
                    for (const property in values) {
                        data.append(property, values[property])
                    }
                } else {
                    have_pin = true;
                    pin_time = pin_button.find('.pin-icon-time').eq(0).text();
                    if (!pin_time.length) {
                        pin_time = '00:00';
                    }
                    let values = {
                        'scene_id': scene_id,
                        'comment': comment,
                        'pin_time': pin_time
                    };

                    for (const property in values) {
                        data.append(property, values[property])
                    }
                }
            } else {
                is_parent = false;
                if(pin_button.length) {
                    have_pin = true;
                    pin_time = pin_button.find('.pin-icon-time').eq(0).text();
                    if (!pin_time.length) {
                        pin_time = '00:00';
                    }
                    data.append('pin_time', pin_time);
                }
                let values = {
                    'scene_id': scene_id,
                    'comment': comment,
                    'parent_id': parent_id
                };
                for (const property in values) {
                    data.append(property, values[property])
                }
            }

            let right = parent_id && !$(this).parents('.video-comment-item').prev().is('.right') ? '' : ' right';
            data.append('right', right);
            $(append_placeholder_comment(is_parent, have_pin, right, data)).insertBefore(target.parents('.video-comment-item')).hide().slideDown(300);
            // re-init comment
            $(this).parents('.video-comment-message').find('.video-comment-input-text.cs-textarea').val('');
            $(this).parents('.video-comment-item').find('input.video-comment-input-attach').val('');
            $('[for^="'+ $(this).parents('.video-comment-item').find('input.video-comment-input-attach')[0].id +'"]').addClass('button--disabled');
            $(this).parents('.video-comment-item').find('.comment__textarea-file').remove();
            let upload_button_wrapper = target.parents('.video-item-comment').find('.upload-button-wrapper');
            $.ajax({
                type: "POST",
                url: "/top/create_comment",
                data: data,
                cache: false,
                processData: false,
                contentType: false,
                beforeSend: function(data) {
                    if(messenger_attach) {
                        // toastr.info('アップロード中…');
                        upload_button_wrapper.css('display', 'flex');
                        upload_button_wrapper.addClass('clicked');
                        upload_button_wrapper.find('.fill .process').css('width', '2%');
                    } else {
                        // toastr.info('コメント作成しています。');
                    }
                },
                xhr: function () {
                    var xhr = new window.XMLHttpRequest();
                    if (messenger_attach) {
                        xhr.upload.addEventListener("progress", function (evt) {
                            if (evt.lengthComputable) {
                                let percentComplete = (evt.loaded / evt.total) * 70;
                                upload_button_wrapper.find('.fill .process').css('width', percentComplete + '%');
                            }
                        }, false);
                    }
                    return xhr;
                },
                success: function (data) {
                    if (messenger_attach) {
                        upload_button_wrapper.find('.fill .process').css('width', '100%');
                        setTimeout(function () {
                            upload_button_wrapper.removeClass('clicked').addClass('success')
                        }, 1000);
                        setTimeout(function () {
                            upload_button_wrapper.removeClass('success').css('display', 'none')
                            upload_button_wrapper.find('.fill .process').css('width', '0');
                        }, 2000);
                        // toastr.success('アップロードを完了しました。');
                    } else {
                        // toastr.success('コメントを作成しました。');
                    }

                    let target_dom = target.parents('.video-comment-item').prev();
                    $(data.comment).insertBefore(target.parents('.video-comment-item'));
                    target_dom.remove();
                    target_dom = target.parents('.video-comment-item').prev();
                    initComment(target_dom);
                    target_dom.find('.fa-download, .comment__download-icon-down').off().on('click', function(e) {
                        e.stopPropagation();
                        let comment_id = $(this).parents('.video-comment-item-reply').attr('data-cmt-id');
                        $.ajax({
                            type: "GET",
                            url: "/top/get_file_download_link",
                            data: {
                                "comment_id": comment_id,
                                'type': 'scene'
                            },
                            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                                window.location.href = response.url;
                            },
                            fail: function (response) {
                                toastr.error('エラーが発生しました', 'ファイルをダウンロード');
                            }
                        })
                    });
                    add_flag_to_hide_video_on_close(target.parents('.project-video-item.show-comment'));
                }, error: function (data) {
                    toastr.error('エラーが発生しました');
                    upload_button_wrapper.removeClass('clicked');
                    upload_button_wrapper.css('display', 'none');
                    upload_button_wrapper.find('.fill .process').css('width', '0');
                }
            })
        }
    });
}

function messageActionInit(target_button) {
    buttonSendInit(target_button);
    target_button.find('.video-comment-resolve').off().on('click', function () {
        let target = $(this).parents('.video-comment-item-reply')
        let comment_id = target.attr('data-cmt-id');

        while(target.is('.sub-item')) {
            target = target.prev();
        }
        let resolved = target.is('.resolved');
        if (target.is('.resolved')) {
            show_unresolved_comment(target);
        } else {
            hide_resolved_comment(target);
        }
        $.ajax({
            type: "POST",
            datatype: "json",
            url: "/top/resolve_comment",
            data: {
                'comment_id': comment_id,
                'resolved': resolved,
                'type': 'scene'
            }, fail: data => {
                let text = target.is('.resolved') ? '再開' : '解決';
                toastr.error('エラーが発生しました', 'コメント' + text);
                if (resolved) {
                    hide_resolved_comment(target);
                } else {
                    show_unresolved_comment(target);
                }
            }
        })
    });

    target_button.find('.video-comment-reply').off().on('click', function () {
        let parent = $(this).parents('.video-comment-item-reply');
        let current_dom = parent;
        while (current_dom.is('.sub-item')) {
            current_dom = current_dom.prev();
        }
        let append_dom = parent;
        while (1) {
            if (append_dom.next().is('.video-comment-item-reply.sub-item')) {
                append_dom = append_dom.next();
            } else if (append_dom.next().is('.video-comment-item.sub-item')) {
                return;
            } else {
                break;
            }
        }

        let timestamp = new Date().getTime();
        append_dom.after(`
            <div class="video-comment-item sub-item" data-parent-id="${current_dom.attr('data-cmt-id')}">
                <div class="video-comment-message">
                    <div class="video-comment-input-pin">
                        <img src="/static/images/icon-pin.svg" alt="" class="mCS_img_loaded">
                        <div class="pin-icon-time">0:00</div>
                    </div>
                    <div class="video-comment-input">
                    <textarea class="video-comment-input-text autoExpand cs-textarea" name="messenger-input" placeholder="コメントを入力…"></textarea>
                  </div>
                  <input class="video-comment-input-attach" type="file" name="messenger-attach" placeholder="" id="messenger-attach-${timestamp}" data-duration="-1">
                  <label class="video-comment-input-label button--disabled" for="messenger-attach-${timestamp}"><img src="../static/images/icon-upload.svg" alt=""></label>
                  <div class="video-comment-button-send"><a class="button button--text button--text-primary button--disabled" href="javascript:void(0)" role="button">送信</a>
                  </div>
                </div>
            </div>
        `);

        initPinTimeButton();
        mScrollbarInit();
        buttonSendInit(target_button);
        $(this).parents('.video-comment-item-reply').find('.video-comment-item.sub-item').hide(0);
        $(this).parents('.video-comment-item-reply').find('.video-comment-item.sub-item').show(300);
    });

    target_button.find('button#delete-comment').off().on('click', function () {
        let comment_id = $(this).parents('.video-comment-item-reply').attr('data-cmt-id');
        if (comment_id !== undefined) {
            let video_comment_item = $(this).parents('.video-comment-item-reply')
            bootbox.confirm({
                message: "このコメントを削除したいでしょうか?",
                buttons: {
                    confirm: {
                        label: 'Yes',
                        className: 'btn-success'
                    },
                    cancel: {
                        label: 'No',
                        className: 'btn-danger'
                    }
                },
                callback: function (result) {
                    if (result === true) {
                        $.ajax({
                            type: "POST",
                            url: "/top/delete_comment",
                            datatype: "json",
                            data: {
                                "comment_id": comment_id,
                                'type': 'scene'
                            },
                            success: function () {
                                video_comment_item.remove();
                                // toastr.success("コメントが削除しました。", "コメントの削除");
                            },
                            error: function () {
                                toastr.error("一旦削除できません。後でもう一度試してお願いします", "コメントの削除");
                            }
                        });
                    }
                }
            });
        }
    });
    target_button.find('button#edit-comment').off().on('click', function () {
        let comment_dom = $(this).parents('.video-comment-item-reply');
        let comment_id = comment_dom.attr('data-cmt-id');

        if (comment_id !== undefined && !comment_dom.is('.editing')) {
            comment_dom.addClass('editing');
            let pin_dom = comment_dom.find('.video-pin-time');
            if (pin_dom.length) {
                let pin_time = pin_dom.find('.video-pin-start').html();
                pin_dom.find('.video-pin-start').addClass('hide');
                pin_dom.append(`<input type="text" value="${pin_time}" class="edit-comment-input-pin-time"/>`)
                pin_dom.find('input').off().on('click', function (e) {
                    e.stopPropagation();
                })
            }

            let comment_content = comment_dom.find('.video-comment-audio');
            comment_content.find('.video-comment-text').addClass('hide');
            comment_content.append(`<textarea class="edit-comment-textarea-comment" style="width: 100%;">${comment_content.find('.video-comment-text').html().replace(/<(.|\n)*?>/g, '\n')}</textarea>`);
            comment_dom.find('.video-comment-action').addClass('hide');
            comment_dom.find('.video-comment-content').append(`<div class="video-comment-action-edit">
                <a class="button button--text button--text-primary video-comment-edit-save" style="margin-right: 35px"
                   href="javascript:void(0)" role="button">保存</a>
                <a class="button button--text button--text-primary video-comment-edit-cancel"
                   href="javascript:void(0)" role="button">キャンセル</a>
            </div>`);

            comment_dom.find('.video-comment-edit-save').off().on('click', function () {
                let new_pin_time = '0:00';
                if (pin_dom.length) {
                    new_pin_time = pin_dom.find('input').val();
                    pin_dom.find('input').remove();
                    if (/^([0-9]{1,2}:)*([0-9]{1,2}:[0-9]{2})+$/.test(new_pin_time)) {
                        pin_dom.find('.video-pin-start').html(new_pin_time);
                    } else {
                        new_pin_time = '0:00';
                    }
                    pin_dom.find('.video-pin-start').removeClass('hide');
                }
                let new_comment_content = comment_content.find('textarea').val();
                comment_content.find('textarea').remove();
                comment_content.find('.video-comment-text').html(new_comment_content);
                comment_content.find('.video-comment-text').removeClass('hide');
                comment_dom.find('.video-comment-action-edit').remove();
                comment_dom.find('.video-comment-action').removeClass('hide');

                let values = {};
                if (!pin_dom.length) {
                    values = {
                        'comment_id': comment_id,
                        'comment': new_comment_content,
                        'type': 'scene'
                    };
                } else {
                    values = {
                        'comment_id': comment_id,
                        'comment': new_comment_content,
                        'type': 'scene',
                        'pin_time': new_pin_time
                    };
                }

                $.ajax({
                    type: "POST",
                    url: "/top/update_comment",
                    data: values,
                    dataType: 'json',
                    success: function (data) {
                        // toastr.success('編集を保存しました。', 'コメントの編集');
                        load_video_comment(comment_dom.parents('.project-video-item.show-comment').children());
                    },
                    error: function (e) {
                        toastr.error('エラーが発生しました', 'コメントの編集');
                        load_video_comment(comment_dom.parents('.project-video-item.show-comment').children());
                    }
                })
            });

            comment_dom.find('.video-comment-edit-cancel').off().on('click', function () {
                if (pin_dom.length) {
                    pin_dom.find('input').remove();
                    pin_dom.find('.video-pin-start').removeClass('hide');
                }

                comment_content.find('textarea').remove();
                comment_content.find('.video-comment-text').removeClass('hide');
                comment_dom.find('.video-comment-action-edit').remove();
                comment_dom.removeClass('editing');
                comment_dom.find('.video-comment-action').removeClass('hide');
            });
        }
    })
}

function wavesurferInit() {
    let current_length = wavesurfer_arr.length;
    $('.video-comment-audio-wave').each(function (i, item) {
        if (!$(item).find('wave').length && !wavesurfer_arr[i + current_length]) {
            var audio_url = $(this).data('audio');
            var wavesurfer = WaveSurfer.create({
                container: item,
                waveColor: '#a7a8a9',
                progressColor: '#36aac4',
                cursorColor: 'rgba(0,157,196,0.29)',
                barWidth: 3,
                barRadius: 3,
                cursorWidth: 3,
                barGap: 3,
                mediaControls: false,
                height: 50,
                responsive: false,
                hideScrollbar: true,
                backend: 'MediaElement',
                pixelRatio: 1
            });

            wavesurfer_arr[current_length+ i] = wavesurfer;


            let cmt_container = $(item).parents(".video-comment-item-reply");
            let cmt_id = cmt_container.attr("data-cmt-id");
            let peaks_loaded = cmt_container.attr("data-peaks-loaded");
            cmt_container.attr('data-wavesurfer', current_length+ i);

            if (cmt_container.length < 1) {
                scence_container = $(item).parents(".video-item-component-content-video");
                peaks_loaded = scence_container.attr("data-peaks-loaded");
                scence_container.attr('data-wavesurfer', current_length+ i);
            }

            if (peaks_loaded && peaks_loaded.length > 1) {
                let array_peaks = peaks_loaded.split(" ");
                wavesurfer.load(audio_url, array_peaks, 'none');
            } else {
                wavesurfer.load(audio_url);
                wavesurfer.on('waveform-ready', function () {
                    let peaks = wavesurfer.backend.getPeaks(32);
                    let peaks_string = "";
                    let values = {};
                    for (let i = 0; i < peaks.length; i++) {
                        peaks_string += String(Math.round(peaks[i] * Math.pow(10, 8)) / Math.pow(10, 8)) + " ";
                    }
                    let wave_index = wavesurfer_arr.indexOf(wavesurfer).toString();
                    let dom_container = $('.video-comment-item-reply[data-wavesurfer^=' + wave_index + ']');
                    if (dom_container.length < 1) {
                        dom_container = $('.video-item-component-content-video[data-wavesurfer^=' + wave_index + ']');
                        let scene_id = dom_container.attr("data-scene-id");
                        values = {
                            "scene_id": scene_id,
                            "peaks": peaks_string,
                            'type': 'variation'
                        }
                    } else {
                        let cmt_id = dom_container.attr("data-cmt-id");
                        values = {
                            "comment_id": cmt_id,
                            "peaks": peaks_string,
                            'type': 'scene'
                        };
                    }
                    $.ajax({
                        type: "POST",
                        url: "/top/update_comment",
                        data: values,
                        dataType: 'json',
                        success: function (data) {
                            console.log("success");
                        },
                        error: function (e) {
                            console.log(e);
                        }
                    });
              });
            }

            $(this).siblings('.video-pin-time, .video-comment-audio-title').on('click', function () {
                stop_video_audio();
                if($(this).siblings(".video-comment-audio-wave").length !== 0) {
                    let scene_id = $(this).parents('.video-comment-content').data('scene-id');

                    let pin_time = $(this).parent().find('.video-pin-start');
                    let target_pin = pin_time.parents('.video-pin-time');
                    if (pin_time.length === 0 || scene_id === 'None') {
                        let is_play = false;
                        let play_pause = $(this).siblings(".video-comment-audio-wave").siblings('.video-pin-time');
                        $('video').each((i, e) => e.pause());
                        $('.video-pin-time').each(function () {
                            $(this).removeClass('playing')
                        });
                        for (i = 0; i < wavesurfer_arr.length; i++) {
                            if (wavesurfer_arr[i]) {
                                if (wavesurfer_arr[i].isPlaying()) {
                                    if (wavesurfer_arr[i] === wavesurfer) {
                                        is_play = true
                                    }
                                    wavesurfer_arr[i].playPause();
                                }
                            }
                        }
                        if (!is_play) {
                            play_pause.addClass('playing');
                            wavesurfer.play();
                            wavesurfer.on('pause', function () {
                                if (wavesurfer.getDuration() === wavesurfer.getCurrentTime()) {
                                    play_pause.removeClass('playing');
                                }
                                play_pause.removeClass('playing');
                                let pins = play_pause.parents('.project-video-item.show-comment').find('.video-item-comment .pin-icon-time');
                                pins.each(function (i, e) {
                                    if ($(e).parents('.video-comment-input-pin').is('.active')) {
                                        $(e).html(msToTime(wavesurfer.getCurrentTime()));
                                    }
                                })
                            });

                            wavesurfer.on('seek', function () {
                                let pins = play_pause.parents('.project-video-item.show-comment').find('.video-item-comment .pin-icon-time');
                                pins.each(function (i, e) {
                                    if ($(e).parents('.video-comment-input-pin').is('.active')) {
                                        $(e).html(msToTime(wavesurfer.getCurrentTime()));
                                    }
                                })
                            })
                        }
                    } else {
                        let start_time = TimeToSeconds(pin_time.text());
                        if (isNaN(start_time)) {
                            start_time = 0;
                        }
                        let video_item_list = $(this).parents('.project-video-item').find('.video-item-wrap .video-item-list');
                        let video_item_component = video_item_list.find('.video-item-component[data-scene-id=' + scene_id + ']').eq(0);
                        let video = video_item_component.find('.video-item-component-content video').get(0);
                        if (video) {
                            playPinWavesurfer(wavesurfer, video, start_time, target_pin);
                        } else {
                            let audio = video_item_component.find('.video-item-component-content audio').get(0);
                            let scene_pin = video_item_component.find('.video-item-component-content .video-item-component-content-video .pin-time-audio');
                            let scene_index = parseInt(video_item_component.find('.video-item-component-content .video-item-component-content-video').attr('data-wavesurfer'));
                            let scene_wavesurfer = wavesurfer_arr[scene_index];
                            if (audio) {
                                playWavesurferWavesurfer(scene_wavesurfer, wavesurfer, start_time, target_pin, scene_pin);
                            }
                        }
                    }
                }
            })
        }
    });
}

function initComment(target) {
    //target is a project-video-item
    //messsage sort button
    sumoSelectInit();

    //init slider
    sliderInit(target);

    //Message button init
    messageActionInit(target);

    // Scrollbar TextArea
    mScrollbarInit();

    wavesurferInit();

    target.find('.video-item-comment-close').off().on('click', function () {
        $(this).parents('.project-video-item')
            .find('.video-item-component.active .show-comment-dom').trigger('click');
    });

    $('input#order-monthy').off().on('change', function () {
        let video_item_comment = $(this).parents('.video-item-comment');
        if ($(this).is(':checked')) {
            video_item_comment.find('.resolved').removeClass('hide').hide(0).show(300);
        } else {
            video_item_comment.find('.resolved').hide(300)
        }
    });

    target.find('.video-pin-time:not(.pin-time-audio), .video-comment-audio-title').on('click', function () {
        let scene_id = $(this).parents('.video-comment-content').data('scene-id');
        if (scene_id !== 'None') {
            let pin_time = $(this).parent().find('.video-pin-start');
            let target_pin = pin_time.parents('.video-pin-time');
            let start_time = TimeToSeconds(pin_time.text());
            if (isNaN(start_time)) {
                start_time = 0;
            }
            let video_item_list = $(this).parents('.project-video-item').find('.video-item-wrap .video-item-list');
            let video_item_control = $(this).parents('.project-video-item').find('.video-item-wrap .video-item-control');
            let video_item_thumbnail_list = video_item_control.find('.video-item-thumbnail-list');
            let video_item_bullet_list = video_item_control.find('.video-item-bullet-list');
            //remove active
            video_item_list.find('.video-item-component').removeClass('active');
            video_item_thumbnail_list.find('.video-item-thumbnail').removeClass('active');
            video_item_bullet_list.find('.video-item-bullet').removeClass('active');
            //find and active true element
            let video_item_component = video_item_list.find('.video-item-component[data-scene-id=' + scene_id + ']').eq(0);
            let index = video_item_component.parents('.video-item-list').data('index');
            let variation_id = video_item_component.parents('.video-item-list').data('variation-id');
            let video_item_thumbnail = video_item_thumbnail_list.find('.video-item-thumbnail[data-index=' + index + ']').eq(0);

            let video_item_bullet = video_item_bullet_list.find('.video-item-bullet[data-index=' + index + ']').eq(0);
            if (video_item_component) {
                video_item_component.addClass('active');
                video_item_thumbnail.addClass('active');
                video_item_bullet.addClass('active');
            }
            //active true variation
            video_item_component.parents('.video-item-list').siblings('.video-item-list').removeClass('active');
            video_item_component.parents('.video-item-list').siblings('.video-item-list').addClass('hide');
            video_item_component.parents('.video-item-list').removeClass('hide').addClass('active');
            //update true video_item_bullet_list
            let max_index = video_item_bullet_list.find('.video-item-bullet').length - 1;
            let current_index = video_item_thumbnail.data('index');
            if (current_index <= 0) {
                $(this).parents('.project-video-item').find('.video-item-bullet-prev').addClass('disable');
                $(this).parents('.project-video-item').find('.video-item-bullet-prev, .video-item-bullet-next').attr('data-current_index', 0);
            } else if (current_index >= max_index) {
                $(this).parents('.project-video-item').find('.video-item-bullet-next').addClass('disable');
                $(this).parents('.project-video-item').find('.video-item-bullet-prev, .video-item-bullet-next').attr('data-current_index', max_index);
            } else {
                $(this).parents('.project-video-item').find('.video-item-bullet-prev, .video-item-bullet-next').removeClass('disable').attr('data-current_index', current_index);
            }
            let video = video_item_component.find('.video-item-component-content video').get(0);
            if (video) {
                if ($(this).siblings(".video-comment-audio-wave").length === 0) {
                    if (target_pin.hasClass('playing')) {
                        stopVideo(video)
                    } else {
                        setTimeout(function () {
                            target_pin.addClass('playing');
                        }, 300);
                        playVideo(video, start_time);
                        $(video).on('pause', function () {
                            target_pin.removeClass('playing');
                        })
                    }
                }
            } else {
                let scene_index = video_item_component.find('.video-item-component-content .video-item-component-content-video').attr('data-wavesurfer');
                let  wavesurfer = wavesurfer_arr[scene_index];
                let scene_pin = video_item_component.find('.video-item-component-content .video-item-component-content-video .pin-time-audio');
                if (target_pin.hasClass('playing')) {
                    stop_video_audio();
                } else {
                    setTimeout(function () {
                        target_pin.addClass('playing');
                    }, 300);
                    playWavesurfer(wavesurfer, start_time, scene_pin);
                    wavesurfer.on('pause', function () {
                        target_pin.removeClass('playing');
                        scene_pin.removeClass('playing');
                    })

                }
            }

            let current_version_index = video_item_component.index();
            let max_version_index = video_item_component.siblings().length + 1;
            if (current_version_index === 0) {
                video_item_component.find('.version-tag').attr('data-content', '');
                video_item_component.find('.version-tag').css('background', '#009ace');
                video_item_component.find('video').removeClass('gray-scale');
            } else {
                video_item_component.find('.version-tag').css('background', '#53565A');
                video_item_component.find('video').addClass('gray-scale');
            }
        }
    });

    window.URL = window.URL || window.webkitURL;
    target.find('input[id^="messenger-attach-"]').on('change', function (e) {
        let context = this;
        let files = this.files;

        if (files.length) {
            let video = document.createElement('video');
            video.preload = 'metadata';
            video.onloadedmetadata = function () {
                window.URL.revokeObjectURL(video.src);
                let duration = video.duration;
                $(context).attr('data-duration', Math.floor(duration));
            };
            video.src = URL.createObjectURL(files[0]);
        } else {
             $(context).attr('data-duration', -1);
        }


        let fileName = files[0].name;
        let clear_file_dom = "clear_" + $(e.target)[0].id;
        let comment_box = $(this).closest('.video-comment-item.comment-form');
        if (comment_box.find('.comment__textarea-file').length > 0) {
            comment_box.find('.comment__textarea-file span').text(fileName);
        } else {
            comment_box.find('.video-comment-message').prepend('<div class="comment__textarea-file"><span>' + fileName +
                '</span><button type="button" class="clear_file close '+ clear_file_dom +'" aria-hidden="true">×</button></div>')
        }

        comment_box.find('.clear_file').off().on('click', function () {
            let target_input = $(this).parents('.video-comment-message').find('input.video-comment-input-attach');
            $(this).parents('.video-comment-item.comment-form').find('.comment__textarea-file').remove();
            target_input.attr('data-duration', -1);
            target_input.val('');
        });
    });

    initPinTimeButton();
    let messages = $('.show-comment .video-comment-text');
    $.each(messages, function (i, v) {
        let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
        v.innerHTML = v.innerHTML.replace(regex, "<a target='_blank' href=$&>$&</a>");
    })
}

function load_video_comment(target) {
    let scene_id = target.parents('.video-item-list .video-item-component.active').attr('data-scene-id');
    if (!scene_id) {
        scene_id = target.parents('.project-video-item.show-comment').find('.video-item-list.active .video-item-component.active').attr('data-scene-id');
    }
}

function load_videos(project_id, $project_item, status, variation_id=-1, scene_id=-1, variation_page=1, show_product_comment=false) {
    ajax_calling = true;
    let lists = '';
    let special_variations = '';
    let prj = [];
    if (page > 1) {
        if (status === 'new') {
            prj = new_projects;
        } else {
            prj = update_projects;
        }
        prj.find(function (e, i) {
            if (e.id === project_id) {
                lists = e.lists;
                special_variations = e.special_variations;
            }
        })
    }
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/top/get_project_video",
        data: {
            'project_id': project_id,
            'page': page,
            'status': status,
            'lists': JSON.stringify(lists),
            'variations': JSON.stringify(special_variations)
        },
        beforeSend: function(){
            $(".project-item__content").find(".loader").show();
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            console.log('successfully load videos');
            if (response.role !== 'admin') {
                let existed = false;
                let existed_id = 0;
                if (response.page === 1) {
                    prj.find(function (e, i) {
                        if (e.id === project_id) {
                            existed = true;
                            existed_id = i
                        }
                    });

                    let content = {
                        'id': project_id,
                        'total_page': response.total_page,
                        'lists': response.lists,
                        'special_variations': response.special_variations
                    };


                    if (!existed) {
                        prj.push(content)
                    } else {
                        prj[existed_id] = content
                    }

                    if (status === 'new') {
                        new_projects = prj;
                    } else {
                        update_projects = prj;
                    }
                }
            }
            let target_dom;
            let project = $('.project-item.active');
            let tab = project.find('.pbanner-tab.active').find('.number-notification');
            if (response.count){
                tab.attr('value', response.count);
                tab.html(response.count);
            }

            if (project.length) {
                target_dom = project.find('.project-tab.project-tab-' + status + '.active .project-item__video-list');

                if (!project_item_load_more) {
                    target_dom[0].innerHTML = '';
                    total_page = response.total_page
                }
                target_dom.append(response.html);
                if (project.find('.project-tab:not(.project-tab-' + status +
                    ') .project-video-item.active, .project-tab:not(.project-tab-' + status +
                    ') .show-comment').length) {
                    project.find('.project-tab:not(.project-tab-' + status +
                        ') .project-video-item:not(.active)').addClass('hide');
                }
            }

            $('.video-item-share, .cscene__share').off().on('click', function() {updateModalInfo($(this))});

            project.find('.project-video-item').off().on('click', function () {
                if (!$(this).is('.marked_as_read_comment')) {
                    $(this).addClass('marked_as_read_comment');
                    let comment_content_element = $(this).find('.video-item-comment-content').first();
                    if (comment_content_element === undefined || comment_content_element.attr('is_read') === 'true') {
                        return;
                    }
                    let scene_title_id = $(this).attr('data-scene-title-id');
                    if (scene_title_id !== undefined) {
                        $.ajax({
                            type: "POST",
                            url: '/top/mark_as_read_comment',
                            datatype: 'json',
                            data: {
                                'scene_title_id': scene_title_id,
                                'type': 'scene'
                            },
                            success: function () {
                                comment_content_element.attr('is_read', 'true');
                            }
                        });
                    }
                }
            });

            project.find('.project-video-item').each(function () {
                var $project_video_item = $(this);

                initCollapseButton($project_video_item);

                initThumbnailClick($project_video_item);

                actionBottomVideo($project_video_item);

                // Process Bullets
                let max_index = $project_video_item.find('.video-item-bullet').length - 1;
                let translate = processBullet(max_index, $project_video_item);

                // Bullets action
                $project_video_item.find('.video-item-bullet').off().on('click', function () {
                    handleBulletClick($project_video_item, min_index, max_index, $(this));
                    let variation_index = $(this).data('index');
                    let variation_current;
                    $(this).parents('.project-video-item').find('.video-item-list').each(function () {
                        if($(this).data('index') === variation_index) {
                            variation_current = $(this).data('variation-id');
                            let refresh = window.location.pathname + '?tab=' + status;
                            refresh = refresh + '&variation=' + variation_current;
                            window.history.pushState({ path: refresh }, '', refresh);
                        }
                    });
                    let show_comment_dom = $('.project-item.active .show-comment');
                    if (show_comment_dom.length) {
                        sliderInit($(this).parents('.project-video-item.show-comment'));
                        $project_video_item.animate({scrollTop: 0}, 1000);
                    }
                });
                if(variation_id !== -1){
                    $project_video_item.find('.video-item-list').each(function () {
                        if($(this).attr('data-variation-id') === variation_id){
                            variation_index = $(this).attr('data-index');
                            $project_video_item.find('.video-item-bullet').each(function () {
                                if($(this).attr('data-index') === variation_index){
                                    $(this).trigger('click');
                                }
                            })
                        }
                    })
                }

                // Next action
                $project_video_item.find('.video-item-bullet-next').off().on('click', function () {
                    var current_index = parseInt($(this).attr('data-current_index'));
                    // console.log(current_index);
                    if (current_index + 1 >= max_index) {
                        $(this).addClass('disable');
                    }

                    $project_video_item.find('.video-item-bullet-prev').removeClass('disable');

                    var new_index = current_index + 1;
                    $(this).attr('data-current_index', new_index);
                    // Translate only when > show_items
                    translate = calculateCarouselButton($project_video_item, max_index, current_index, new_index, translate);
                });

                // Prev action
                $project_video_item.find('.video-item-bullet-prev').off().on('click', function () {
                    var current_index = parseInt($(this).attr('data-current_index'));
                    if (current_index - 1 === min_index) {
                        $(this).addClass('disable');
                    }

                    var new_index = current_index - 1;
                    $(this).attr('data-current_index', new_index);
                    // Update Bullets
                    $project_video_item.find('.video-item-bullet').removeClass('active');
                    $project_video_item.find('.video-item-bullet').eq(new_index).addClass('active');
                    $project_video_item.find('.video-item-bullet-next').removeClass('disable');

                    // Translate only when > show_items
                    translate = calculateCarouselButton($project_video_item, max_index, current_index, new_index, translate);
                });

                // Hover video
                $project_video_item.find('video').each(function () {
                    this.onended = function () {
                        let buttons = $(this).parent().find('.video-button');
                        buttons.fadeIn(300);
                    }
                });

                $project_video_item.find('.video-item-wrap').unbind().hover(function () {
                    let video;
                    if ($(this).is('video')) {
                        video = $(this).get(0);
                    } else {
                        video = $('.video-item-list.active .video-item-component.active video', this).get(0);
                    }
                    $(video).css('filter','none');
                    }, function () {
                    let video;
                    if ($(this).is('video')) {
                        video = $(this).get(0);
                    } else {
                        video = $('.video-item-list.active .video-item-component.active video', this).get(0);
                    }
                    $(this).find('.video-item-list.active .video-item-component.active .video-button').removeClass('show');
                });


                $project_video_item.find('.play-video').unbind().hover(function () {
                    let video;
                    if ($(this).is('video')) {
                        video = $(this).get(0);
                    } else {
                        video = $(this).prev('.video-item-list.active .video-item-component.active video').get(0);
                    }
                    playVideo(video);
                    $('.video-pin-time').each(function () {
                        $(this).removeClass('playing')
                    });
                    }, function () {
                    let video;
                    let video_item;
                    if ($(this).is('video')) {
                        video_item = $(this);
                        video = $(this).get(0);
                    } else {
                        video_item = $(this).prev('.video-item-list.active .video-item-component.active video');
                        video = video_item.get(0);
                    }
                    $(this).find('.video-item-list.active .video-item-component.active .video-button').removeClass('show');
                    video.pause();
                    video_item.on('pause', function () {
                        let video = $(this)[0];
                        let pins = $(this).parents('.project-video-item.show-comment').find('.video-item-comment .pin-icon-time');
                        pins.each(function (i, e) {
                            if ($(e).parents('.video-comment-input-pin').is('.active')) {
                                $(e).html(msToTime(video.currentTime));
                                $('.mcomment-input-title').html('<i class="icon icon--sicon-pin"></i>' + '<span>'+ msToTime(video.currentTime) + '</span>');
                            }
                        })
                    });
                });


                let count = 0;
                $project_video_item.find('.video-item-component').each(function (i, e) {
                    var $project_video_component = $(this);
                    var video_element = $('video', $project_video_component).get(0);
                    if (status === 'update') {
                        if (!$(e).parents('.project-video-item.show-comment').find('.video-item-comment').length) {
                            $project_video_item.addClass('show-comment');
                            load_video_comment($(this));
                        }
                    } else {
                        $project_video_component.find('.show-comment-dom').off().on('click', function (e) {
                            stop_video_audio();
                            e.stopPropagation();
                            let variation_active =$(this).parents('.video-item-list').attr('data-variation-id');
                            let refresh = window.location.pathname + '?tab=' + status;
                            let url_string = window.location.href;
                            let url = new URL(url_string);
                            refresh = refresh + '&variation=' + variation_active;
                            window.history.pushState({ path: refresh }, '', refresh);

                            if ($(this).parents('.project-item.active')
                                .find('.project-tab.project-tab-' + status + ' .project-video-item.show-comment')
                                .length) {
                                refresh = window.location.pathname + '?tab=' + status;
                                window.history.pushState({ path: refresh }, '', refresh);
                                $project_video_item.removeClass('show-comment');
                                $project_video_item.parents('.project-item__video-list')
                                    .find('.project-video-item').removeClass('hide');
                            } else {
                                $project_video_item.addClass('show-comment');
                                $project_video_item.parents('.project-item__video-list')
                                    .find('.project-video-item:not(.show-comment)').addClass('hide');
                                load_video_comment($(this));
                                $('html, body').animate({scrollTop: 0}, 1000);
                            }
                        });
                    }

                    $project_video_component.find('.video-item-button-left').off().on('click', function () {
                        let video = $(this).siblings('video').get(0);
                        video.currentTime = 0;
                        video.play();
                    });

                    $project_video_component.find('.video-item-button-top, .mark-as-ok').off().on('click', function () {
                        if (!$(this).is('.disabled, .confirmed')) {
                            $(this).addClass('disabled');
                            let scene_id = $(this).parents('.video-item-component.active').data('scene-id');
                            $.ajax({
                                type: "POST",
                                datatype: "json",
                                url: "/top/mark_as_ok",
                                data: {
                                    'scene_id': scene_id
                                },
                                success: function (data) {
                                    if (data.result === 'success') {
                                        // toastr.success('進行中に移動しました', 'とりあえずOK');
                                        let target = $('.video-item-component[data-scene-id="' + data.scene_id + '"] .video-item-button-top');
                                        target.removeClass('disabled');
                                        target.addClass('confirmed');
                                        $project_video_item.remove();
                                    } else {
                                        toastr.error('エラーが発生しました', 'とりあえずOK');
                                    }
                                },
                                error: function () {
                                    toastr.error('エラーが発生しました', 'とりあえずOK');
                                }
                            })
                        }
                    });
                    $project_video_component.find('.video-item-button-right, .update-owner').off().on('click', function () {
                        if (!$(this).is('.disabled, .confirmed')) {
                            $(this).addClass('disabled');
                            let scene_id = $(this).parents('.video-item-component.active').data('scene-id');
                            toastr.info('検収していますので、少々お待ちください。', 'ハートつける');
                            $.ajax({
                                type: "POST",
                                datatype: "json",
                                url: "/top/mark_as_done",
                                data: {
                                    'scene_id': scene_id
                                },
                                success: function (data) {
                                    if (data.result === 'success') {
                                        // toastr.success('検収しました!', 'ハートつける');
                                        let target = $('.video-item-component[data-scene-id="' + data.scene_id + '"] .video-item-button-right, .video-item-component[data-scene-id="' + data.scene_id + '"] .update-owner');
                                        target.removeClass('disabled');
                                        target.addClass('confirmed');
                                        $project_video_item.remove();
                                    } else {
                                        toastr.error('エラーが発生しました', 'ハートつける');
                                    }
                                },
                                error: function () {
                                    toastr.error('エラーが発生しました', 'ハートつける');
                                }
                            })
                        }
                    });
                    if (status === 'update') {
                        $project_video_component.find('.show-comment-dom').trigger('click');
                    }
                    if (status === 'new' && variation_id !== -1 && variation_page === variation_current) {
                        if ($(this).parents('.video-item-list').attr('data-variation-id') === variation_id) {
                            if(count === 0){
                                $project_video_component.find('.show-comment-dom').trigger('click');
                            }
                            count ++;
                        }
                    }
                    $project_video_component.find('.version-tag').on('click', function () {
                        handleClickVersion($(this));
                    });
                });
                count =0
            });

            project_id_load_more = project_id;
            project_item_load_more = $project_item;
            ajax_calling = false;
            if(variation_current < variation_page) {
                variation_current ++;
                page++;
                load_videos(project_id, $project_item, status, variation_id, scene_id, variation_page);
            }else if (variation_current > variation_page) {
                variation_current = 0
            }else {
                if(!ajax_calling && status === 'update' && variation_id !== 1){
                    if (scene_id !== -1) {
                        $("#"+scene_id).ready(function(){
                            setTimeout(function () {
                                let top_length = $("#"+scene_id).offset().top;
                                $('html, body').animate({
                                    scrollTop: top_length - 100
                                }, top_length/3);
                            }, 1000)
                        })
                    }
                }
            }

            hide_when_video_playing(target_dom);

            if (response.role === 'admin') {
                initAdminNewUpload();
                $('datalist')[0].innerHTML = response.choices;

                if (status === 'new') {
                    project.find('a.button-upload-video-scene-title').get(0).click();
                }
            }
            if (response.role === 'admin' && show_product_comment){
                project.find('.file-upload-owner').trigger('click');
            }
        },

        complete:function(data){
            $(".project-item__content").find(".loader").hide();
        }
    });
}

function resetLoadmore() {
    project_id_load_more = false;
    project_item_load_more = false;
    total_page = 1;
    page = 1;
    ajax_calling = false;
}

function setOpenModalNewScene(scene_id) {
        stop_video_audio();
        // let scene_id = $(this).parents('.cscene.cscene--video').find('.cscene__variation.slick-current').first().data('scene-id');
        let video_modal = $('#modal-upload-scene');
        $.ajax({
            method: "GET",
            url: "/top/get_video_modal?scene_id=" + scene_id,
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                video_modal.find('.modal-body').empty();
                video_modal.find('.modal-body').html(response.html);
                // SoundcheckProject.videoModal();
                video_modal.find('.modal-dialog__header__text h1').text('シーンの編集');
                video_modal.attr('product-scene-id', response.product_scene_id);
                video_modal.attr('product-scene-val', response.product_scene_val);
                video_modal.attr('data-scene-title-id', response.scene_title_id);
                video_modal.modal({
                    backdrop: false
                });
                video_modal.modal('show');
                let countIndex = $('.scene-take-container').length + 1;
                if(countIndex > 1) {
                    $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('テイク'+ countIndex +'を追加');
                } else {
                    $('label[for="create-scene-upload-dropzone"] .contract__form-label').text('ファーストテイクを追加');
                }

                // video_modal.on('hidden.bs.modal', function () {
                //     if($('#processingSceneModal')[0].style.display === 'block') {
                //         $('body').addClass('modal-open');
                //     }
                // });
            },
            error: function (response) {
                toastr.error(response.responseJSON.message);
            }
        });
}


function show_modal(project_item, project_id, variation_id = -1) {
    $('.project-tab-progress.active, .collection-wrap').on('click', '.project-delivery-item-content .cvideo__thumb', function (e) {
        if (isScrolling){
            return;
        }
        // if($(this).find('.thumb-schedule-video').hasClass('view_only')) {
        //     return;
        // }
        stop_video_audio();
        if($(this).attr('out-of-date-schedule')) {
            let scene_id = $(this).parents('.project-delivery-item-content').attr('data-scene-id');
            let product_scene_id = $(this).parents('.pd-chapter').attr('data-product-scene-id');
            let scene_title_id = $(this).parent().attr('data-scene-title-id');
            let name_product_scene = $('#product_scene option[data-value^=' + product_scene_id + ']').attr('data-name');

            // $('#modal-upload-scene').modal('show')
            $('#modal-upload-scene').attr('action-type', 'edit');
            $('#modal-upload-scene').attr('data-scene-id', scene_id);
            $('#modal-upload-scene').attr('product-scene-id', product_scene_id);
            $('#modal-upload-scene').attr('product-scene-val', name_product_scene);
            $('#modal-upload-scene').attr('data-scene-title-id', scene_title_id);

            return setOpenModalNewScene(scene_id);
        }
        let url_string;
        if ($(this).parents('.project-tab-progress.active').length) {
            let scene_id = $(this).parents('.project-delivery-item-content').attr('data-scene-id');
            url_string = location.protocol + '//' + location.host + location.pathname + '/scene/' + scene_id;
        } else {
            url_string = $(this).attr('data-link')
        }
        if(window.location.href.includes('collection/get_bookmarked')) {
            window.open(url_string, '_blank')
        } else {
            let chapter_active = $('.filter-item-project.item-chapter-active');
            if (chapter_active.length > 0){
                let chapter_id = chapter_active.attr('data-ps-id');
                url_string = url_string + '?chapter_id=' + chapter_id;
            }
            window.location = url_string;
        }
    });

    $('.project-tab-progress.active, #tabs-1').on('click', '.project-delivery-item-content .cvideo__title', function (e) {
        e.stopPropagation();
        if ($(this).parents('.project-tab-progress.active').hasClass('.cannot-check')) {
            return
        }
        let target = $(e.target);
        let action_element = target.parents(".cvideo__heading").find(".scene-title__action");
        if (action_element.hasClass("active")) {
            action_element.removeClass("active");
        } else {
            action_element.addClass("active");
        }
    });

    $('.project-tab-progress.active, #tabs-1').on('mouseenter mouseleave', '.project-delivery-item-content .cvideo__title, .project-delivery-item-content .scene-title__action, .project-delivery-item-content .cvideo__heading', function (e) {
            e.stopPropagation();
            if ($(this).parents('.project-tab-progress.active').hasClass('.cannot-check')) {
                return
            }
            let target = $(e.target);
            let action_element = target.parents(".cvideo__heading").find(".scene-title__action");
            if (action_element.hasClass("active") && e.type === 'mouseleave') {
                action_element.removeClass("active");
            }
            action_element.toggleClass("active-hover", e.type === 'mouseenter');
        });

    $(document).on('click', '.cvideo__heading .scene-title__edit', function (e) {
        e.preventDefault();
        e.stopPropagation();
        if (!$('#edit-scene-title').length) {
            let title = $(this).parents(".cvideo__heading").find(".cvideo__title");
            let value = title.text();
            let scene_title_id = title.attr("data-scene-title-id");
            var scene_title = $(e.target).parents(".cvideo__heading").find(".scene-title__action");
            bootbox.confirm({
                message: `<form id='edit-scene-title' action='' class="bootbox-edit-title form-group">\
                          <label>シーン名</label>
                          <input class="form-control" type='text' name='scene_title_name' value='${value}'/>
                      </form>`,
                buttons: {
                    confirm: {
                        label: '更新',
                        className: 'btn btn--primary bootbox-edit-accept'
                    },
                    cancel: {
                        label: 'キャンセル',
                        className: 'btn btn--tertiary bootbox-edit-cancel'
                    }
                },
                callback: function (result) {
                    let new_name = $("#edit-scene-title").find("input").val().trim();

                    if (new_name == value) {
                        scene_title.removeClass('active');
                        return;
                    }
                    if (result) {
                        if (new_name == "") {
                            toastr.error('シーン名を入力してください。');
                            return false;
                        } else {
                            $.ajax({
                                type: "POST",
                                url: "/ajax/edit_scene_video_name/",
                                data: {
                                    'name': new_name,
                                    'title_id': scene_title_id
                                },
                                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                                    if (response.code === '200') {
                                        $('.project-delivery-item-content[data-scene-title-id=' + scene_title_id + ']').find('.cvideo__title').text(new_name);
                                        scene_title.removeClass('active');
                                        // toastr.success('シーン名を更新しました。');
                                    } else {
                                        toastr.error('エラーが発生しました', 'シーン名を編集');
                                    }
                                },
                                error: function () {
                                    toastr.error('エラーが発生しました', 'シーン名を編集');
                                }
                            })
                        }
                    } else {
                        scene_title.removeClass('active');
                    }
                }
            });
            $("#edit-scene-title").parents(".bootbox-confirm").attr("style", 'display: flex; align-items: center;');
        }
    });

    $(document).on('click', '.cvideo__heading .scene-title__delete', function (e) {
        let scene_title_id = $(this).parents(".cvideo__heading").find(".cvideo__title").attr("data-scene-title-id");
        var scene_title = $(e.target).parents(".cvideo__heading").find(".cvideo__title");
        bootbox.confirm({
            message: "本当に削除しますか?",
            buttons: {
                confirm: {
                    label: 'はい',
                    className: 'btn--tertiary btn-delete-message'
                },
                cancel: {
                    label: 'いいえ',
                    className: 'btn--primary btn-cancel-message'
                }
            },
            callback: function (result) {
                if (result === true) {
                    $.ajax({
                        type: "POST",
                        url: "/ajax/delete_scene_video/",
                        data: {
                            'title_id': scene_title_id
                        },
                        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                            if (response.code === '200') {
                                $('.cvideo__title[data-scene-title-id^=' + scene_title_id + ']').parents('.cvideo').remove();
                                countSceneUpdate(data);
                                // toastr.success('シーンを削除しました。');
                            } else {
                                toastr.error('エラーが発生しました', 'シーンを削除');
                            }
                        },
                        error: function () {
                            toastr.error('エラーが発生しました', 'シーンを削除');
                        }
                    })
                }else{
                    scene_title.trigger("click");
                }
            }
        })
    });
}

function loadSceneDetail(scene_id) {
    let active_offer = $('.pd-scene-title-detail .pd-comment');
    $.ajax({
        type: "GET",
        url: "/top/get_message_scene_title",
        data: {
            "scene_id": scene_id
        },
        beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
            $(".loader").show();
            $('.pd-scene-title-detail').removeClass('hide');
            $('.pd-scene-title-detail').find('.pd-scene').html('');
            $('.pd-scene-title-detail').find('.pd-comment').html('');
            $('.pd-scene-title-detail').find('.pd-section-file .pd-file-content').remove();
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            window.scrollTo(0, 0);
            $('.pd-scene-title-detail').attr('data-scene-title-id', response.scene_title_id);
            $('.pd-scene-title-detail').find('.pd-comment').html(response.html);
            $('.tab--video-progress').addClass('hide');
            $('.project-chapter-item-search').addClass('hide');
            $('.mcomment-top').hide();
            setTimeout(function () {
                $('.pd-file-heading').trigger('click');
                $('.loader').hide();
            }, 100);

            commentInput();
            //previewFile();
            createMessage(active_offer);
            editMessage(active_offer);
            resolveComment(active_offer);

            newWavesurferInit();
            projectToggleResolved();
            active_offer.on('click', '.video-pin-time:not(.pin-time-audio), .video-comment-audio-title', function (e) {
                //e.stopPropagation();
                let scene_id = $(this).parents('.s-audio').data('scene-id');
                if (scene_id !== 'None') {
                    let pin_time = $(this).parent().find('.video-pin-start');
                    let target_pin = $(this).parent().find('.s-audio-control');
                    let start_time = TimeToSeconds(pin_time.text());
                    if (isNaN(start_time)) {
                        start_time = 0;
                    }

                    goToSceneActive(scene_id);

                    let video_item_list = $(this).parents('.pd-scene-title-detail').find('.cscene__variation');

                    let video_item_component = video_item_list.find('.cscene__version[data-scene-id=' + scene_id + ']').eq(0);

                    let video = video_item_component.find('.ccscene__thumb video').get(0);
                    let wave;
                    if (!video) {
                        wave = video_item_component.find('.cscene__version-horizontal .s-audio--audio-wave');
                    }

                    if ($(this).siblings(".s-audio-source").length !== 0) {
                        let wave_index = $(this).parents('.s-audio').data('wavesurfer');
                        let scene_id = $(this).parents('.s-audio').data('scene-id');
                        let pin_time = $(this).parent().find('.video-pin-start');
                        let target_pin = pin_time.parents().find('.s-audio-control');

                        let start_time = TimeToSeconds(pin_time.text());
                        if (isNaN(start_time)) {
                            start_time = 0;
                        }
                        if (video) {
                            playPinWavesurfer(wavesurfer_arr[wave_index], video, start_time, target_pin);
                        } else {
                            let audio = video_item_component.find('.s-audio--audio-wave');
                            let scene_pin = video_item_component.find('.video-item-component-content .video-item-component-content-video .pin-time-audio');
                            let scene_index = parseInt(video_item_component.find('.s-audio').attr('data-wavesurfer'));
                            let scene_wavesurfer = wavesurfer_arr[scene_index];
                            if (audio.length > 0) {
                                playWavesurferWavesurfer(scene_wavesurfer, wavesurfer_arr[wave_index], start_time, target_pin, scene_pin);
                            }
                        }

                    } else {
                        if (video) {
                            if (target_pin.hasClass('active')) {
                                stopVideo(video)
                            } else {
                                setTimeout(function () {
                                    target_pin.addClass('active');
                                }, 300);
                                playVideo(video, start_time);
                                $(video).on('pause', function () {
                                    target_pin.removeClass('active');
                                })
                            }
                        } else if (wave) {
                            let scene_index = wave.attr('data-wavesurfer');
                            let wavesurfer = wavesurfer_arr[scene_index];
                            if (target_pin.hasClass('active')) {
                                stop_video_audio();
                            } else {
                                setTimeout(function () {
                                    target_pin.addClass('active');
                                }, 300);
                                if(!wavesurfer.loaded) {
                                    let src = $(wavesurfer.mediaContainer).siblings('.s-audio-source');
                                    let link = src.attr('data-link');
                                    wavesurfer.load(link);

                                    wavesurfer.on('waveform-ready', function () {
                                        playWavesurfer(wavesurfer, start_time);
                                        wavesurfer.on('pause', function () {
                                            target_pin.removeClass('active');
                                        })
                                    })
                                } else {
                                    playWavesurfer(wavesurfer, start_time);
                                    wavesurfer.on('pause', function () {
                                        target_pin.removeClass('active');
                                    })
                                }
                            }
                        }
                    }
                }
            });

            sScrollbarBottom();

            seenComment('scene');

            active_offer.on('click', '.mcomment-input-text, .mcomment-bottom', function () {
                seenComment('scene');
            });

            let message = active_offer.find('.s-text, .s-audio-text, .s-filetext');
            $.each(message, function (i, v) {
                if (!$(v).is('.align-center')) {
                    let regex = /(?:(?:https?|http|ftp):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:;,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!;:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
                    v.innerHTML = v.innerHTML.replace(regex, "<a target='_blank' href=$&>$&</a>");
                }
            });

            let refresh = window.location.pathname + '?tab=progress';
            refresh = refresh + '&variation=' + response.variation_id;
            window.history.pushState({path: refresh}, '', refresh);
        }
    });

    $.ajax({
        type: "GET",
        url: "/top/get_scenes_of_title_by_scene",
        data: {
            "scene_id": scene_id
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            $(".loader").hide();
            $('.pd-scene-title-detail').find('.pd-scene').append(response.html);

            let $project_video_item = $('.pd-scene-title-detail');

            actionBottomVideo($project_video_item);

            setActionForProjectItem($project_video_item, scene_id);

            hide_when_video_playing($('.pd-scene-title-detail'));

            //resize video
            $('.pd-scene-title-detail').find('video.cscene__not-radio').on('loadeddata', function () {
                let video_dom = $(this);
                let e = $(this)[0];
                let v_width = e.videoWidth;
                let v_height = e.videoHeight;
                let v_ratio = parseFloat(v_width) / parseFloat(v_height);
                if (v_ratio < 16 / 9) {
                    video_dom.parents('.ccscene__thumb').removeClass('cscene__version-horizontal').addClass('cscene__version-vertical');
                    video_dom.removeClass('cscene__ratio-43').addClass('cscene__ratio-35');
                } else {
                    video_dom.parents('.ccscene__thumb').addClass('cscene__version-horizontal');
                    video_dom.addClass('cscene__ratio-43');
                }
                let scene_dom = $(this).parents('.cscene__version');
                let scene_id = scene_dom.attr('data-scene-id');
                let data = new FormData();
                data.append('scene_id', scene_id);
                data.append('video_width', v_width);
                data.append('video_height', v_height);
                $.ajax({
                    type: "POST",
                    datatype: "json",
                    contentType: false,
                    processData: false,
                    cache: false,
                    url: "/top/update_size_scene",
                    data: data,
                    beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                        console.log('ok')
                    },
                })
            });


            newWavesurferInit();

            projectScene();

            active_offer.find('.video-item-share, .cscene__share').off().on('click', function () {
                updateModalInfo($(this))
            });

        }
    });
}



function goToSceneActive(scene_id, type='') {
    let version = $('.cscene__version.slick-slide[data-scene-id^=' + scene_id + ']');
    let variation = version.parents('.cscene__variation');
    let version_index = version.attr('data-index');
    let variation_index = variation.attr('data-index');
    let current_variation_index = $('.cscene__variation-dot.slick-current').attr('data-index');
    let current_version_index = $('.cscene__version-dot.slick-current').attr('data-index');
    if (current_variation_index !== variation_index || current_version_index !== version_index) {
        $('#sliderHorizontalNav0').slick('slickGoTo', variation_index);
        if (type !== 'reload') {
            variation.find('.cscene__version-dot[data-index^=' + version_index + ']').trigger('click');
        }
        
        // Initialize HLS videos after slick slide change
        if (typeof window.initializeAllHLSVideos === 'function') {
            setTimeout(function() {
                window.initializeAllHLSVideos();
            }, 100);
        }
    }
}

function load_done_videos(project_id, project_item, variation_id=-1) {
    $.ajax({
        type: "GET",
        datatype: "json",
        url: "/top/get_project_done_video",
        data: {
            'project_id': project_id,
            'variation_id': variation_id
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            let target = project_item.find('.project-tab.project-tab-progress.active');
            target.empty();
            target.append(response.html);
            hover_play(project_item);
            show_modal(project_item, project_id);
            if(variation_id !== -1) {
                $('.project-delivery-item').each(function () {
                    if ($(this).find('.project-delivery-item-content').attr('data-scene-title') === response.scene_title_id) {
                        $(this).trigger('click');
                    }
                });
                $('.project-chapter-videos').each(function () {
                    $(this).find('.project-chapter-video-item').each(function () {
                        if($(this).attr('data-scene-title-id') === response.scene_title_id) {
                            $(this).trigger('click')
                        }
                    });
                });
            }
            project_item.find('.project-progress-action-btn .button').off().on('click', function () {
                clickActionButton($(this), project_id, project_item);
            });

            dragDropProcessing();

            hide_when_video_playing(target);

            initProductSceneEdit(target);

            $('.sumo-select select').each(function () {
                $(this).SumoSelect();
            });
        }
    })
}

function load_process_videos(project_id, project_item, variation_id = -1, is_done = 'false', show_product_comment = false, show_scene_detail=false, is_todo_list=false) {
    let titlePage = $('.owner-top').attr('data-title-page') + ' | HOME';
    $('title').text(titlePage);
    if ($('.pd-section.pd-section--all-video').length < 1) {
        // $('.item-project-todo-list').addClass('item-chapter-active')
        // console.log('get_project_update_video -- tat ca video process')
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/top/get_project_update_video",
            data: {
                'project_id': project_id,
                'variation_id': variation_id
            },
            beforeSend: function(xhr, settings) {
            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                $(".loader").show();
                // let target = project_item.find('.project-tab.project-tab-progress.active .tab--video-progress');
                // let targetCheckBack = target.find('.tab--video-watting_checkback');
                // let targetSectionChapter = target.find('.pd-section.pd-section--all-video');
                // let targetSectionUpdate = target.find('.pd-section.pd-section--update-video');
                // targetCheckBack.empty();
                // targetSectionChapter.remove();
                // targetSectionUpdate.remove();
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                let target = project_item.find('.project-tab.project-tab-progress.active .tab--video-progress');
                target.prepend(response.html);
                show_modal(project_item, project_id, variation_id);
                projectRating();
                hover_play(project_item);
                dragScrollHorizontal($('.pd-section__video.pd-section--update.mscrollbar'))
                if (response.role === 'admin' && show_product_comment) {
                    project_item.find('.file-upload-owner').trigger('click');
                }
                if (!localStorage.getItem('first_login')) {
                    localStorage.setItem('first_login', 'true');
                }
                let first_login = localStorage.getItem('first_login')
                if (show_scene_detail && first_login === 'true' && $('.prdt').hasClass('is-admin')) {
                    localStorage.setItem('first_login', 'false');
                    $('.pd-section.pd-section--update-video .cvideo__thumb.cvideo__thumb-list-update').first().trigger('click')
                }
                if (response.html && response.html !== "\n\n\n\n") {
                    let todo_list_block = $('.pd-section.pd-section--update-video.todo-list-item')
                    if (todo_list_block.length > 0) {
                        todo_list_block.removeClass('d-none-chapter')
                        todolist_chapter = true;
                    }
                    $('.item-project-delivery').removeClass('item-chapter-active')
                    $('.item-project-todo-list').addClass('item-chapter-active')
                }
                // if (!is_todo_list) {
                //     if ($('.pd-section.pd-section--update-video').length < 1) {
                //         console.log('vao day')
                //         $('.item-project-todo-list').addClass('hide')
                //         load_process_videos_watting_checkback();
                //     }
                // }
                if (!todolist_chapter && !delivery_chapter) {
                    let product_scene_id = $('.pd-chapter.active').first().attr('data-product-scene-id');
                    let tab_chapter_active = $(".chapter-item[data-ps-id='" + product_scene_id + "']");
                    if (tab_chapter_active) {
                        let chapter_item_active = $(".pd-chapter.active[data-product-scene-id='" + product_scene_id + "']")
                        if (chapter_item_active.length > 0) {
                            $('.chapter-block').addClass('d-none-chapter')
                            chapter_item_active.removeClass('d-none-chapter')
                        }
                        $('.filter-item-project').removeClass('item-chapter-active')
                        tab_chapter_active.addClass('item-chapter-active');
                    }
                }
            },
            error: function (e) {
                // console.log(e.responseText)
                $(".loader").hide();
            },
            complete: function (response) {
                $(".loader").hide();
                // console.log('remove class')
                // $('.filter-item-project').removeClass('item-disabled')
            }
        });

        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/top/get_project_process_video",
            data: {
                'project_id': project_id,
                'variation_id': variation_id
            },
            beforeSend: function() {
                $('.tab--video-progress').append(`<div class="load-more-loading load-progess"></div>`);
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                list_product_scene_ids = response.list_product_scene_ids;
                total_load = response.total_load;
                count_product_scene = response.count_product_scene;
                actionTabAllScene(project_item, response, variation_id);
                load_more_process_videos(project_id, project_item, response, variation_id);
                goToProductScene()
            },
        })
    }
}


function load_process_videos_watting_checkback() {
    let projectId = $('.project-item').first().data('project-id');
    let projectItem = $('.project-item');
    ajaxUpdateListWattingsFeedback(projectItem, projectId);
}

function goToProductScene() {
    const url_string = window.location.href;
    var url = new URL(url_string);
    var chapter_active = url.searchParams.get("chapter");
    if (chapter_active) {
        let product_scene_dom = $(".pd-chapter[data-product-scene-id^=" + chapter_active + "]");
        if (product_scene_dom.length > 0) {
            scrollToProductScene(product_scene_dom)
        }
        let action = setInterval(function () {
            let product_scene_dom = $(".pd-chapter[data-product-scene-id^=" + chapter_active + "]");
            if (product_scene_dom.length > 0) {
                scrollToProductScene(product_scene_dom);
                clearInterval(action);
            }
        }, 300);
    }
}


function scrollToProductScene(product_scene_dom) {
    let top_length = product_scene_dom.offset().top;
    $('html, body').animate({
        scrollTop: top_length - 100
    }, top_length / 3)
}


function load_more_process_videos(project_id, project_item, response, variation_id) {
    if (!last_load_product_scene) {
        let data = {
            'project_id': project_id,
            'variation_id': variation_id,
            'current_load': response.current_load,
            'list_ids': list_product_scene_ids.slice(2*response.current_load, 2*response.current_load+2),
            'total_load': total_load,
            'count_product_scene': count_product_scene,
        };
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/top/get_project_process_video_load_more",
            data: data,
            beforeSend: function() {
                $('.pd-chapter-list .load-more-loading').remove();
                $('.pd-chapter-list').append(`<div class="load-more-loading"></div>`)
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                $('.load-progess').remove();
                actionTabAllScene(project_item, response, variation_id);
                load_more_process_videos(project_id, project_item, response, variation_id)
                let chapterList =  $('.pd-chapter__list.mscrollbar')
                chapterList.each(function () {
                    dragScrollHorizontal($(this))
                })
            },
        })
    } else {
        const searchParams = new URLSearchParams(window.location.search);
        if (searchParams.has('chapter_id')) {
            const chapter_id = searchParams.get('chapter_id');
            $(`.filter-item-project[data-ps-id="${chapter_id}"]`).trigger('click');
        }
    }
}

function actionTabAllScene(project_item, response, variation_id) {
    let target = project_item.find('.project-tab.project-tab-progress.active .tab--video-progress');
    if (target.find('.pd-chapter-list').length > 0) {
        target.find('.pd-chapter-list').append(response.html);
        if (response.last_load) {
            $('.load-more-loading').remove();
            last_load_product_scene = true;
            if (response.role === 'admin') {
                $(`<div class="pd-add-chapter" style="display: none"><a class="pd-add-chapter__content button-upload-video-scene-title" href="#create-chapter">
                <div class="pd-add-chapter__icon"><i class="icon icon--sicon-plus"></i></div>
                <div class="pd-add-chapter__text">チャプターを追加</div>
                </a></div>`).insertAfter(target.find('.pd-chapter-list'));
            }
        }
        if (response.html_deleted != '') {
            target.append(response.html_deleted);
        }
    } else {
        target.append(response.html);
    }
    if (!$('.pd-chapter__toggle').hasClass('active')) {
        $('.pd-chapter__title').removeClass('active');
        $('.pd-chapter').removeClass('active');
        $(".pd-chapter").find('.pd-chapter__content').slideUp(300);
    }
    projectRating();
    sort_direction_check($('.tab--video-progress'));
    dragDropProcessing();
    initProductSceneEdit(target);
    hide_when_video_playing(target);
    initAdminNewUpload();
    openUploadForm();
}


function openUploadForm() {
    $('.project-tab-progress').on('click', '.pd-chapter__add', function () {
        let product_scene_id = $(this).parents('.pd-chapter').attr('data-product-scene-id');
        let name_product_scene = $('#product_scene option[data-value^=' + product_scene_id + ']').attr('data-name');
        $('#modal-upload-scene').find('.modal-dialog__header__text h1').text('シーンの作成');

        $('#modal-upload-scene').modal('show')
        $('#modal-upload-scene').attr('action-type', 'create');
        $('#modal-upload-scene .scene-list-take').empty();
        $('#modal-upload-scene').attr('product-scene-id', product_scene_id);
        $('#modal-upload-scene').attr('product-scene-val', name_product_scene);

        // $('#id-product-scene').attr('readonly', true);
        // $('#id-product-scene').val(name_product_scene);
        // if(window.location.href.split('tab=progress')[1] !== '' && $('.popup-add-chapter').css('display') === 'none') {
        //     $('.popup-add-chapter').css({'display': 'flex'})
        //     $('.popup-add-chapter').parents('.modal-container').css({'display': 'flex'});
        // }
    });

    $('.btn-create-product-scene').off('click').on('click', function e() {
        let product_scene_text = $('#create-chapter').find('.input-chapter-name').val();
        let product_id = $('#create-chapter').attr('data-project-id');
        product_scene_text = product_scene_text.trim();
        if (!$(this).hasClass('active') && product_scene_text != '' && product_id) {
            //e.preventDefault();
            $(this).addClass('active');
            $.ajax({
                type: "POST",
                url: '/top/create_product_scene_by_name',
                data:
                    {
                        'product_scene_val': product_scene_text,
                        'product_id': product_id
                    },
                beforeSend: function(xhr, settings) {
                    xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                },
                success: function (data) {
                    $('.modal-container.chapter__modal-container').css('display', 'none');
                    $('.pd-chapter-list').append(data.product_scene_html);
                    $('#product_scene').html(data.datalist_html);
                    dragDropProcessing();
                    let listChapterItem = $('.chapter-item')
                    let chapterHtml = `<li class="filter-item-project chapter-item" data-ps-id="${data.product_scene_id}">${data.product_scene_name}</li>`
                    listChapterItem.last().after(chapterHtml)
                    $('.chapter-item').last().trigger('click')
                },
                error: function() {
                    toastr.error('エラーが発生しました', 'シーン並び替え');
                },
                complete: function () {
                    $('.btn-create-product-scene').removeClass('active');
                }
            })

        }
    })
}

function likeVideo(event, target) {
    event.stopPropagation();
    let parent = target.parents('.project-chapter-video-item');
    let scene_id = parent.find('.project-chapter-video-item-content').data('scene-id');
    if (parent.length < 1) {
        scene_id = target.parents('.project-delivery-item-content').attr('data-scene-id');
    }
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/scene/rating_scene",
        data: {
            'scene_id': scene_id,
            'value': 5
        },
        success: function (data) {
            if (data.status === 'success') {
                // toastr.success('評価しました!', '好き');
            } else {
                toastr.error('エラーが発生しました', '好き');
            }
        },
        error: function () {
            toastr.error('エラーが発生しました', '好き');
        }
    })
}

function unlikeVideo(event, target) {
    event.stopPropagation();
    let parent = target.parents('.project-chapter-video-item');
    let scene_id = parent.find('.project-chapter-video-item-content').data('scene-id');
    if (parent.length < 1) {
        scene_id = target.parents('.project-delivery-item-content').attr('data-scene-id');
    }
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/scene/rating_scene",
        data: {
            'scene_id': scene_id,
            'value': 1
        },
        success: function (data) {
            if (data.status === 'success') {
                // toastr.success('評価しました!', '好きじゃない');
            } else {
                toastr.error('エラーが発生しました', '好きじゃない');
            }
        },
        error: function () {
            toastr.error('エラーが発生しました', '好きじゃない');
        }
    })
}

function clickActionButton(target, project_id, project_item, variation_id=-1) {
    let export_acr_el = target.parents('.project-item__content').find('#export-acr');
    let export_comment_el = target.parents('.project-item__content').find('#export-comment');
    if (export_acr_el.length > 0) {
        export_acr_el = export_acr_el.first();
    }
    if (export_comment_el.length > 0) {
        export_comment_el = export_comment_el.first();
    }
    let export_url = new URL(export_acr_el.attr('export-url'), window.location.origin);
    let export_comment_url = new URL(export_comment_el.attr('export-url'), window.location.origin);
    if (export_url.searchParams.has('status')) {
        export_url.searchParams.delete('status');
        export_acr_el.attr('export-url', export_url.toString())
    }

    if (export_comment_url.searchParams.has('status')) {
        export_comment_url.searchParams.delete('status');
        export_comment_el.attr('export-url', export_url.toString());
        export_comment_el.find('a').attr('href', export_url.toString());
    }

    if (target.parents('.project-delivery').hasClass('done')) {
        load_process_videos(project_id, project_item);
    } else if (target.parents('.project-delivery').hasClass('done-select')) {
        let download_list = [];
        let lists = target.parents('.project-tab.project-tab-progress').find('.selected');
        lists.each(function (i, e) {
            download_list.push($(e).data('id'));
        });

        if (download_list.length) {
            $.ajax({
                type: "GET",
                datatype: "json",
                url: "/top/mass_download",
                data: {
                    'list': JSON.stringify(download_list),
                },
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    if (response.status === 'success') {
                        response.url.forEach(function (e, i) {
                            setTimeout(function () {
                                window.location.href = e;
                            }, (i) * 1000)
                        })
                    } else {
                        toastr.error('エラーが発生しました');
                    }
                }
            })
        } else {
            alert("ダウンロードしたい演出を選択してください。")
        }
    }
    if (!target.parents('.project-delivery').hasClass('done') && !target.parents('.project-delivery').hasClass('done-select')) {
        if (!export_url.searchParams.has('status')) {
            export_url.searchParams.append('status', 'done');
            export_acr_el.attr('export-url', export_url.toString())
        }
        if (!export_comment_url.searchParams.has('status')) {
            export_comment_url.searchParams.append('status', 'done');
            export_comment_el.attr('export-url', export_comment_url.toString())
            export_comment_el.find('a').attr('href', export_comment_url.toString());
        }
        load_done_videos(project_id, project_item, variation_id);
    }
}

function handleBulletClick($project_video_item, min_index, max_index, target) {
    var bullet_index = target.data('index');

    target.addClass('active').siblings('.video-item-bullet').removeClass('active');
    // Do something
    let video_item_list = $project_video_item.find('.video-item-list');
    video_item_list.removeClass('active').addClass('hide');
    let old_video = video_item_list.find('.video-item-component.active');
    if (old_video.find('video').length > 0) {
        stopVideo(old_video.find('video').get(0));
    } else {
        $('.video-pin-time').removeClass('playing');
        for (i = 0; i < wavesurfer_arr.length; i++) {
            if (wavesurfer_arr[i]) {
                if (wavesurfer_arr[i].isPlaying()) {
                    wavesurfer_arr[i].playPause();
                }
            }
        }
    };
    old_video.removeClass('active');

    let new_video_item_list = $project_video_item.find('.video-item-list').eq(bullet_index);
    new_video_item_list.removeClass('hide').addClass('active');
    let new_video = new_video_item_list.find('.video-item-component.playing').first();
    if (new_video.length !== 1) {
        new_video = new_video_item_list.find('.video-item-component').first();
    }
    if (new_video.find('video').length > 0) {
        new_video.find('video').get(0).currentTime = 0;
        playVideo(new_video.find('video').get(0));
        let pins = new_video.find('video').parents('.project-video-item.show-comment').find('.video-item-comment .pin-icon-time');
        pins.each(function (i, e) {
            if ($(e).parents('.video-comment-input-pin').is('.active')) {
                $(e).html(msToTime(new_video.find('video').get(0).currentTime));
                $('.mcomment-input-title').html('<i class="icon icon--sicon-pin"></i>' + '<span>' + msToTime(new_video.find('video').get(0).currentTime) + '</span>');
            }
        });
    }

    new_video.addClass('active');

    // Prev Button Data Update
    $project_video_item.find('.video-item-bullet-prev').attr('data-current_index', bullet_index);

    if (bullet_index === min_index) {
        $project_video_item.find('.video-item-bullet-prev').addClass('disable');
    } else {
        $project_video_item.find('.video-item-bullet-prev').removeClass('disable');
    }
    // Next Button Data Update
    $project_video_item.find('.video-item-bullet-next').attr('data-current_index', bullet_index);
    if (bullet_index === max_index) {
        $project_video_item.find('.video-item-bullet-next').addClass('disable');
    } else {
        $project_video_item.find('.video-item-bullet-next').removeClass('disable');
    }
    // Update Thumbnails
    $project_video_item.find('.video-item-thumbnail').removeClass('active');
    $project_video_item.find('.video-item-thumbnail').eq(bullet_index).addClass('active');
}

function setHandleForSlider(el, slider_item) {
    setTimeout(function () {
        let elq = $(el);
        if (elq.find('.noUi-origin').length > 0) {
            elq.find('.noUi-origin').eq(1).attr('disabled', true);
            elq.find('.noUi-origin').eq(1).find('.noUi-handle.noUi-handle-upper').css('border-color', '#d9d9d9');
            elq.parent().find('.video-time-slider-end, .video-time-slider-label-end').off().on('click', function () {
                if (!elq.find('.noUi-origin').eq(1)[0].hasAttribute('disabled') || elq.find('.noUi-origin').eq(1).attr('disable') == false) {
                    elq.find('.noUi-origin').eq(1).attr('disabled', true);
                    elq.find('.noUi-origin').eq(1).find('.noUi-handle.noUi-handle-upper').css('border-color', '#d9d9d9');
                    slider_item.find('.video-time-slider-end').addClass('disabled');
                    slider_item.find('.video-time-slider-label-end').addClass('disabled');
                } else {
                    elq.find('.noUi-origin').eq(1).attr('disabled', false);
                    elq.find('.noUi-origin').eq(1).find('.noUi-handle.noUi-handle-upper').css('border-color', '#009ace');
                    slider_item.find('.video-time-slider-end').removeClass('disabled');
                    slider_item.find('.video-time-slider-label-end').removeClass('disabled');
                }
            });

            elq.parent().find('.video-time-slider-start, .video-time-slider-label-start').off().on('click', function () {
                if (!elq.find('.noUi-origin').eq(0)[0].hasAttribute('disabled') || elq.find('.noUi-origin').eq(0).attr('disabled') == false) {
                    elq.find('.noUi-origin').eq(0).attr('disabled', true);
                    elq.find('.noUi-origin').eq(0).find('.noUi-handle.noUi-handle-lower').css('border-color', '#d9d9d9');
                    slider_item.find('.video-time-slider-start').addClass('disabled');
                    slider_item.find('.video-time-slider-label-start').addClass('disabled');
                } else {
                    elq.find('.noUi-origin').eq(0).attr('disabled', false);
                    elq.find('.noUi-origin').eq(0).find('.noUi-handle.noUi-handle-lower').css('border-color', '#009ace');
                    slider_item.find('.video-time-slider-start').removeClass('disabled');
                    slider_item.find('.video-time-slider-label-start').removeClass('disabled');
                }
            });

        }
    }, 500);
}

function calculateCarouselButton($project_video_item, max_index, current_index, new_index, translate) {
    $(this).attr('data-current_index', new_index);
    // Do something
    $project_video_item.find('.video-item-list').removeClass('active');
    $project_video_item.find('.video-item-list').eq(new_index).addClass('active');
    // Update Bullets
    $project_video_item.find('.video-item-bullet').removeClass('active');
    $project_video_item.find('.video-item-bullet').eq(new_index).addClass('active');

    if ($project_video_item.find('.video-item-bullet').length > show_items) {
        translate += (current_index - new_index)*20;
        $project_video_item.find('.video-item-bullet').css('transform', 'translateX(' + translate + 'px)');
    }

    // Update Button Index
    $project_video_item.find('.video-item-bullet-prev, .video-item-bullet-next').attr('data-current_index', new_index);

    // Update Thumbnails
    $project_video_item.find('.video-item-thumbnail').removeClass('active');
    $project_video_item.find('.video-item-thumbnail').eq(new_index).addClass('active');

    $project_video_item.find('.video-item-bullet').eq(new_index).trigger('click');
    return translate;
}

function load_more() {
    if ($(window).scrollTop() > $(document).height() - $(window).height() - 500) {
        if (project_id_load_more && page < total_page && !ajax_calling) {
            page++;
            load_videos(project_id_load_more, project_item_load_more, current_tab);
        }
    }
}

function uploadProduction(target) {
    let parent = target.parents('.project-delivery-item-content');
    let scene_title_id = parent.attr('data-scene-title-id');
    if (!parent.length) {
        scene_title_id = target.parents('.project-chapter-video-item').data('scene-title-id');
    }

    bootbox.confirm({
        title: "納品をアップロードする",
        message: "<input type='file' class='upload-production' data-scene-id='" + scene_title_id + "'/>",
        buttons: {
            confirm: {
                label: 'アップロード',
                className: 'btn-success'
            },
            cancel: {
                label: 'キャンセル',
                className: 'btn-danger'
            }
        },

        callback: function (result) {
            if (result) {
                let upload_button_wrapper = $(".upload-final-product-file");
                let input = $('input.upload-production');
                if (input.length && input[0].files.length) {
                    let data = new FormData();
                    let file = input[0].files[0];
                    data.append('production_file', file);
                    data.append('scene_title_id', scene_title_id);
                    $.ajax({
                        type: "POST",
                        url: "/scene/upload_production",
                        data: data,
                        cache: false,
                        processData: false,
                        contentType: false,
                        beforeSend: function(data) {
                            if(file) {
                                // toastr.info('アップロード中…');
                                upload_button_wrapper.css('display', 'flex');
                                upload_button_wrapper.addClass('clicked');
                                upload_button_wrapper.find('.fill .process').css('width', '2%');
                            } else {
                                // toastr.info('コメント作成しています。');
                            }
                        },
                         xhr: function () {
                            var xhr = new window.XMLHttpRequest();
                            if (file) {
                                xhr.upload.addEventListener("progress", function (evt) {
                                    if (evt.lengthComputable) {
                                        let percentComplete = (evt.loaded / evt.total) * 70;
                                        upload_button_wrapper.find('.fill .process').css('width', percentComplete + '%');
                                    }
                                }, false);
                            }
                            return xhr;
                        },
                        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                            upload_button_wrapper.find('.fill .process').css('width', '100%');
                            setTimeout(function () {
                                upload_button_wrapper.removeClass('clicked').addClass('success')
                            }, 1000);
                            setTimeout(function () {
                                upload_button_wrapper.removeClass('success').css('display', 'none');
                                upload_button_wrapper.find('.fill .process').css('width', '0');
                            }, 2000);

                            if (response.status === '200') {
                                // toastr.success('納品がアップロードしました。', '納品を送る');
                                // parent.remove();
                            } else {
                                toastr.error('エラーが発生しました', '納品を送る');
                            }
                        },
                        error: function (data) {
                            toastr.error('エラーが発生しました');
                            upload_button_wrapper.removeClass('clicked');
                            upload_button_wrapper.css('display', 'none');
                            upload_button_wrapper.find('.fill .process').css('width', '0');
                        }
                    })
                } else {
                    toastr.error('納品ファイルを選択してください。', '納品を送る');
                    target.trigger('click');
                }
            }
        }
    });

}

function undoneVideo(target, type=null) {
    let parent = target.parents('.project-chapter-video-item, .project-delivery-item, .project-video-item, .project-delivery-item-content, .pd-scene-title-detail');
    var scene_title_id;
    if (parent.is('.project-chapter-video-item')) {
        scene_title_id = parent.data('scene-title-id');
    } else if (parent.is('.project-video-item')) {
        scene_title_id = parent.data('scene-title-id');
    } else if (parent.is('.project-delivery-item-content')) {
        scene_title_id = parent.data('scene-title-id');
    } else if (parent.is('.pd-scene-title-detail')) {
        scene_title_id = parent.data('scene-title-id');
    }
    else {
        scene_title_id = parent.find('.project-delivery-item-content').data('scene-title');
    }
    let has_process_video = 0;
    let has_update_video = 0;
    let projectID = target.parents('.container').find('.project-item').attr('data-project-id');
    if($(".pd-section--update-video").length){
        has_update_video = 1;
    }
    if($(".pd-section--delivery-video").length){
        has_process_video = 1;
    }
    let project_id = $('.project-item').data('project-id');
    let scene_id = parent.data('scene-id');
    if(!scene_id){
        scene_id = parent.find('.cscene__variation').data('scene-id');
    }
    $.ajax({
        type: "POST",
        datatype: "json",
        url: "/scene/undone",
        data: {
            'scene_title_id': scene_title_id,
            'has_process_video': has_process_video,
            'has_update_video': has_update_video,
            'project_id': project_id,
            'scene_id': scene_id,
            'type': type
        },
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            if (response.status === '200') {
                getProjectSettingDetail(projectID);
                // toastr.success('検収を戻しました');
                $('#processingSceneModal').modal('hide');

                let scene_update_html = response['scene_update_html'];
                let list_update_html = response['list_update_html'];
                let scene_process_html = response['scene_process_html'];
                let list_process_html = response['list_process_html'];
                if(scene_update_html){
                    $('.pd-section--update-video .pd-section__video').prepend(scene_update_html);
                }
                if(scene_process_html){
                    $('.pd-section--delivery-video .pd-section__video').prepend(scene_process_html);
                }
                if(list_update_html){
                    $('.tab--video-progress.tab--video-all').prepend(list_update_html);
                }
                if(list_process_html){
                    if($(".pd-section--update-video").length){
                        $(list_process_html).insertAfter($(".pd-section--update-video"));
                    }else{
                        $('.tab--video-progress.tab--video-all').prepend(list_process_html);
                    }
                }
                $(`.project-delivery-item-content[data-scene-title-id=${scene_title_id}]`).each(function (index, item) {
                    let icon = $(item).find('.icon--sicon-heart');
                    icon.addClass("project-chapter-video-done icon--sicon-heart-o");
                    if (response.role === 'admin') {
                        icon.addClass('cannot-check')
                    }
                    icon.removeClass("project-chapter-video-undone icon--sicon-heart");
                    //update rating
                    let star = $(item).find(".stars");
                    let average_star = parseFloat(star.attr("data-rating"));
                    if(average_star > 0){
                        star.addClass("selected");
                        let number_star = "star-" + String(average_star);
                        star.find("a").removeClass("active");
                        star.find("." + number_star).addClass("active");
                        star.attr("data-rating", average_star);
                    }else{
                        star.removeClass("selected");
                        star.find("a").removeClass("active");
                    }
                });
                projectRating();
                countSceneUpdate(response);
            } else {
                toastr.error('エラーが発生しました', '進行中に戻す');
            }
        },
        error: function () {
            toastr.error('エラーが発生しました', '進行中に戻す');
        }
    })
}

function add_flag_to_hide_video_on_close(target) {
    if(target.parents('.project-tab.project-tab-new.active')) {
        target.addClass('remove_on_close');
    }
}

function processBullet(max_index, $project_video_item) {
    let current_index = $project_video_item.find('.video-item-bullet.active');
    if (current_index.length) {
        current_index = current_index.eq(0).index();
    } else {
        current_index = $project_video_item.find('.video-item-bullet').eq(0);
        current_index.addClass('active');
        current_index = 0;
    }

    let min_show = Math.ceil((max_index - 9)/2) + 1;
    let max_show = min_show + 9 - 3;

    let translate = 0;
    if ((min_show - current_index) > 0) {
          translate = (min_show - current_index)*20;
    } else if ((max_show - current_index) < 0) {
        translate = (max_show - current_index)*20;
    }
    if(max_index > 9 && (current_index < min_show || current_index > max_show)) {
        $project_video_item.find('.video-item-bullet').css('transform', 'translateX('+ translate +'px)')
    }
    $project_video_item.find('.video-item-bullet-prev').attr('data-current_index', current_index);
    $project_video_item.find('.video-item-bullet-next').attr('data-current_index', current_index);
    $project_video_item.find('.video-item-bullet-prev').removeClass('disable');
        $project_video_item.find('.video-item-bullet-next').removeClass('disable');
    if(current_index === 0) {
        $project_video_item.find('.video-item-bullet-prev').addClass('disable');
    } else if (current_index === max_index) {
        $project_video_item.find('.video-item-bullet-next').addClass('disable');
    }
    return translate;
}

function load_tab($project_item, project_id, tab_active, variation_id=-1, scene_id=-1, variation_page=1, is_done='false', show_product_comment=false) {
    let show_scene_detail = false;
    let segmentItemNotResolved = $('.segment-navbar .item-not-resolved')
    if ($project_item.find('.pbanner').attr('data-project-role') === 'creator' || $('.block-navigation-bar').hasClass('hide')) {
        tab_active = 'messenger';
    }
    if ($('.project-item.active').find('.block-navigation-bar').hasClass('hide')) {
        if ($('.pbanner').attr('data-project-role') !== 'owner') {
            $('.project-item__content').addClass('hide');
        }
        $('.btn-tutorial-pc').addClass('hide')
    }
    $project_item.find('.pbanner-tab').removeClass('active');
    $project_item.find('.pbanner-tab[data-show='+ tab_active+']').addClass('active');
    $project_item.find('.project-tab').removeClass('active');
    //$project_item.find('.project-tab .project-item__video-list').html('');
    $project_item.find('.project-tab-' + tab_active).addClass('active');
    hiddenElementByTab(tab_active)
    resetLoadmore();
    let leftSidebarModal = $('#left-sidebar');
    leftSidebarModal.css('display', 'none')
    if (tab_active === 'progress') {
        $('.balance-wallet-icon').removeClass('show');
        $('.show-file-icon').removeClass('show');
        leftSidebarModal.css('display', 'initial')
        show_scene_detail = false

        let noti = parseInt($('.pbanner-tab[data-show="' + tab_active + '"] .number-notification').text())
        if(noti && noti == 1 && first_open) {
            show_scene_detail = true
        }
        load_process_videos(project_id, $project_item, variation_id, is_done, show_product_comment, show_scene_detail);
        load_process_videos_watting_checkback();
        showToastCloseButton();
        current_tab = 'progress';
    } else if (tab_active === 'product-comment') {
        $('.switch-talkroom').removeClass('hide')
        $('.balance-wallet-icon').removeClass('show');
        $('.show-file-icon').addClass('show');
        current_tab = 'product-comment';
        get_comment((project_id))
    } else if (tab_active === 'messenger') {
        $('.switch-dm').removeClass('hide')
        $('.balance-wallet-icon').addClass('show');
        $('.show-file-icon').removeClass('show');
        current_tab = 'messenger';
        const url_string = window.location.href;
        let url = new URL(url_string);
        let offer_active = url.searchParams.get("offer");
        show_offer = false
        let noti = parseInt($('.pbanner-tab[data-show="' + tab_active + '"] .number-notification').text())
        if(noti && noti == 1 && first_open) {
            show_offer = true
        }
        get_messenger_artist(project_id, offer_active, null, show_offer)
    }

    changePlaceHolderSearch();

    $project_item.find('.project-item__filter-item, .pbanner-tab').off().on('click', function (e) {
        e.preventDefault();
        stop_video_audio();
        let tab_item = $(this).data('show');
        removeSearch();
        $('.custom-switch-new').addClass('hide')
        if ('progress,product-comment,messenger'.includes(tab_item)) {
            let nav_top_bar = $('.navigation-top-app-bar');
            if (nav_top_bar.length > 0 && nav_top_bar.hasClass('hide-top-bar')) {
                nav_top_bar.removeClass('hide-top-bar');
            }
            if (!$(this).hasClass('active')) {
                $project_item.find('.pbanner-tab').removeClass('active');
                $(this).addClass('active');
                $project_item.find('.project-tab').removeClass('active');
                $project_item.find('.project-tab-' + tab_item).addClass('active');
                window.stop();
                let segmentNavbar = $('.segment-navbar');
                let leftSidebar = $('#left-sidebar-open');
                let leftSidebarModal = $('#left-sidebar');
                // let actionPanelHead = $('.action-panel-head');
                let balanceWallet =  $('.balance-wallet-icon');
                let showFileIcon =  $('.show-file-icon');
                // let offerBlockLeft = actionPanelHead.find('.offer-block-left');
                // offerBlockLeft.addClass('hidden-el');
                let query_params = '';
                let budget_log_sidebar = $('#budgetLogSidebar');
                switch (tab_item) {
                    case 'progress':
                        if (!budget_log_sidebar.hasClass('d-none-sb')){
                            budget_log_sidebar.addClass('d-none-sb');
                        }
                        balanceWallet.removeClass('show');
                        showFileIcon.removeClass('show');
                        load_process_videos(project_id, $project_item);
                        current_tab = 'progress';
                        segmentNavbar.addClass('hidden-el');
                        leftSidebar.removeClass('hidden-el');
                        leftSidebarModal.css('display', 'initial')
                        showToastCloseButton();
                        break;
                    case 'product-comment':
                        $('.switch-talk-room').removeClass('navbar-active')
                        $('.switch-talk-room.text-left').addClass('navbar-active')
                        if ($('.switch-checkbox-tr:checked').length > 0){
                            $('.switch-checkbox-tr:checked').trigger('click')
                        }
                        if (!budget_log_sidebar.hasClass('d-none-sb')){
                            budget_log_sidebar.addClass('d-none-sb');
                        }
                        $('.switch-talkroom').removeClass('hide')
                        balanceWallet.removeClass('show');
                        showFileIcon.addClass('show');
                        $('.tab--messenger-artist').empty();
                        get_comment(project_id);
                        current_tab = 'product-comment';
                        segmentNavbar.removeClass('hidden-el');
                        leftSidebar.addClass('hidden-el');
                        leftSidebarModal.css('display', 'none')
                        $('.toast').remove();
                        break;
                    case 'messenger':
                        $('.switch-dm').removeClass('hide')
                        balanceWallet.addClass('show');
                        showFileIcon.removeClass('show');
                        $('.project-tab-product-comment').empty();
                        get_messenger_artist(project_id);
                        current_tab = 'messenger';
                        segmentNavbar.removeClass('hidden-el');
                        leftSidebar.addClass('hidden-el');
                        leftSidebarModal.css('display', 'none')
                        // offerBlockLeft.removeClass('hidden-el');
                        $('.toast').remove();
                        break;
                }
                // let refresh = window.location.pathname + '?tab=' + current_tab + query_params
                let refresh = window.location.pathname + '?tab=' + current_tab
                window.history.pushState({path: refresh}, '', refresh);
            } else if (tab_item === 'messenger' && $('.psearch-main').length) {
                $('.project-tab-product-comment').empty();
                get_messenger_artist(project_id, null, 'waiting');
                current_tab = 'messenger';
                $('.switch-dm').removeClass('hide')
            } else {
                if (tab_item === 'product-comment') {
                    $('.switch-talkroom').removeClass('hide');
                }else if (tab_item === 'messenger') {
                    $('.switch-dm').removeClass('hide')
                }
                return;
            }
        }
        changePlaceHolderSearch();
    });

    // let filterItemChapter = $('.filter-item-project')
    $(document).on('click', '.filter-item-project', function () {
        leftSidebarModal.removeClass('toggled boder-siderbar u-bg-background open-sidebar')
        $(this).addClass('item-chapter-active')
        let data_ps_id = $(this).attr('data-ps-id');
        $('.chapter-block').addClass('d-none-chapter')
        $('.filter-item-project').removeClass('item-chapter-active')
        switch (data_ps_id) {
            case 'todo-list':
                let todo_list_block = $('.pd-section.pd-section--update-video.todo-list-item')
                if (todo_list_block.length > 0) {
                    todo_list_block.removeClass('d-none-chapter')
                }
                $('.item-project-todo-list').addClass('item-chapter-active')
                break;
            case 'processing-list':
                let delivery_block = $('.tab--video-watting_checkback.processing-list-item')
                if (delivery_block.length < 1) {
                    delivery_block = $('.pd-section.pd-section--delivery-video')
                }
                if (delivery_block.length > 0) {
                    $('.filter-item-project').removeClass('item-chapter-active')
                    delivery_block.removeClass('d-none-chapter')
                }
                $('.item-project-delivery').addClass('item-chapter-active')
                console.log('case processing-list')
                break;
            default:
                let tab_chapter_active = $(".chapter-item[data-ps-id='" + data_ps_id + "']");
                if (tab_chapter_active) {
                    let chapter_item_active = $(".pd-chapter.active[data-product-scene-id='" + data_ps_id + "']")
                    if (chapter_item_active.length > 0) {
                        chapter_item_active.removeClass('d-none-chapter')
                    }
                    tab_chapter_active.addClass('item-chapter-active');
                }
                break
        }
    })
}

function optimizePreload() {
    $('video:not(:in-viewport)').each(function(i,e) {
        if(e.preload !== 'none') {
            e.preload = 'none';
        }
    });

    $('video:in-viewport').each(function(i,e) {
        if(e.preload !== 'auto') {
            e.preload = 'auto';
        }
    });
}

let hovering = false;
let hovering_id = 0;
let hover_zooming = false;
let close_full_screen = false;

function hover_play(project_item = null) {
    let zoomOutTimeout;
    $(document).on('mouseenter mouseleave', '.project-chapter-video-item-content, .project-delivery-item-content .cvideo__thumb:has(video:not([poster="/static/images/pdf-item-thumbnail.png"]))', function (e) {
        if (e.type === 'mouseenter') {
            clearTimeout(zoomOutTimeout);
            if (!hover_zooming) {
                $('video, audio').each(function (i, e) {
                    e.pause()
                })
                let video = $(this).find('video').get(0);
                video.play();
                video.removeAttribute('controls');
                video.muted = false;
                hovering = true
                let counter = hovering_id;
                close_full_screen = false;
                if ($(this).attr('data-file-type') != 'audio') {
                        if (hovering && counter === hovering_id) {
                            hover_zooming = true;
                            $(video).addClass('hover-zoom-netflix-prepare');
                            setTimeout(() => {
                                $(video).addClass('hover-zoom-netflix');
                            }, 10);
                        }
                }
            }
        } else {
            $(this).css('pointer-events', 'none')
            $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare')
            zoomOutTimeout = setTimeout(() => {
                $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-2')
                $('.hover-zoom-netflix').removeClass('hover-zoom-netflix');
                $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-3')
                $('.hover-zoom-netflix-prepare').removeClass('hover-zoom-out-netflix-prepare');
            }, 200)

            setTimeout(() => {
                $('.hover-zoom-out-netflix-prepare-2').removeClass('hover-zoom-netflix-prepare hover-zoom-out-netflix-prepare hover-zoom-out-netflix-prepare-2 hover-zoom-out-netflix-prepare-3');
            }, 10)

            setTimeout(() => {
                $(this).find('video').removeClass('hover-zoom-netflix');
                $(this).css('pointer-events', 'auto')
            }, 10)
            hover_zooming = false;
            hovering_id++;
            hovering = false;
            close_full_screen = true;
            $(this).find('video').get(0).pause();
            if (!hover_zooming && !close_full_screen) {
                hovering = false;
                hovering_id++;
                let video = $(this).find('video').get(0);
                video.pause();
                $(this).removeClass('hover-zoom-netflix hover-zoom-netflix-prepare')
            }
        }
    });

    // $(document).on('click', '.video-netflix-overlay', function() {
    //     $('.hover-zoom-netflix').removeClass('hover-zoom-netflix-center');
    //     $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare')

    //     setTimeout(() => {
    //         $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-2')
    //         $('.hover-zoom-netflix').removeClass('hover-zoom-netflix');
    //         $('.hover-zoom-netflix-prepare').addClass('hover-zoom-out-netflix-prepare-3')
    //         $('.hover-zoom-netflix-prepare').removeClass('hover-zoom-out-netflix-prepare');
    //     }, 200)

    //     setTimeout(() => {
    //         $('.hover-zoom-out-netflix-prepare-2').css({'position': 'relative', 'top': 'auto', 'left': 'auto'})
    //         $('.hover-zoom-out-netflix-prepare-2').removeClass('hover-zoom-netflix-prepare hover-zoom-out-netflix-prepare hover-zoom-out-netflix-prepare-2 hover-zoom-out-netflix-prepare-3');
    //     }, 800)

    //     hover_zooming = false;
    //     hovering_id++;
    //     hovering = false;
    //     close_full_screen = true;
    //     // $('body .video-netflix-overlay').remove();
    // })

    $(document).on('click', '.project-chapter-video-undone:not(.cannot-check)', function (e) {
        let type;
        if ($(this).parents('#tabs-1')) {
            type = 'scene_title'
        }
        e.stopPropagation();
        if ($(e.target).hasClass("project-chapter-video-done") && !$(e.target).hasClass("cannot-check")) {
            doneVideo($(this));
        } else {
            undoneVideo($(this), type);
        }
    });

    $(document).on('click', '.project-chapter-video-done:not(.cannot-check)', function (e) {
        e.stopPropagation();
        let type;
        if ($(this).parents('#tabs-1')) {
            type = 'scene_title'
        }
        if ($(e.target).hasClass("project-chapter-video-undone")) {
            undoneVideo($(this), type);
            return
        } else {
            doneVideo($(this));
        }
    });
}

function dragDropProcessing() {
    $('.project-tab-progress:not(.view_only) .pd-section.pd-section--all-video .pd-section__main .pd-section__content .pd-chapter-list').sortable({
        containment: "parent",
        items: "> div",
        handle: ".pd-chapter__title:not(.active)",
        tolerance: "pointer",
        cursor: "move",
        start: function (event, ui) {
            ui.placeholder.height(ui.helper.outerHeight());
            let start_pos = ui.item.index();
            ui.item.attr('data-start', start_pos);
        },
        change: function (event, ui) {
        },
        update: function (event, ui) {
            let index = ui.item.index();
            let start_pos = ui.item.attr('data-start');
            let before_id;
            let after_id;
            let type = '';
            after_id = ui.item.next().attr('data-product-scene-id');
            before_id = ui.item.prev().attr('data-product-scene-id');
            if (start_pos > index) {
                type = 'gt';
            } else if (start_pos < index) {
                type = 'lt';
            }

            $.ajax({
                type: "POST",
                url: "/top/update_product_scene_index",
                data: {
                    'product_scene_id': ui.item.attr('data-product-scene-id'),
                    'before': before_id,
                    'after': after_id,
                    'type': type
                },
                fail: function (data) {
                    toastr.error('エラーが発生しました', 'チャプター並び替え');
                }
            })
        }
    }).disableSelection();

    // Sort the children
    $(".pd-chapter__list").sortable({
        containment: "document",
        items: "> div, > a",
        connectWith: '.pd-chapter__list',
        handle: 'a.scene-title__move',
        cursor: "move",
        start: function (event, ui) {
            ui.placeholder.height(ui.helper.outerHeight());
            let index = ui.item.index();
            console.log(index); // Index oldContainer
        },
        change: function (event, ui) {
        },
        update: function (event, ui) {
            if (ui.sender) { // oldContainer
                let scene_title_id = ui.item.find('.project-delivery-item-content').attr('data-scene-title-id');
                let product_scene_id = ui.item.parents('.pd-chapter').attr('data-product-scene-id');
                $.ajax({
                    type: "POST",
                    url: "/top/update_scene_title_position",
                    data: {
                        'product_scene_id': product_scene_id,
                        'scene_title_id': scene_title_id
                    },
                    fail: function (data) {
                        toastr.error('エラーが発生しました', 'シーン並び替え');
                    }
                })
            }
        },
    }).disableSelection();
}

function initProductSceneEdit(target) {
    target.find('.project-chapter .edit-product-scene').off().on('click', function() {
        let product_scene_id = $(this).parents('.project-chapter-item').attr('data-product-scene-id');
        let current_name = $(this).parents('.project-chapter-title').find('.productscene-name').text().trim();
        if(product_scene_id && current_name) {
            bootbox.confirm({
                title: "チャプター名を編集",
                message: "<span>チャプター名</span>" +
                    "<input type='text' class='product-scene-new-name' style='width: 100%; margin-top: 5px;'" +
                    "data-product-scene-id='" + product_scene_id + "' value='"+ current_name +"' />",
                buttons: {
                    confirm: {
                        label: '更新',
                        className: 'btn-success'
                    },
                    cancel: {
                        label: 'キャンセル',
                        className: 'btn-danger'
                    }
                },
                callback: function (result) {
                    if (result) {
                        let input = $('input.product-scene-new-name');
                        if (input.length && input.val() !== '') {
                            $.ajax({
                                type: "POST",
                                url: "/ajax/edit_product_scene_name/",
                                data: {
                                    'name': input.val(),
                                    'product_scene_id': product_scene_id
                                },
                                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                                    if (response.code === '200') {
                                        let product_scene = $('.project-chapter-item[data-product-scene-id^='+response.product_scene_id+']');
                                        product_scene.find('.project-chapter-title .productscene-name').text(response.new_name);
                                        // toastr.success('チャプター名が更新しました。', 'チャプター名を編集');
                                    } else {
                                        toastr.error('エラーが発生しました', 'チャプター名を編集');
                                    }
                                },
                                error: function () {
                                    toastr.error('エラーが発生しました', 'チャプター名を編集');
                                }
                            })
                        } else {
                            toastr.error('チャプター名をを入力してください。', 'チャプター名を編集');
                        }
                    }
                }
            });
        }
    })

    target.find('.project-chapter .delete-product-scene').off().on('click', function() {
        let product_scene_id = $(this).parents('.project-chapter-item').attr('data-product-scene-id');
        let current_name = $(this).parents('.project-chapter-title').text().replace(/\s*$/, '');
        if(product_scene_id && current_name) {
            bootbox.confirm({
                title: "チャプター削除",
                message: "紐付いているシーンを削除しますので、本当に削除しますでしょうか?",
                buttons: {
                    confirm: {
                        label: '削除',
                        className: 'btn-danger'
                    },
                    cancel: {
                        label: 'キャンセル',
                        className: 'btn-info'
                    }
                },
                callback: function (result) {
                    if (result) {
                        $.ajax({
                            type: "POST",
                            url: "/ajax/delete_product_scene/",
                            data: {
                                'product_scene_id': product_scene_id
                            },
                            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                                if (response.code === '200') {
                                    $('.project-chapter-item[data-product-scene-id^='+response.product_scene_id+']').remove();
                                    countSceneUpdate(response);
                                    if (!response.real_delete) {
                                        $(response.html_deleted).append($('.pd-section.pd-section--deleted-video'));
                                        $('.pd-section.pd-section--all-video')
                                    } else {
                                       if ( $('.pd-section.pd-section--deleted-video .pd-chapter').length) {
                                           $('.pd-section.pd-section--deleted-video').remove();
                                       }
                                    }
                                    // toastr.success('チャプターが削除しました。', 'チャプター削除');
                                } else {
                                    toastr.error('エラーが発生しました', 'チャプター削除');
                                }
                            },
                            error: function () {
                                toastr.error('エラーが発生しました', 'チャプター削除');
                            }
                        })
                    }
                }
            });
        }
    })
}

function get_comment(project_id) {
    let titlePage = $('.owner-top').attr('data-title-page') + ' | TALKROOM';
    $('title').text(titlePage);
    if ($('.pd-product-comment').length < 1) {
        current_load_product_comment = 0;
        is_loading_product_comment = false;
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/top/get_comment",
            data: {
                'project_id': project_id,
                'type': 'project'
            },
            beforeSend: function() {
                let first_load = false;
                $('.load-more-loading').remove();
                if (!$('.psearch-main').length) {
                    first_load = true
                }
                if ($('.project-tab-product-comment').text().trim() === '') {
                    $('.project-tab-product-comment').append(`<div class="load-more-loading"></div>`);
                }
            },
            success: function (data) {
                let target = $('.project-tab.project-tab-product-comment');
                target.append(data.html);
                if (!$('.mcomment-top').hasClass('comment-top-area')){
                    $('.mcomment-top').hide();
                }
                $('.load-more-loading').remove();
                $('.pd-file-heading').trigger('click');
                $('.pd-file-heading').trigger('click');
                seenComment('project');
                initmCustomScrollbar();
                sScrollbarBottom();
                initProjectComment(target);
                commentInput();
                getDownloadAudio();
                createMessage(target);
                editMessage(target);
                resolveComment(target);
                projectToggleResolved();

                newWavesurferInit();
                projectToggleResolved();
                removeDuplicatedDate();

                target.on('click', '.mcomment-input-text, .mcomment-bottom', function () {
                    seenComment('project');
                });

                $('.mmessage').last().addClass('load-lasted-message');
                total_product_comment = parseInt(data.total_page);
                scrollProductComment();
                autoLoadMore();
                scrollListComment($('.mmessage-list'))
            },
            complete: function () {
                scrollCommentBar();
                let messages = $('.prdt .new-video-menu .mmessage');
                if (messages.length > 0) {
                    calcMoreActionComment(messages);
                }
                calcPositionDropdownComment2();
                hoverDropdownMessage()
                hoverBtnActionMessage()
                clickBtnActionMessage()
                calcMoreActionComment($('.prdt .mmessage'));
                resizeCommentInput();
            }
        });
    }
}

function show_unresolved_comment(target) {
    let target_parents = target.parents('.video-item-comment-content');
    let resolve_switch = target_parents.siblings('.video-item-comment-top').find('.button-switch input');
    let animate = !resolve_switch.is(':checked');

    if(animate) {
        target.slideDown(300);
    }
    target.removeClass('resolved');
    target.find('.video-comment-resolve')[0].innerText = '解決';
    while (target.next().is('.sub-item')) {
        target = target.next();
        if(animate) {
            target.slideDown(300);
        }
        target.find('.video-comment-resolve')[0].innerText = '解決';
        target.removeClass('resolved');
    }

}

function hide_resolved_comment(target) {
    let target_parents = target.parents('.video-item-comment-content');
    let resolve_switch = target_parents.siblings('.video-item-comment-top').find('.button-switch input');
    let animate = !
        resolve_switch.is(':checked');

    if(animate) {
        target.hide(300);
    }

    target.addClass('resolved');
    target.find('.video-comment-resolve')[0].innerText = '再開';
    while (target.next().is('.sub-item')) {
        target = target.next();
        if (animate) {
            target.hide(300);
        }
        target.find('.video-comment-resolve')[0].innerText = '再開';
        target.addClass('resolved');
    }
}

function append_placeholder_comment(is_parent, have_pin, right, data) {
    let sub_item = is_parent ? '' : ' sub-item ';
    let avatar_url = $('a.sheader-account-link img')[0].src;
    let div_audio_pin = '';
    let div_pin = '';
    let div_download_file = '';

    if (have_pin) {
        div_pin = `<div class="video-pin-time">
                <span></span>
                <div class="video-pin-start">${data.get('pin_time')}</div>
            </div>`;
    }
    if (data.get('file') !== 'undefined') {
        let file_name = data.get('file').name;
        let file_ext = file_name.match(/.[0-9a-zA-Z]{3,4}$/);
        if (file_ext && file_ext === '.mp3' || file_ext === '.wav') {
            div_audio_pin = `<div class="video-comment-audio-title">${file_name}</div>
                <div class="video-comment-audio-wave" data-audio=""></div>`
        } else {
            div_download_file = `<div class="comment__download comment__download--bottom">
                    <i class="fas fa-download"></i>
                    <a class="comment__download-icon-down">${file_name}</a>
                </div>`;
        }
    }
    return `<div class="video-comment-item-reply ${sub_item} ${right}">
        <div class="video-comment-item-reply-user">
            <div class="video-comment-user">
                <img class="video-comment-user-img mCS_img_loaded" src="${avatar_url}" alt="">
            </div>
            <div class="video-comment-time">now</div>
        </div>
        <div class="video-comment-item-reply-content-container">
            <div class="video-comment-item-reply-content">
                <div class="video-comment-reply-content">
                    <div class="video-comment-content">
                        ${div_audio_pin}
                        ${div_pin}
                        
                        <div class="video-comment-audio">
                            <div class="video-comment-text"><p>${data.get('comment')}</p></div>
                        </div>
                        
                        <div class="comment-edit-button-group">
                            <button id="edit-comment">
                                <img src="/static/images/edit.svg" alt="">
                            </button>
                            <button id="delete-comment">
                                <img src="/static/images/delete.svg" alt="">
                            </button>
                        </div>
                        <div class="video-comment-action">
                            <a class="button button--text button--text-primary video-comment-reply" href="javascript:void(0)" role="button">返信</a>
                            <a class="button button--text button--text-primary video-comment-resolve" href="javascript:void(0)" role="button">解決</a>
                        </div>
                    </div>
                </div>
            </div>
            ${div_download_file}
        </div>
    </div>`
}

function hide_when_video_playing(target) {
    target.find('video').off('play').on('play', function() {
        $(this).siblings('.show').removeClass('show');
        $(this).parents('.cscene-horizontal').find('button.slick-next, button.slick-prev').hide();
    });

    target.find('video').on('pause', function() {
        $(this).siblings('.video-button').addClass('show');
        $(this).parents('.cscene-horizontal').find('button.slick-next, button.slick-prev').show();
    });
}

function show_hide_progress(target) {
    target.find('.project-item__more-left-top').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).toggleClass('active');
    });
}

$(document).ready(function () {
    const url_string = window.location.href;
    var url = new URL(url_string);
    var project_active = url.searchParams.get("project");
    if (!project_active) {
        project_active = $('.project-item').first().data('project-id');
    }
    var tab_active = url.searchParams.get("tab");
    var is_show_comment = false;
    var active_project_el = $('.project-item[data-project-id=' + project_active + ']').first();
    var show_comment = url.searchParams.get("show_comment");

    if (tab_active === 'update') {
        tab_active = 'progress';
    } else if (!tab_active) {
        var noti = $('.pbanner-tab .number-notification')
        var has_noti_el = noti.filter(function() {
            return parseInt($(this).text()) > 0
        })
        tab_active = 'progress';
        if(has_noti_el.length == 1) {
            tab_active = has_noti_el.parents('.pbanner-tab').attr('data-show')
            let refresh = window.location.pathname + '?tab=' + tab_active;
            window.history.pushState({path: refresh}, '', refresh);
        } else if (has_noti_el.length > 1) {
            tab_active = has_noti_el.first().parents('.pbanner-tab').attr('data-show')
            let refresh = window.location.pathname + '?tab=' + tab_active;
            window.history.pushState({path: refresh}, '', refresh);
        }
        if ($('.prdt').hasClass('is-admin')){
            first_open = true
        }
    }

    var variation_active = url.searchParams.get("variation");
    var page_active = url.searchParams.get("tab");

    if(variation_active && project_active ) {
        $.ajax({
            type: "GET",
            datatype: "json",
            url: "/top/get_status_scene_title",
            data: {
                'product_id': project_active,
                'variation_id': variation_active,
                'tab_active': page_active
            },
            beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                if (response.status === 'success') {
                    project_id = project_active;
                    let refresh = window.location.pathname + '/scene/' + variation_active;
                    window.location = refresh
                } else {
                    window.location.href = '/warning/404'
                }
            }
        });
    }


    $('.project-item').each(function () {
        var $project_item = $(this);
        // Bullets var
        if (project_active && !variation_active){
            project_id = project_active;
            if ($project_item.attr('data-project-id') === project_id){
                $project_item.addClass("active");
                resetLoadmore();
                load_tab($project_item, project_id, tab_active, -1, -1, 1, 'false', is_show_comment);
            } else {
                $project_item.addClass("hide");
                $project_item.find('.project-item__title').removeClass('active');
            }
        }
        //init_project_banner_hover($project_item);
        //staff_info($project_item);
        show_hide_progress($project_item);
        $project_item.find('.project-item__general').off('click').on('click', function (e) {
            if ($project_item.is('.show-staff')) {
                e.preventDefault();
                e.stopPropagation();
                $project_item.removeClass('show-staff');
                $project_item.find('.project-item__content').removeClass('hide');
                $project_item.find('.project-item__staff').remove();
            }
        });

        $project_item.find('.project-item__setting .button').off().on('click', function () {
            $.ajax({
                type: "GET",
                url: "/top/admin_setting_project",
                beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
                    $project_item.find('.project-tab').addClass('hide');
                    $project_item.find('.project-item__top').addClass('hide');
                    $project_item.find('.project-item__video-list').addClass('hide');
                    $project_item.find('.project-setting').show();
                    $project_item.find('.setting-content-tab').html(response.html);
                }
            })
        });

        $project_item.find('.project-setting__filter-item').off().on('click', function () {
            var tab_item = $(this).data('show');

            if (!$(this).hasClass('active')) {
                $project_item.find('.project-setting__filter-item').removeClass('active');
                $(this).addClass('active');
                $project_item.find('.project-setting-tab').removeClass('active');
                $project_item.find('.project-setting-tab-' + tab_item).addClass('active');
            }
        });
    });

    $(window).scroll(function () {
        load_more();
        optimizePreload();
    });
    if (show_comment) {
        $('.project-item').first().find('.file-upload-owner').trigger('click')
    }

    copyURL();

    setOpenVideoModalFor(active_project_el);

    setOpenVideoModalFor($('#processingSceneModal'));

    // load_process_videos_watting_checkback();
});


function autoResize(el) {
    setTimeout(function () {
        let height;
        el.style.cssText = 'height:auto; padding:0';
        height = el.scrollHeight < 200 ? el.scrollHeight : 200;
        el.style.cssText = 'height:' + height + 'px';
    }, 0);
}

function hiddenElementByTab(tab_item) {
    let segmentNavbar = $('.segment-navbar');
    let leftSidebar = $('#left-sidebar-open');
    switch (tab_item) {
        case 'progress':
            segmentNavbar.addClass('hidden-el');
            leftSidebar.removeClass('hidden-el');
            break;
        case 'product-comment':
            segmentNavbar.removeClass('hidden-el');
            leftSidebar.addClass('hidden-el');
            break;
        case 'messenger':
            segmentNavbar.removeClass('hidden-el');
            leftSidebar.addClass('hidden-el');
            break;
    }
}

function resizeCommentInput() {
    let main_block = $('.pd-section__content.main-talk-room')
    if (main_block.length < 1) {
        main_block = $('.offer-content-message')
    }
    if (main_block.length > 0) {
        if ($(window).width() > max_width_sp_device) {
            let width_main_block = main_block.width();
            let left_main_block = main_block.offset().left;
            let footer_comment_block = $('.footer-comment-block');
            let width_footer_comment_block = footer_comment_block.width();
            let fixedBlockLeft = left_main_block + (width_main_block - width_footer_comment_block) / 2;
            footer_comment_block.css({
                'width': width_main_block + 'px',
                'left': fixedBlockLeft + 'px'
            });
        }
    }
}


$('.show-file-icon').on('click', () => {
    const screenWidth = $(window).width();
    if (screenWidth > max_width_sp_device) return;
    if ($('.show-file-icon').hasClass('open-file')) {
        $('.pd-section-file').css('display', 'none');
        $('.main-talk-room').css('display', 'block');
        $('.show-file-icon').removeClass('open-file');
    } else {
        $('.pd-section-file').css('display', 'block');
        $('.main-talk-room').css('display', 'none');
        $('.show-file-icon').addClass('open-file');
    }
});