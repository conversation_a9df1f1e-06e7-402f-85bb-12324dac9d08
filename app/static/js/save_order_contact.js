function redirectScreenCreateProject(type_contact, messageValue, artist_id, budgetValue, sale_id, saleName, list_file='', list_folder='') {
    let formData = new FormData();
    formData.append('type_contact', type_contact);
    formData.append('message', messageValue);
    formData.append('artist_id', artist_id);
    formData.append('sale_id', sale_id);
    formData.append('sale_name', saleName);
    formData.append('budget', budgetValue);
    formData.append('file', list_file);
    formData.append('folder', list_folder);
    $.ajax({
        type: 'POST',
        url: '/direct/save_order_data',
        data: formData,
        processData: false,
        contentType: false,
        success: function (data) {
            window.location.href = data.url;
        },
        error: function () {

        },
        complete: function () {
        }
    });
}
