// Scroll tabs
function onScroll(event) {
    var difference = document.documentElement.scrollHeight - window.innerHeight;
    var scrollPos = $(document).scrollTop();

    if (difference - scrollPos <= 160 && $('.user-info__tabs-list li a[href="#tab_4"]').length) {
        $('.user-info__tabs-list li a').removeClass('active');
        $('.user-info__tabs-list li a[href="#tab_4"]').addClass('active')
    } else {
        scrollPos += 10;
        $('.user-info__tabs-list li a').removeClass('active');
        $('.user-info__tabs-list li a').each(function () {
            var currLink = $(this);
            var refElement = $(currLink.attr("href"));
            if (refElement.position().top <= scrollPos && refElement.position().top + refElement.height() > scrollPos) {
                $('.user-info__tabs-list li a').removeClass("active");
                currLink.addClass("active");
            } else {
                currLink.removeClass("active");
            }
        });
    }
}

// handle schedule
var thisMonthSelected = [];
var nextMonthSelected = [];
var statusSelect = {
    thisMonth: {},
    nextMonth: {}
};
var deadlineOffer = {
    thisMonth: [],
    nextMonth: []
};
var currentDate = new Date();
var startDayNextMonth = moment().zone('+0900').add(2, 'M').startOf('month').toDate();
var preState = {};
monthName = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
datePicker = function () {
    if ($('.mcalendar').length > 0) {

        $('.mcalendar').datepicker({
            inline: true,
            multidate: true,
            format: 'dd-mm-yyyy',
            locale: 'ja',
            todayHighlight: true,
            todayBtn: true,
            debug: true,
            showButtonPanel: false,
            numberOfMonths: 2,
            beforeShowDay: function (date) {
                if (date <  new Date(formatDate(currentDate))) {
                    return {
                        classes: 'disabled'
                    };
                }
                return;
            }
        }).on('changeDate', function (e) {
            var selected = $(this).datepicker('getDates').map(e => e.getDate());
            var isThisMonth = $(this).hasClass('mcalendar__this_month');
            var oldSelect;
            var itemSelect;
            let monthYear = {};

            selected = Array.from(new Set(selected));
            if (isThisMonth) {
                oldSelect = thisMonthSelected;
                thisMonthSelected = selected;
                itemSelect = 'thisMonth';
                monthYear['month'] = currentDate.getMonth();
                monthYear['year'] = currentDate.getFullYear();
            } else {
                oldSelect = nextMonthSelected;
                nextMonthSelected = selected;
                itemSelect = 'nextMonth';
                monthYear['month'] = startDayNextMonth.getMonth();
                monthYear['year'] = startDayNextMonth.getFullYear();
            }

            diff = diffArray(oldSelect, selected);
            date = diff[0]
            var currentCountSelect = statusSelectClick(date, itemSelect)
            handleUpdateSchedule(currentCountSelect, isThisMonth, new Date(monthYear.year, monthYear.month, date), date, itemSelect)
        });

        // show calendar for next month
        $('.mcalendar__next_month .datepicker-days .datepicker-switch').click();

        if (startDayNextMonth.getMonth() === 0) {
            let elemYear = $('.mcalendar__next_month .datepicker-years td span').toArray()
                .filter(e => $(e).text() == startDayNextMonth.getFullYear())[0];
            $(elemYear).click();
        }
        let elemMonth = $('.mcalendar__next_month .datepicker-months td span').toArray()
            .filter(e => $(e).text() === monthName[startDayNextMonth.getMonth()])[0];
        $(elemMonth).click();

        getSchedules();
    }
}

datePicker()

function handleUpdateSchedule(status, isThisMonth, datetime, date, itemSelect) {
    fulldate = moment(datetime).format('DD/MM/YYYY');
    handleGetTaskDeadline(date, isThisMonth, fulldate);
    updateSchedule(fulldate, status, itemSelect, isThisMonth)
}

function findElement(date, isThisMonth) {
    containt_class = isThisMonth ? 'mcalendar__this_month' : 'mcalendar__next_month';
    return $(`.${containt_class} tr td.day`).not('.new').not('.old').toArray()
        .filter(e => $(e).text() === `${date}`)[0];
}

function diffArray(arraya, arrayb) {
    if (arraya.length > arrayb.length) {
        return arraya.filter(x => arrayb.indexOf(x) === -1);
    }

    return arrayb.filter(x => arraya.indexOf(x) === -1);
}

function statusSelectClick(date, itemSelect) {
    if (!statusSelect[itemSelect][date]) {
        statusSelect[itemSelect][date] = 2
    } else if (statusSelect[itemSelect][date] == 1) {
        statusSelect[itemSelect][date] = 3
    } else {
        statusSelect[itemSelect][date] -= 1
    }

    return statusSelect[itemSelect][date]
}

function updateSchedule(fulldate, status, itemSelect, isThisMonth) {
    let creator_id = $('#creator_settings__form').attr('data-id');
    $.ajax({
        type: "POST",
        url: '/creator/update_creator_schedule',
        headers: {
            'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
        },
        data: {
            'date': fulldate,
            'status': status,
            'creator_id': creator_id
        },
        success: function (data) {
            let obj = statusSelect[itemSelect];
            showAllIconInMonth(isThisMonth, obj);
        },
        error: function (xhr, status, error) {
        }
    });
}

function showIconSchedule(status, $elem) {
    switch (status) {
        case 1:
            if (!$elem.hasClass('day__off')) $elem.addClass('day__off');
            $elem.remove('day__maybe');
            break;
        case 2:
            if (!$elem.hasClass('day__maybe')) $elem.addClass('day__maybe');
            $elem.remove('day__off');
            break;
        case 3:
            $elem.remove('day__off');
            $elem.remove('day__maybe');
            break;
        default:
            break;
    }
}

function handleGetTaskDeadline(date, isThisMonth, fulldate) {
    let currentMonth = isThisMonth ? 'thisMonth' : 'nextMonth';
    showAllDeadlineInMonth(isThisMonth, deadlineOffer[currentMonth])
}

function getSchedules() {
    let creator_id = $('#creator_settings__form').attr('data-id');
    $.ajax({
        type: "GET",
        url: '/accounts/get_schedule_list',
        data: {
            'creator_id': creator_id
        },
        success: function (data) {
            statusSelect = data.schedules;
            showAllIconInMonth(true, statusSelect.thisMonth);
            showAllIconInMonth(false, statusSelect.nextMonth)
        },
        error: function (xhr, status, error) {
        }
    });

    $.ajax({
        type: "GET",
        url: '/accounts/get_task_deadline',
        data: {
            'creator_id': creator_id
        },
        success: function (data) {
            let deadlineTask = data.group_offers;
            showAllDeadlineInMonth(true, deadlineTask['thisMonth']);
            showAllDeadlineInMonth(false, deadlineTask['nextMonth']);
            $('#dealline_task').empty();
            $('#dealline_task').append(data.task_html);
            removeDuplicatedDate()
        },
        error: function (xhr, status, error) {
        }
    });
}

function showAllIconInMonth(isThisMonth, dates) {
    for (const prog in dates) {
        elem = findElement(prog, isThisMonth);
        $elem = $(elem);
        showIconSchedule(dates[prog], $elem)
    }
}

function showAllDeadlineInMonth(isThisMonth, dates) {
    let currentMonth = isThisMonth ? 'thisMonth' : 'nextMonth';

    for (const prog in dates) {
        elem = findElement(dates[prog], isThisMonth);
        $elem = $(elem);
        $elem.addClass('day__deadline');
        deadlineOffer[currentMonth].push(dates[prog]);
    }
}

function removeDuplicatedDate() {
    let date;
    $('.account__field-text').each(function (i, e) {
        if (date === e.innerHTML) {
            $(e).addClass('hide');
        }
        date = e.innerHTML;
    })
}

// QR code
function qrCode() {
    var slug = $('#id_slug').val().trim();
    if (slug !== '') {
        $('.account__copy-link').removeClass('disable');
        var share_url = $('#basic-addon3').text() + $('#id_slug').val();
        $("#qrcode img").remove();

        var qrcode = new QRCode("qrcode", {
            text: share_url,
            width: 200,
            height: 200,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.H
        });
    }

    $('#id_slug').on('input', function (e) {
        var slug = $('#id_slug').val().trim();
        if (slug !== '') {
            $('.account__copy-link').removeClass('disable');
            var share_url = $('#basic-addon3').text() + $('#id_slug').val();
            $("#qrcode img").remove();

            var qrcode = new QRCode("qrcode", {
                text: share_url,
                width: 200,
                height: 200,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H
            });
        }
        else {
            $('.account__copy-link').addClass('disable');
            $("#qrcode img").remove();
        }
    });

    $('.account__copy-link').off().on('click', function () {
        var $temp = $(`<input class="copy-me" style="opacity: 0;">`);
        $(this).closest('.account__form-group-link').append($temp);
        $temp.val($('#basic-addon3').text() + $('#id_slug').val()).select();
        document.execCommand("copy");
        $('.copy-me').remove();
        // toastr.success("リンクをコピーしました。");
    });
}

$('[id^=id_notification]').on('change', function () {
    //uncheck all radio buttons
    $('[id^=id_notification]').attr('checked', null);
    //check current button
    $(this).attr('checked', 'checked');
});

$('.account__trade-item').on('click', function () {
    $('.account__trade-item').attr('selected', null);
    $(this).attr('selected', 'true');
    $('#id_trading').val($(this).data('option'));
    if ($(this).not('.active')) {
        $(this).addClass('active').siblings('.account__trade-item').removeClass('active');
    }
});

$('#no-notice-off-day').on('click', function () {
    if ($(this).attr('checked') == 'checked') {
        $(this).attr('checked', null);
    } else {
        $(this).attr('checked', 'checked');
    };
});

// block list
var labelCompanyName = '<p class="account__field-text" style="margin-bottom: 8px;">会社名・ユーザー名</p>';
var labelReason = '<p class="account__field-text" style="margin-bottom: 8px;">理由（任意)</p>';

$('.block_list__new').on('change', function () {
    let companyName = $("#company-name").val();
    let reason = $('#reason').val();
    let $this = $(this);

    if (!!companyName && !!reason) {
        $this.addClass('added');
    }

    if (!companyName && !reason && $this.hasClass('added')) {
        $this.removeClass('added');
    }
})

$('.blocklist__edit').on('change', function () {
    $(this).addClass('edited')
})

$("#button__add_blocklist").on("click", function () {
    $("#company-name small.errorlist").remove();
    $("#reason small.errorlist").remove();
    $("#company-name").removeClass('error-border');
    $("#reason").removeClass('error-border');
    createBlocklist();
});

$(document).on("click", '.account__blocklist--unblock .delete-row', function (event) {
    event.preventDefault();
    let $this = $(this);

    $this.parents(".form-group-wrap").fadeOut(300, function () {
        let text = $(this).find('.account__field-text').toArray();
        let index = $(this).attr('data-index');

        if (text.length > 0) {
            $('.label_company .account__field-text').remove();
            $('.label_reason .account__field-text').remove();

            setTimeout(function () {
                blockEdit = $('#block_list__hint')
                    .parents('.account__form-group')
                    .find('.blocklist__edit');
                if (blockEdit.length > 0) {
                    $(blockEdit[0]).find('.label_company').prepend(labelCompanyName);
                    $(blockEdit[0]).find('.label_reason').prepend(labelReason);
                }
            }, 200);
        }
        $(this).find('.input__company_name').addClass('input_company_name__blocklist__delete');
        $('.input__company_name').removeClass('error-border');
        $('small.errorlist').remove();
        $(`#id_company_banned_creator-${index}-DELETE`).attr('checked', 'checked');
        $(this).removeClass('blocklist__edit');
        $(this).addClass('blocklist__delete');
        $(this).hide();

        let arrEdited = $('.blocklist__edit').toArray();
        validateAllEditBlockList(arrEdited);
    });

})

$(document).on('mouseenter mouseleave', '.blocklist__edit .form-group-content', function(e) {
    $(this).find('.account__blocklist--unblock').toggleClass('active-action-delete', e.type === 'mouseenter')
})

$('#btn__ok').on('click', function (e) {
    e.preventDefault();
    let arrEdited = $('.blocklist__edit').toArray();

    if ($('input.error-border').length > 0) {
        validateAllEditBlockList(arrEdited);
        $($('input.error-border')[0]).focus();
        return;
    }
    if (validateAllEditBlockList(arrEdited)) {
        $('#creator_settings__form').submit();
    } else {
        $($('input.error-border')[0]).focus();
    }
})

function validateAllEditBlockList(arrEdited) {
    let isValid = true;

    for (let index = arrEdited.length - 1; index > 0 ; index--) {
        const $elem = $(arrEdited[index]);
        if (!validateEditBlockList($elem)) {
            if (isValid) {
                isValid = false;
                $elem.find('.input__company_name').focus();
            }
        }
    }

    return isValid;
}

function validateEditBlockList($elem) {
    let $companyName = $elem.find('.input__company_name');
    let hasError = false;
    let companyName = $companyName.val() || "";
    let validCompanyNames = getValidCompanyName();

    if (validCompanyNames.filter(e => e == companyName.trim()).length >= 2) {
        if (!$companyName.hasClass('error-border')) {
            $companyName.addClass('error-border');
            $companyName.after('<small class="errorlist">' + '同じ会社・ユーザーがすでに存在しています。' + '</small>');
            centerIconPC($companyName, '15px')
        }

        if (!hasError) hasError = true;
    } else {
        if(!hasError && !$companyName.hasClass('error-border')) {
            $companyName.parents('.form-group-wrap').find('small.errorlist').remove();
            $companyName.removeClass('error-border');
            centerIconPC($companyName, '0')
        }
    }

    if (companyName.trim() == "") {
        if (!$companyName.hasClass('error-border')) {
            $companyName.addClass('error-border');
            $companyName.after('<small class="errorlist">この項目は必須です。</small>');
            centerIconPC($companyName, '15px');
        }
        if (!hasError) hasError = true;
    }


    if (hasError) {
        let arrInput = $elem.find('.input__reason').toArray();
        for (let index = 0; index < arrInput.length; index++) {
            const $e = $(arrInput[index]);
            if ($e.parent().find('.errorlist').length < 1) {
                $e.after('<small class="errorlist" style="padding: 8px"></small>');
            }
        }
    }
    return !hasError;
}

var currentIndex = 0;

function createBlocklist() {
    let $totalForm = $('#id_company_banned_creator-TOTAL_FORMS');
    let $initForm = $('#id_company_banned_creator-INITIAL_FORMS');
    let initForm = Number($initForm.val());
    let totalForm = Number($totalForm.val());
    if (currentIndex < 1 && initForm > 0) {
        currentIndex = initForm;
    } else {
        currentIndex += 1;
        $totalForm.val(totalForm + 1);
    }

    $('#form-group-wrap__add-button').before(initDom(currentIndex));
    setTimeout(function () {
        blockEdit = $('#block_list__hint')
            .parents('.account__form-group')
            .find('.blocklist__edit');
        labelBlockList = $('.label_company .account__field-text');
        if (blockEdit.length > 0 && labelBlockList.length < 1) {
            $(blockEdit[0]).find('.label_company').prepend(labelCompanyName);
            $(blockEdit[0]).find('.label_reason').prepend(labelReason);
        }
    }, 200);

}

function cleanInput() {
    $("#company-name, #reason").val('');
    $('.block_list__new').removeClass('added');
    $('.block_list__new').find('.form-group-content').removeClass('label-errors');
    $('.blocklist__edit').each(function (elem) {
        let $elem = $(elem);
        if ($elem.hasClass('edited')) {
            $elem.removeClass('edited')
        }
    })
    $("#company-name").parents('.form-group-wrap').find('small.errorlist').remove();
    $("#company-name").removeClass('error-border');
}

function validateNewBlockList(formValid, companyName) {
    let required = new Map([["#company-name", companyName]])
    required.forEach(function (value, key) {
        if (value.trim() == "") {
            $(key).next().remove()
            $(key).addClass('error-border');
            $(key).parents('.col-md-12').find('.form-group-content').addClass('label-errors');
            $(key).after('<small class="errorlist">この項目は必須です。</small>');
            centerIconPC($(key), '15px');
            $('#reason').after('<small class="errorlist" style="padding: 10px"></small>');
        } else {
            $(key).removeClass('error-border'); $(key).next().remove();
            centerIconPC($(key), '0')
            $(key).parents('.col-md-12').find('.form-group-content').removeClass('label-errors');
        }
    })
}

var initDom = function (index) {
    let creatorId = $('form.user-info__form.row').attr('data-id')
    return `
            <div class="col-md-12 form-group form-group-wrap blocklist__edit" data-index=${index}>
                <div class="form-group-content">
                  <div class="col-md-4 col-sm-12">
                    <label for="id_company" class="label_company">
                      <input type="text" name="company_banned_creator-${index}-company_name" value="" placeholder="株式会社コレモ"
                        class="form-control account__input-text maxlength-company input__company_name" id="id_company">
                    </label>
                  </div>
                  <div class="col-md-4 col-sm-12">
                    <label for="id_reason" class="label_reason">
                      <input type="text" name="company_banned_creator-${index}-reason" value="" placeholder="競合規制のため"
                        class="form-control account__input-text input__reason" id="id_reason">
                    </label>
                  </div>
                  <input type="hidden" name="company_banned_creator-${index}-id" id="id_company_banned_creator-${index}-id">
                  <input type="hidden" name="company_banned_creator-${index}-creator" value="${creatorId}" id="id_company_banned_creator-${index}-creator">
                  <input type="checkbox" name="company_banned_creator-${index}-DELETE" id="id_company_banned_creator-${index}-DELETE" class="delete__checkbox" style="display: none;">
                  <div class="col-sm-12 form-group-action account__blocklist--unblock hide-action-delete">
                    <i class="delete-row icon icon--sicon-trash"></i>
                  </div>
                </div>
              </div>
        `
}

$(document).on('keyup', '.input__company_name', function () {
    validateInputBlockList($(this));
})

function getBlockListDeleted() {
    return $('.blocklist__delete').toArray().map(function (e) {
        let index = $(e).attr('data-index');
        return $(`#id_company_banned_creator-${index}-id`).val() || "";
    }).filter(e => e.trim() != '');
}

function getValidCompanyName() {
    return $('.input__company_name')
        .not('.error-border').not('.input_company_name__blocklist__delete')
        .toArray()
        .map(e => $(e).val()).filter(e => e.trim() != "");
}

function getIgnoreIds($companyName) {
    let index = $companyName.parents('.form-group-wrap').attr('data-index');
    let id = $(`#id_company_banned_creator-${index}-id`).val();
    ids = getBlockListDeleted();

    if(id) ids.push(id);

    return ids;
}

function validateInputBlockList($companyName) {
    let $reason = $companyName.parents('.form-group-wrap').find('.input__reason');
    let companyName = $companyName.val() || "";
    let reason = $reason.val();

    $companyName.parents('.form-group-wrap').find('small.errorlist').remove();
    $companyName.removeClass('error-border');
    centerIconPC($companyName, '0')

    let validCompanyNames = getValidCompanyName();

    if (companyName.trim() == "") {
        $companyName.addClass('error-border');
        $companyName.after('<small class="errorlist">この項目は必須です。</small>');
        $reason.after('<small class="errorlist" style="padding: 8px"></small>');
        centerIconPC($companyName, '15px')
        return
    }

    if (validCompanyNames.filter(e => e == companyName.trim()).length >= 2) {
        $companyName.addClass('error-border');
        $companyName.after('<small class="errorlist">' + '同じ会社・ユーザーがすでに存在しています。' + '</small>');
        $reason.after('<small class="errorlist" style="padding: 8px"></small>');
        centerIconPC($companyName, '15px')
        return
    }

    $companyName.removeClass('error-border');
    $companyName.parents('.col-md-12').find('.form-group-content').removeClass('label-errors');
    var paramList = { companyName, reason };
    paramList['ids'] = getIgnoreIds($companyName);
    $.ajax({
        url: '/accounts/ajax/block_list/validate',
        method: 'POST',
        headers: {
            'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
        },
        data: {
            'param': paramList
        },
        datatype: 'json',
        beforeSend: function (xhr, settings) {
                            xhr.setRequestHeader("X-CSRFToken", window.CSRF_TOKEN);
                        },
                        success: function (response) {
            $companyName.parents('.form-group-wrap').find('small.errorlist').remove();
            $companyName.removeClass('error-border');
            if (response.existed) {
                $companyName.addClass('error-border');
                $companyName.after('<small class="errorlist">' + response.existed + '</small>');
                $reason.after('<small class="errorlist" style="padding: 8px"></small>');
            }

        }
    })
}

function centerIconPC(companyName, marginIcon) {
    if ($(window).width() > 992) {
        companyName.parents().find('.account__blocklist--unblock').css('margin-bottom', marginIcon);
    }
}

function checkValidSlug(slug_name) {
    let creator_id = $('#creator_settings__form').attr('data-id');
    $.ajax({
        url: '/accounts/accounts_check_valid_slug',
        data: {
            'slug_name': slug_name,
            'creator_id': creator_id
        },
        success: function (data) {
            $('#id_slug').removeClass('error-border');
            $('.errorlist').html('')
        },
        error: function (data) {
            $('#id_slug').addClass('error-border');
            $('.errorlist').html(data.responseJSON.message);
        }
    })
}

var timer;

$("#id_slug").on('keyup', function () {
    clearTimeout(timer);
    timer = setTimeout(function () {
        let slug_name = $("#id_slug").val();
        checkValidSlug(slug_name)
    }, 1000);
});

// add placeholder
var placeholder = `（例）
・名前(クレジット)が表に発表出来る作品のみお請けしています。
・プロジェクトが私にとってエキサイティングかどうかで判断します。`;

$('textarea#id_policy').attr('placeholder', placeholder);

$('#id_policy').focus(function () {
    if ($(this).val() === placeholder) {
        $(this).attr('placeholder', '');
    }
});

$('#id_policy').blur(function () {
    if ($(this).val() === '') {
        $(this).attr('placeholder', placeholder);
    }
});

function selectSkill() {
    isSelected = $(this).hasClass('selected');
    let dataId = $(this).attr('data-id')
    let checkbox = $(`#skill_${dataId}`)
    if (isSelected) {
        $(this).removeClass('selected');
        checkbox.removeAttr('checked');
    } else {
        $(this).addClass('selected')
        checkbox.attr('checked', 'checked');
    }
}

// Active trade
function activeTrade() {
    $('.account__trade-item').on('click', function () {
        $('.account__trade-item').attr('selected', null);
        $(this).attr('selected', 'true');
        $('#id_trading').val($(this).data('option'));
        if ($(this).not('.active')) {
            $(this).addClass('active').siblings('.account__trade-item').removeClass('active');
        }
    });
}

function switchCheckboxIsLeader() {
    $(document, '.switch-label').on('change', function () {
        let toggleDom = $(this).parents('.form-check-group').find('.switch-checkbox');
        if (toggleDom.is(':checked')) {
            toggleDom.attr('checked', 'checked');
        } else {
            toggleDom.removeAttr('checked');
        }
    })
}

// Selected option
$(document).ready(function () {
    // Maxlength
    $(document).on('input', '.maxlength-company', function (e) {
        var company = $(this).val().trim();
        if (company.length > 100) {
            $(this).val(company.slice(0, 100));
        }
    })

    onScroll();
    $(document).on("scroll", onScroll);
    activeTrade();
    qrCode();
    switchCheckboxIsLeader()
});
$(document).on("click", '.account__field-item', selectSkill);
