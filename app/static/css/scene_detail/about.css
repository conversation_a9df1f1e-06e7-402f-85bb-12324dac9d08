@charset "UTF-8";
/* CSS Document */


/* ---------
   About
--------- */
.about {
    width: 100%;
    height: 723px;
    background-color: #fcfcfc;
    background-image: url("../images/<EMAIL>");
    background-size: cover;
}

.about h3 {
    padding-bottom: 1.5em;
}

.about-text {
    text-align: center;
    color: #333333;
}


.about-text h1 {
    font-family: 'A+mfCv-AXISラウンド 50 M StdN';
    font-size: 4.375rem;
    font-weight: normal;
    color: #444444;
    padding: 220px 0px 0px;
}

.about-text h2 {
    font-size: 1.875rem;
    font-weight: normal;
    color: #444444;
}

.about-text p {
    padding: 120px 0px 0px;
    font-weight: normal;
}

.otocap {
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    letter-spacing: -0.075em;
    margin-left: -0.075em;
}


/* ---------
   Profile
--------- */

.profile {
    width: clamp(375px, 80%, 1000px);
}

.profile li {
    font-size: 1rem;
    font-weight: normal;
    margin: 0.25rem 0;
}

.profile img {
    margin-bottom: 1rem;
}

.flex-container-row {
    display: flex;
    flex-wrap: nowrap;
}

.role {
    padding-right: 0.75em;
    flex: 0 1 50%;
    text-align: end;
    font-family: 'A+mfCv-AXISラウンド 50 L StdN';
    color: #A7A8A9;
}

.description {
    padding-left: 0.75em;
    flex: 0 1 50%;
    text-align: start;
    color: #333333;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
}


.logo-client {
    width: clamp(375px, 80%, 1000px);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    align-items: center;
}

.client-text {
    width: 100%;
    color: #333333;
    text-align: center;
}

.client-text li {
    padding: 1.5em 0;
}

.client-text li a {
    color: #333333;
    font-family: 'A+mfCv-AXISラウンド 50 R StdN';
    font-size: 1rem;
    font-weight: normal;

}


.client-logo {
    width: clamp(240px, 24%, 320px);
}


@media (max-width: 560px) {

    .about-text h1 {
        font-size: 1.875rem;
        padding: 220px 0px 0px;
    }

    .about-text h2 {
        font-size: 1.5rem;
        font-weight: normal;
        color: #444444;
    }

    .about-text p {
        font-size: 0.6875rem; /*11px */
        padding: 120px 0px 0px;
        font-weight: normal;
    }

    .client-text li a {
        color: #333333;
        font-size: 0.8125rem;
        font-weight: normal;
    }
}
