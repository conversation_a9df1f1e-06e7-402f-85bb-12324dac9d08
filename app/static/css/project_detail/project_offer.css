.project-offer__item {
    display: flex;
    align-items: start;
    margin-bottom: 20px;
}

@media screen and (max-width: 991px) {
    .project-offer__item {
        display: block;
    }
}

.project-offer__item:last-child {
    margin-bottom: 0;
}

.offer-user {
    display: flex;
    align-items: start;
    flex: 0 0 354px;
    border: 1px solid #333;
    border-radius: 10px;
    padding: 10px 20px;
}

@media screen and (max-width: 991px) {
    .offer-user {
        flex: 1;
    }
}

.offer-user__avatar {
    width: 75px;
    align-self: flex-end;
    position: relative;
}

.offer-user__info {
    margin-left: 10px;
    width: calc(100% - 155px);
}

.offer-user__name {
    color: #a7a8a9;
    font-size: 12px;
    margin-bottom: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    /*! autoprefixer: on */
    overflow: hidden;
}

.offer-user__work {
    font-size: 10px;
}

.offer-user__money {
    flex: 0 0 80px;
    text-align: right;
    font-size: 10px;
    align-self: center;
}

.offer-user__price {
    margin-bottom: 5px;
}

.offer-user__price-sub {
    margin-bottom: 5px;
}

.offer-user__price-total {
    color: #009dc4;
}

.offer-detail {
    flex: 1;
    margin-left: 28px;
    padding: 8px;
    background-color: rgba(240, 240, 240, .5);
}

@media screen and (max-width: 991px) {
    .offer-detail {
        margin-left: 0;
    }
}

.offer-detail__name {
    width: 90px;
}

.offer-detail__bank {
    width: 110px;
}

.offer-detail__branch {
    width: 110px;
}

.offer-detail__branch-number {
    width: 130px;
}

.offer-detail__account-type {
    width: 130px;
}

.offer-detail__account-number {
    width: 110px;
}

.offer-detail__transaction {
    margin-bottom: 0;
}

.offer-detail__price {
    width: 90px;
}

.offer-detail__close-date {
    width: 120px;
}

.offer-detail__accept-date {
    width: 110px;
}

.offer-detail__detail {
    width: 130px;
}

.table-offer {
    font-size: 10px;
}

.table-offer thead tr td, .table-offer tbody tr td, .table-offer thead tr th, .table-offer tbody tr th {
    border: none;
    padding: 5px 8px;
    word-break: break-word;
}

.table-offer thead {
    color: #333;
}

.table-offer tbody {
    color: #53565a;
}

.offer-user__avatar-img {
    width: 60px;
    border-radius: 50%;
}

.button--active {
    display: flex;
    align-items: center;
    border-radius: 20px;
    padding: 0;
    background-color: #009ace;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    cursor: pointer;
    color: #FFFFFF;
}

.project-item__filter-item {
    padding: 4px 24px;
    line-height: 0;
}

.project-item__filter {
    margin-bottom: 20px;
}
