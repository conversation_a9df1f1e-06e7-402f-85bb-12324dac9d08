/* show message*/
.s-audio-name {
  flex: 0 0 100%;
  margin: 5px 0;
}

.s-audio-name .icon--sicon-download {
  cursor: pointer;
}

/* .s-audio-control {
  font-size: 24px;
  margin-right: 4px;
} */

.s-audio-control .icon--sicon-pause {
  display: none;
}

.s-audio-control .icon--sicon-play {
  display: block;
}

.s-audio-control.active .icon--sicon-pause {
  display: block;
}

.s-audio-control.active .icon--sicon-play {
  display: none;
}

.s-audio-control:hover {
  cursor: pointer;
}

.s-audio-waveform {
  flex: 0 0 calc(100% - 80px);
  position: relative;
}

.s-audio-time {
  margin-left: auto;
}

.s-audio {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  padding: 8px 0px 16px;
  /* font-size: 13px; */
  /* word-break: break-word; */
  /* border-radius: 6px; */
}

.s-audio.s-audio--gray.active,
.s-audio.s-audio--black.active {
  background-color: #009ace;
  color: #ffffff;
  border: 1px solid #009ace;
}

.s-audio--gray {
  background-color: #fcfcfc;
  color: #000;
  border: 1px solid #f0f0f0;
}

.s-audio--black {
  background-color: #fcfcfc;
  color: #000000;
  border: 1px solid #f0f0f0;
}

.s-audio--blue {
  background-color: #009ace;
  color: #fff;
}

.s-audio--white {
  background-color: #ffffff;
  color: #53565a;
}

.s-file {
  display: flex;
  align-items: center;
  padding: 4px 0px 4px 0px;
  font-size: 13px;
  word-break: break-word;
  /* border-radius: 6px; */
}

.s-file .icon {
  margin-right: 10px;
  font-size: 16px;
  color: #a7a8a9;
}

.s-file p {
  margin: 0;
  font-size: 13px;
}

.tfile-type .s-file {
  /*padding: 6px 65px 24px 12px;*/
  /* line-height: 20px; */
  /* flex-wrap: nowrap; */
  /* margin-right: 5px; */
  /* border-bottom: 1px solid #f0f0f0; */
  background-color: #fff;
}

.s-file.s-file--gray {
  background-color: #fcfcfc;
  color: #000000;
}

.s-file--gray:hover {
  background-color:#f0f0f0;
  color: #000000;
}

.s-file--black {
  background-color: #fcfcfc;
  color: #000000;
  border: 1px solid #f0f0f0;
}

.s-file--black:hover {
  color: #000000;
}

.s-file-tooltip {
  padding: 0 8px;
}

.s-file-tooltip .icon {
  margin-right: 12px;
}

.s-file-tooltip span {
  margin-right: 24px;
}

.s-file-tooltip span:last-child {
  margin-right: 0;
}

.s-fileupload {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
}

.s-fileupload .s-filetop {
  display: flex;
  align-items: center;
}

.s-fileupload .s-fileavatar {
  display: -ms-inline-grid;
  display: inline-grid;
  margin-right: 8px;
}

.s-fileupload .s-fileinfo {
  color: #a7a8a9;
  font-size: 12px;
}

.s-fileupload .icon {
  font-size: 16px;
}

.s-fileupload .s-filebottom {
  color: #53565a;
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.s-fileupload .s-filename {
  font-size: 12px;
  margin-left: 12px;
}

.s-fileupload--gray {
  background-color: #f0f0f0;
  color: #53565a;
}

.s-fileupload--black {
  background-color: #53565a;
  color: #fff;
}

.s-fileupload--black .s-fileavatar {
  border: 1px solid #fff;
  border-radius: 50%;
}

.s-fileupload--black .s-fileinfo {
  color: #a7a8a9;
}

.s-fileupload--black .s-filebottom {
  color: #fff;
}

.s-filedisable-wrap {
  /*position: absolute;*/
  /*margin-bottom: 30px;*/
}

.s-filedisable {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  font-size: 12px;
  border-radius: 6px;
  background-color: #d3d3d3;
  width: fit-content;
}

.s-filedisable.s-filedisable--black {
  margin-left: auto;
  margin-right: 0;
}

.s-filedisable.s-filedisable--gray {
  margin-left: 0;
  margin-right: auto;
}

.s-filedisable .icon {
  margin-right: 10px;
  font-size: 16px;
}

.s-filedisable ~ .s-filetext {
  padding: 8px 16px;
  border-radius: 6px;
  /*position: absolute;*/
  top: 30px;
  width: fit-content;
  margin-left: 0;
  margin-right: auto;
}

.s-filedisable--gray {
  color: #a7a8a9;
}

.s-filedisable--gray ~ .s-filetext {
  background-color: #f0f0f0;
  color: #000000;
  left: 0;
  margin-top: -8px;
  width: fit-content;
  margin-right: auto;
  margin-left: 0;
}

.s-filedisable--gray:hover {
  color: #a7a8a9;
}


.s-filedisable--black ~ .s-filetext {
  background-color: #fcfcfc;
  color: #000000;
  border: 1px solid #f0f0f0;
  margin-top: -8px;
  right: 0;
  width: fit-content;
  margin-right: 0;
  margin-left: auto;
}

.s-filedisable--black.hide ~ .s-filetext,
.s-filedisable--gray.hide ~ .s-filetext {
  margin-top: 0 !important;
}

.editing .s-filedisable--black,
.reply .s-filedisable--gray {
  background-color: #0076a5;
  color: #ffffff;
}

.s-text {
  line-height: 1.5;
  padding: 8px 8px;
  word-break: break-word;
  white-space: pre-line;
  border-radius: 6px;
}

.s-text--gray {
  background-color: #f0f0f0;
  color: #000000;
  border: 1px solid #f0f0f0;
}

.s-text--black {
  background-color: #009ace;
  color: #ffffff;
  border: 1px solid #009ace;
}

.s-video {
  display: block;
  line-height: 1;
}

.mmessenger .messenger-content {
  display: flex;
}

.martist .mmessage.mmessage--received .messenger-content .s-text--gray {
  cursor: default;
}

.mmessage--received .mmessenger .messenger-content {
  justify-content: flex-start;
}

.mmessage--sent .mmessenger .messenger-content {
  justify-content: flex-end;
}

.messenger-image-preview-content {
  display: flex !important;
  flex-direction: column !important;
}

.image-preview-comment img {
  width: 100%;
  max-width: 250px;
  margin-bottom: 6px;
  height: 100%;
  max-height: 250px;
}

.messenger-video-preview-content {
  display: flex !important;
  flex-direction: column !important;
}

.messenger-document-preview-content {
  display: flex !important;
  flex-direction: column !important;
}


.block-pdf-image {
  height: 300px;
}

.block-pdf-image .pdf-image {
  max-height: 100%;
}

.comment-file-content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 9px;
  width: 100%;
}

/*.mmessage .mmessage-content,*/
/*.s-filedisable {*/
/*    cursor: default;*/
/*}*/

.mmessenger--audio .s-audio-waveform,
.mmessenger--audio .s-audio-time {
  display: none;
}

.mmessenger--audio .s-audio {
  min-width: auto;
}

.mmessenger--audio-wave .messenger-content wave {
  cursor: pointer !important;
}

.pd-section__content.active .mmessenger--audio-wave .s-audio {
  min-width: 300px;
}

.mrow .mmessenger--audio-wave .s-audio {
  min-width: 275px;
}

.pd-section__content:not(active) .mmessenger--audio-wave .s-audio {
  min-width: 275px;
  cursor: pointer;
}

.s-audio.s-audio--audio-wave .s-audio-control .icon {
  color: #a7a8a9;
}

.s-audio .s-audio-control .icon {
  color: #a7a8a9;
}

.s-audio.s-audio--gray.active .s-audio-control .icon,
.editing .s-audio .s-audio-control .icon,
.reply .s-audio .s-audio-control .icon,
.s-audio.s-audio--black.active .s-audio-control .icon {
  color: #ffffff;
}

/* input message*/
.mcommment {
  border-top: 1px solid #f0f0f0;
  /*    border-radius: 6px; */
  background-color: #fff;
}

.border-editing .mcomment-message .mcomment-top:has(.block-remove-msg-editing .mcomment-input-close.btn-remove-msg.d-block) .mcomment-input {
  padding: 10px 24px 10px 16px;
}

.mcomment-input {
  display: flex;
  flex: 1;
  padding: 10px 16px;
}

.mcomment-input-text {
  border: none;
  resize: none;
  width: 100%;
  line-height: 20px;
  padding: 0;
}

.mcomment-input-text {
  scrollbar-face-color: #d3d3d3;
  scrollbar-track-color: transparent;
}

.mcomment-input-text::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.mcomment-input-text::-webkit-scrollbar-thumb {
  background: #d3d3d3;
  border-radius: 2px;
}

.mcomment-input-text::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

.mcomment-input-text:focus {
  outline: none;
}

.mcomment-input-text:-ms-input-placeholder {
  color: #d3d3d3;
}

.mcomment-input-text::placeholder {
  color: #d3d3d3;
}

.mcomment-top {
  border-bottom: 1px solid #f0f0f0;
}

.mcomment-bottom {
  display: flex;
  align-items: center;
  padding: 8px 0px 48px 0px;
  min-height: 48px;
}

.mcomment-action {
  display: flex;
  align-items: center;
}

.mcomment-action .icon:hover {
  color: #009ace;
  cursor: pointer;
}

.mcomment-pin {
  color: #a7a8a9;
  /*display: flex;*/
  font-size: 24px;
  margin: 0 16px 0 8px;
}

.mcomment-pin.active {
  color: #009ace;
}
.mcomment-pin:hover {
  color: #009ace;
}

.mcomment-pin .pin-icon-time {
  display: none;
}

.mcomment-pin.active .pin-icon-time {
  display: block;
}

.mcomment-attached:not(:empty) {
  padding: 4px 14px;
}

.mcomment-icon {
  color: #a7a8a9;
  margin-left: auto;
  position: relative;
}
.mcomment-icon .icon:hover {
  color: #009ace;
}

.mcomment-icon .mcomment-icon-btn {
  color: #a7a8a9;
  display: flex;
  font-size: 16px;
}

.mcomment-icon .mcomment-icon-btn:hover {
  color: #009ace;
}

.mcomment-icon .mboard-triangle {
  width: 30px;
  height: 30px;
  position: absolute;
  bottom: calc(100% + 4px);
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s;
}

.mcomment-icon .mboard-triangle.active {
  opacity: 1;
  visibility: visible;
  transition: all 0.2s;
}

.mcomment-icon .mboard-triangle:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  bottom: -6px;
  left: 0;
  box-sizing: border-box;
  border: 10px solid;
  border-color: transparent transparent #fff #fff;
  transform-origin: 0 0;
  transform: rotate(-45deg);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.mcomment-icon .mcomment-icon-board {
  position: absolute;
  bottom: calc(100% + 13px);
  width: 292px;
  max-width: 100vw;
  height: 344px;
  background-color: #fff;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 12px 16px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s;
}

.mcomment-icon .mcomment-icon-board.active {
  opacity: 1;
  visibility: visible;
  transition: all 0.2s;
}

.mcomment-icon .micon-board-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -6px;
}

.mcomment-icon .micon-board-icon {
  flex: 0 0 25%;
  padding: 6px;
}

.mcomment-icon .micon-board-icon:hover {
  cursor: pointer;
}

.mcomment-icon .micon-board-section {
  margin-bottom: 8px;
}

.mcomment-icon .micon-board-section:last-child {
  margin-bottom: 0;
}

.mcomment-icon .micon-board-heading {
  margin-bottom: 4px;
}

.mcomment-input-placeholder {
  color: #a7a8a9;
  margin-left: 8px;
}

.mcomment-attach {
  display: none !important;
  /* stylelint-disable-line */
}

.mcomment-attach-label {
  margin-bottom: 0;
  color: #a7a8a9;
  display: flex;
  font-size: 15px;
}

.mcomment-attach-label:hover {
  cursor: pointer;
  color: #009ace;
}

.mcomment-send {
  margin-left: 8px;
  margin-right: 8px;
  color: #a7a8a9;
  pointer-events: none;
  display: flex;
  font-size: 24px;
}

.mcomment-send.active {
  color: #009ace;
  pointer-events: auto;
}

.mcommment-file__name {
  max-width: 200px;
  overflow: hidden;
  white-space: nowrap;
}

/*show mitem*/

.mscene {
  height: 86px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px 10px 8px 16px;
  display: flex;
  flex-direction: column;
  background-color: #f0f0f0;
}

.mscene__top {
  display: flex;
}

.mscene__label {
  margin-left: auto;
}

.mscene__label .icon {
  color: #fff;
  font-size: 20px;
}

.mscene__bottom {
  display: flex;
  margin-top: auto;
}

.mscene__name {
  color: #000;
  display: flex;
  align-items: center;
}

.mscene__name .thread-name {
  font-size: 13px;
  font-weight: 300;
  line-height: 20px;
  color: var(--black1-color);
  margin-bottom: 14px;
  max-width: 300px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  display: block;
  white-space: nowrap;
}

@media (max-width: 739px) {
  .mscene__name .thread-name {
    max-width: 230px;
  }
}

@media (min-width: 740px) and (max-width: 1023px) {
  .mscene__name .thread-name {
    max-width: 660px;
  }
}

.mscene__name .notification {
  margin-left: 8px;
}

.mscene.mactive {
  background-color: #f0f0f0;
}

.mscene.mchecked {
  background-color: #53565a !important;
}

.mscene.mprogress {
  background-color: #ffffff;
}

.mscene.mchecked .mscene__user .avatar {
  border: 2px solid #fff;
}

.mscene.mchecked .mscene__name {
  color: #fff;
}

/*.mscene.mchecked .mscene__name .notification {*/
/*    border: 1px solid #fff;*/
/*    height: 17px;*/
/*}*/

/* offer informatio */
.minfo-accept__link {
  color: #009ace;
}

.minfo-accept__action {
  margin-bottom: 16px;
}

.minfo-accept__action .btn {
  width: 100%;
}

/*.minfo-file_info {*/
/*    margin-bottom: 10px;*/
/*    cursor: pointer;*/
/*}*/

.minfo-task__item {
  margin-bottom: 8px;
}

.minfo-task__item:last-child {
  margin-bottom: 0;
}

.minfo-task__title {
  background-color: #f0f0f0;
  border-radius: 6px;
  padding: 8px 16px;
  margin-top: 8px;
}

.accordion .accordion-header {
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.accordion-item .accordion-header {
  border-bottom: none;
}

.accordion .accordion-header:first-child {
  border-top: none;
}

.accordion .accordion-header:hover {
  cursor: pointer;
}

.accordion .accordion-heading {
  display: flex;
  align-items: center;
  padding: 16px;
}

.accordion .accordion-heading .icon {
  font-size: 16px;
  margin-left: auto;
  color: #a7a8a9;
  transition: all 0.3s;
}

.accordion .accordion-heading:not(.collapsed) .icon {
  transform: rotate(-180deg);
  transition: all 0.3s;
}

.accordion .accordion-content {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

label[for="deadline-date"],
.select-deadline_time,
#deadline-date {
  cursor: pointer;
}

/*direct owner */

.mthread {
  height: 120px;
  background-size: cover;
  background-position: center center;
  border-radius: 6px;
  padding: 16px 16px 12px 16px;
  display: flex;
  flex-direction: column;
}

.mthread__top {
  display: flex;
  margin-bottom: 8px;
}

.mthread__user {
  display: flex;
  justify-content: flex-start;
}

.mthread__user-list {
  display: flex;
  flex-wrap: nowrap;
}

.mthread__user:not(:first-child) {
  margin-left: -8px;
}

.mthread__user .avatar {
  border: 2px solid #fff;
}

.mthread__label {
  margin-left: auto;
}

.mthread__bottom {
  display: flex;
  /*margin-top: auto; */
}

.mthread__name {
  color: #fff;
  display: flex;
  align-items: center;
}

.mthread__name .notification {
  margin-left: 8px;
  border: 1px solid #fff;
  height: 17px;
}

.mthread__setting {
  margin-left: auto;
  color: #fff;
  font-size: 20px;
  line-height: 1;
}

.mthread__setting:hover {
  color: #fff;
}

.mthread--main {
  height: 165px;
}

.mthread .bdropdown {
  margin-left: auto;
}

.mthread .bdropdown .icon {
  color: #fff;
}

.mprethread {
  height: 150px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  background-color: #fcfcfc;
  padding: 16px 16px 12px 16px;
  display: flex;
  flex-direction: column;
}

.mprethread__top {
  display: flex;
}

.mprethread__top .avatar-icon {
  display: flex;
}

.mprethread__label {
  margin-left: auto;
}

.mprethread__bottom {
  margin-top: auto;
}

.mprethread__name {
  color: #000;
  display: flex;
  align-items: center;
}

.mprethread__name .notification {
  margin-left: 8px;
}

.mprethread__position {
  font-size: 12px;
  margin-top: 5px;
}

.mprethread--main {
  height: 165px;
}

.mcreator {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: #fff;
  padding: 24px 12px;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.mcreator__label {
  display: flex;
  position: absolute;
  top: 8px;
  right: 8px;
}

.mcreator__role {
  color: #53565a;
}

.mcreator__name,
.mcreator__role {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
  /*! autoprefixer: on */
  overflow: hidden;
  word-break: break-word;
  width: 50%;
  margin-right: 10px;
  margin-left: 10px;
}

.mcreator__hover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.mcreator:hover .mcreator__hover,
.mcreator.active .mcreator__hover {
  opacity: 1;
  visibility: visible;
  transition: all 0.3s;
}

.mcreator__profile {
  display: inline-block;
  background-color: #fff;
  color: #a7a8a9;
  padding: 12px 0;
  font-size: 11px;
}

.mcreator__profile:hover {
  color: #009ace;
}

.mcreator__action {
  margin-top: 43px;
}

.mcreator__edit {
  margin-right: 6px;
}

.mcreator__delete {
  margin-left: 6px;
}

.mcreator__edit,
.mcreator__delete {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-radius: 50%;
  font-size: 20px;
}

.mcreator__edit:hover,
.mcreator__delete:hover {
  color: #fff;
}

.step {
  display: inline-flex;
  align-items: center;
}

.step-number {
  background-color: #000;
  border-radius: 4px;
  border: none;
  color: #fff;
  font-weight: 300;
  width: 32px;
  height: 32px;
  text-align: center;
  margin: 0 8px;
}

.step-number::-webkit-outer-spin-button,
.step-number::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.step-number:focus {
  outline: none;
}

.step-button {
  width: 24px;
  height: 24px;
  border: 1px solid #000;
  border-radius: 4px;
  color: #000;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.step-button:hover {
  cursor: pointer;
}

.step-button-group {
  border: 1px solid #000;
  border-radius: 4px;
}

.step-button-group .step-button {
  border: none;
  width: 22px;
  height: 22px;
}

/*notication new message*/

.notification {
  display: inline-block;
  height: 15px;
  line-height: 13px;
  font-size: 10px;
  font-weight: 300;
  padding: 0 4px;
  min-width: 15px;
  text-align: center;
}

.notification--blue {
  background-color: #009ace;
  color: #fff;
}

.notification--black {
  background-color: #000;
  color: #fff;
}

.notification--round {
  border-radius: 8px;
}

.notification--square {
  border-radius: 4px;
}

.notification--outline-gray {
  border: 1px solid #f0f0f0;
  line-height: 14px;
}

.mmessage-near {
  margin-bottom: 2px;
  margin-left: 32px;
}

.editing .s-text,
.editing .s-file,
.editing .s-audio,
.editing .s-filetext {
  background-color: #009ace;
  color: #ffffff;
  border: none;
}

.mcommment.border-editing {
  border: 1px solid #009ace;
}

.editing .s-file .icon,
.reply .s-file .icon {
  color: #ffffff;
}

.reply .s-text,
.reply .s-audio,
.reply .s-filetext {
  background-color: #009ace;
  color: #ffffff;
  border: 1px solid #009ace;
}

.sselect-wrapper .SelectClass,
.sselect-wrapper .SumoUnder {
  visibility: inherit;
}

/* Modal case folder */
.folder-modal {
  background-color: rgba(0, 0, 0, 0.8);
}

.folder-modal .modal-content {
  border-radius: 12px;
}
.folder-modal .modal-header {
  /*display: flex;*/
  position: relative;
  padding: 24px 24px 0;
  border-bottom: 0px solid #e5e5e5;
}
.folder-modal .modal-title {
  font-size: 16px;
  color: #000000;
  font-weight: 400;
}

/*.folder-modal .modal-header .close span{*/
/*    font-size: 18px;*/
/*    position: absolute;*/
/*    right: 24px;*/
/*    top: 24px;*/
/*}*/

.folder-modal .modal-body {
  padding: 0 24px;
}

.tree-folder {
  padding: 16px;
  border-radius: 6px;
}

.foldertreeview {
  margin-bottom: 0;
}

.menufilter {
  font-size: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.foldertreeview ul {
  display: none;
  margin: 15px -16px;
  list-style: none;
}

.foldertreeview ul.expanded {
  display: block;
  margin: 0;
}

.foldertreeview > li:first-child {
  display: block !important;
}

.foldertreeview li,
.foldertreeview a {
  color: #333;
  text-decoration: none;
  cursor: pointer;
}

.foldertreeview i.glyphicon {
  margin-right: 8px;
  color: #53565a;
}

.subactivated .list-group {
  margin: 0;
}

.expanded .list-group-item {
  padding-left: 22px;
  color: #53565a;
}

.list-sub {
  display: flex;
  align-items: center;
}

.list-group-item {
  border: none;
  padding: 7px 0;
  font-size: 12px;
  color: #53565a;
}

.list-group-item .icon--sicon-download {
  position: relative;
  bottom: 8px;
  color: #a7a8a9;
}

.list-group-item.list-sub .icon--sicon-download {
  bottom: 0;
}

.hasSub {
  color: #53565a;
}

.list-group .mfolder__sub {
  padding: 4px 0;
  display: block;
}

.list-group-item.subactivated .mfolder__sub {
  display: block !important;
}

.tfile-item-content {
  cursor: pointer;
  margin-bottom: 0px;
  padding-left: 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

/* Add offer */
.messenger-add-row {
  background-color: #ffffff;
  border: 1px dashed #d3d3d3;
  border-radius: 6px;
  padding: 16px;
  text-align: center;
  display: block;
  cursor: pointer;
  margin-top: 0;
  margin-bottom: 8px;
  color: #a7a8a9;
}

.messenger-add-icon .icon {
  font-size: 20px;
}

.messenger-add-icon p {
  font-size: 12px;
  line-height: 18px;
  margin-bottom: 0;
}

.mscene__user {
  display: flex;
  align-items: center;
}

.mscene__user .icon {
  margin: 0 8px;
}

.messenger-add-row:hover {
  background-color: #009ace;
  color: #f0f0f0 !important;
}
/* End add offer */

/* Button edit, delete offer */
.accordion-header .scene-title__action {
  display: none;
}

.accordion-header .active-action-offers {
  display: flex;
}

.scene-title__action {
}
.scene-title__action .button-edit_offer,
.scene-title__action .button-delete_offer {
  margin-right: 8px;
  width: 24px;
  height: 24px;
  background-color: rgba(167, 168, 169, 0.2);
  border-radius: 50%;
  color: #a7a8a9;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.accordion .accordion-heading:not(.collapsed) .button-edit_offer .icon,
.accordion .accordion-heading:not(.collapsed) .button-delete_offer .icon {
  transform: rotate(0deg);
}

.accordion .accordion-heading .button-edit_offer .icon,
.accordion .accordion-heading .button-delete_offer .icon {
  margin: 0 !important;
}
/* End button edit, delete offer */

/* Collapse */
.martist .mcolumn--right .mcolumn-header {
  display: block;
  margin-bottom: 0;
}

.mcolumn-header {
  position: relative;
  z-index: 9999;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: #a7a8a9;
  height: auto;
}

.mcolumn-header:hover {
  cursor: pointer;
  color: #009ace;
}

.mcolumn-header .icon {
  font-size: 24px;
  margin-right: 8px;
}

.mcolumn-next {
  display: block;
}

.mcolumn-header-toggle.active {
  transform: rotate(180deg);
  transition: all 0.3s;
}

.mcolumn-header .mcolumn-header-toggle {
  font-size: 16px;
  display: inline-flex;
}

.mcolumn--right.active {
  width: 55px;
  transition: all 0.3s;
  flex: 0 0 55px;
}

.mcolumn--right {
  width: 268px;
  transition: all 0.3s;
  overflow: hidden;
}

.mcolumn--wrap {
  display: flex;
  width: calc(100% - 268px);
  flex-wrap: wrap;
  transition: all 0.3s;
}

.mcolumn--wrap.active {
  width: calc(100% - 55px);
  transition: all 0.3s;
}

.mcolumn-content {
  height: 80vh;
  transform: translateX(0);
  visibility: visible;
  opacity: 1;
  transition: all 0.3s;
}

.mcolumn-content.active {
  transform: translateX(100%);
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s;
}

.mcolumn-toggle {
  display: flex;
  transform: rotate(0deg);
  justify-content: flex-end;
  color: #a7a8a9;
}

.mcolumn-toggle:hover {
  color: #009ace;
}

@media (max-width: 992px) {
  .mcolumn--wrap {
    width: 100%;
  }

  .messenger-detail {
    margin-top: 40px !important;
  }

  .mcolumn--wrap.active {
    width: 100%;
  }

  .mcolumn--right {
    margin-top: 40px;
    width: 100%;
  }

  .mcolumn-toggle {
    display: none;
  }

  .mcolumn-back {
    display: block;
  }
}
/* End collapse */

/* Search */
.pd-search {
  align-items: center;
}
.pd-search-keyword .sform-group__append-before {
  width: 0;
  display: none;
}

.sform-group__icon {
  margin-right: 12px;
  text-align: right;
}

.sform-group__icon .icon {
  font-size: 16px;
  color: #a7a8a9;
}

input::-webkit-calendar-picker-indicator {
  opacity: 0;
}

.count--list-offers-search {
  font-size: 13px;
  font-weight: 400;
  color: #000000;
}

.select:focus,
.select:active {
  border: 1px solid #d3d3d3;
}

textarea.sform-control--input {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 13px;
  /* line-height: 200%; */
  color: #000000;
  resize: none;
}
/* End search */

@media (max-width: 992px) {
  .mcolumn--main .mmessage-list {
    height: calc(100% - 120px);
  }
}

/* Hover icon */
.sform-group__icon .icon:hover,
.messenger-help .icon:hover,
.button-edit_offer:not(.message-first__message) .icon:hover,
.button-delete_offer .icon:hover {
  color: #009ace !important;
}
/* End hover icon */

/* Folder msg */
.messager-folder .s-file--file,
.s-audio.active .messager-folder .s-file--file:hover {
  background-color: #a7a8a9;
  color: #ffffff;
  border: 1px solid #a7a8a9;
}

.s-file--file {
  /* border: 1px solid #f0f0f0; */
}

.messager-folder .s-file--file .icon {
  color: #ffffff;
}

.messager-folder .s-file--file:hover {
  background-color: #009ace;
  cursor: pointer;
  border: 1px solid #009ace;
}
/* End folder msg */

/* Style datetime cmt */
.mmessage-time {
  font-family: "A+mfCv-AXISラウンド 50 L StdN", "Noto Sans Japanese",
    "sans-serif";
  font-size: 8px;
  color: #a7a8a9;
}
/* End style datetime cmt */
.mheader-toggle {
  border: none !important;
  padding: 27px 16px 13px;
  margin-bottom: 0;
}

.minfo-accept__policy .form-check-label {
  margin-bottom: 0;
}

/* Count icon avartar */
/*.mmessage-user-seen .notification {*/
.notification.notification--outline-gray {
  font-size: 8px;
  line-height: 12px;
  color: #a7a8a9;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 0 8px;
}
/* End count icon avartar */

/* Message owner */
.mmessage-user-seen.seen--owner {
  width: 32px;
}

.mmessage-user-seen.seen--owner:not(:first-child) {
  margin-left: -15px;
}

.mmessage-user-seen.seen--owner:first-child .avatar {
  border: 2px solid transparent;
}

.mmessage-user-seen.seen--owner:not(:first-child) .avatar {
  background-color: #fff;
  border: 2px solid #fff;
}

.mmessage-user-count {
  width: 32px;
  font-family: "A+mfCv-AXISラウンド 50 R StdN";
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  padding: 4px 0;
  color: #fff;
  background-color: #a5a6a7;
  border: 2px solid #fff;
  border-radius: 50%;
  margin-left: -15px;
  text-align: center;
}

.tfile-sheading {
  margin-bottom: 10px;
}

.notification.notification--border-gray {
  font-size: 8px;
  line-height: 12px;
  color: #a7a8a9;
  background-color: #fff;
  border: 1px solid #a7a8a9;
  border-radius: 12px;
  padding: 0 4px;
}
/* End message owner */

/* Drag & drop file */

.modal.popup-container .modal-dialog.popup-dialog {
  position: relative;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}

.popup-container .popup-footer {
  padding-top: 16px;
}

.popup-container .popup-text {
  font-size: 13px;
  color: #000;
  text-align: center;
}

.account__upload-file {
  display: flex;
  flex-wrap: wrap;
}

.account__file {
  position: relative;
  max-width: 170px;
  display: flex;
  align-items: center;
  padding: 8px 25px 8px 16px;
  background-color: #f0f0f0;
  border-radius: 6px;
  margin: 4px;
}

.account__file:first-child {
  margin-left: 0;
}

.account__file .icon {
  font-size: 15px;
  color: #a7a8a9;
}

.account__file .icon--sicon-close {
  position: absolute;
  right: 8px;
  cursor: pointer;
}

.mattach-preview-container .mcommment-file {
  background-color: #f0f0f0;
}

.mattach-preview-container .mcommment-file .determinate {
  background-color: rgba(0, 0, 0, 0.05);
}

.mattach-preview-container .mcommment-file .mcommment-file__name {
  font-size: 11px;
  line-height: 17px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #000;
  max-width: 100px;
}

.account__file-name {
  font-size: 11px;
  line-height: 17px;
  display: block;
  margin: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #000;
  max-width: 100px;
}

.mcommment-file__delete .icon,
.mcommment-file__name .icon {
  color: #53565a;
}

.account_upload-file {
  width: 100%;
}

.account_upload-file .fallback .dz-button .icon,
.account_upload-file .fallback .dz-button p {
  color: #a7a8a9;
}

.account_upload-file #uploadFile {
  padding: 24px;
  cursor: pointer;
  background: var(--white-color);
  border: 1px dashed #d3d3d3;
  border-radius: 6px;
  text-align: center;
  min-height: 53px;
  margin-top: 8px;
}

.account_upload-file .dz-button .icon {
  font-size: 20px;
  color: #53565a;
}

.account_upload-file .dz-button p {
  font-size: 13px;
  line-height: 20px;
  color: #53565a;
  margin-top: 10px;
  margin-bottom: 0;
}

.dropzone .dz-default.dz-message {
  margin: 0;
}

.account_upload-file .account_file {
  display: none;
}

.mattach-preview-container .mattach-previews {
  margin: 4px 0 0;
  padding: 0;
  overflow-y: hidden !important;
}

.dropzone.dz-drag-hover {
  background-color: #009ace !important;
  border: 1px dashed #009ace !important;
  color: white !important;
}

.dropzone.dz-drag-hover .icon,
.dropzone.dz-drag-hover p {
  color: white !important;
}

.account_upload-file .fallback:hover {
  background-color: #a7a8a9 !important;
  transition: 0.2s;
}

.account_upload-file .fallback:hover .icon,
.account_upload-file .fallback:hover p {
  color: white !important;
}

.upload-final-product-file.upload-button-wrapper {
  z-index: 9999;
  /* background-color: rgba(255, 255, 255, 0.7); */
}
/* End drag & drop file */

.s-audio-text,
.s-filedisable.s-filedisable--black,
.s-filedisable--black ~ .s-filetext,
.s-filedisable.s-filedisable--gray,
.s-filedisable--gray ~ .s-filetext {
  word-break: break-word;
  white-space: pre-line;
}

.s-audio-file {
  white-space: normal;
}

.mmessage--sent .s-audio-text {
  margin-right: 0;
  margin-left: auto;
}

.s-audio-icon {
  display: flex;
}

.mmessage--sent .s-audio-icon {
  align-content: flex-start;
}

.file-name--confirm {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  cursor: pointer !important;
  text-overflow: ellipsis;
}

/* System message */
.mmessage-system {
  border-top: 1px solid #009ace;
  border-bottom: 1px solid #009ace;
  background-color: #fff;
  margin: 8px 0;
  cursor: default;
}

.mmessage-system__content {
  font-size: 13px;
  line-height: 200%;
  text-align: center;
  color: #009ace;
  padding: 8px 0;
  width: 100%;
  word-break: break-word !important;
  white-space: pre-line !important;
}
/* End system message */
