<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin: auto; background: none; display: block; shape-rendering: auto; animation-play-state: running; animation-delay: 0s;" width="200px" height="200px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
<g transform="translate(80,50)" style="animation-play-state: running; animation-delay: 0s;">
<g transform="rotate(0)" style="animation-play-state: running; animation-delay: 0s;">
<circle cx="0" cy="0" r="6" fill="#1d3f72" fill-opacity="1" style="animation-play-state: running; animation-delay: 0s;" transform="scale(1.38751 1.38751)">
  <animateTransform attributeName="transform" type="scale" begin="-0.875s" values="1.5 1.5;1 1" keyTimes="0;1" dur="1s" repeatCount="indefinite" style="animation-play-state: running; animation-delay: 0s;"></animateTransform>
  <animate attributeName="fill-opacity" keyTimes="0;1" dur="1s" repeatCount="indefinite" values="1;0" begin="-0.875s" style="animation-play-state: running; animation-delay: 0s;"></animate>
</circle>
</g>
</g><g transform="translate(71.21320343559643,71.21320343559643)" style="animation-play-state: running; animation-delay: 0s;">
<g transform="rotate(45)" style="animation-play-state: running; animation-delay: 0s;">
<circle cx="0" cy="0" r="6" fill="#1d3f72" fill-opacity="0.875" style="animation-play-state: running; animation-delay: 0s;" transform="scale(1.45001 1.45001)">
  <animateTransform attributeName="transform" type="scale" begin="-0.75s" values="1.5 1.5;1 1" keyTimes="0;1" dur="1s" repeatCount="indefinite" style="animation-play-state: running; animation-delay: 0s;"></animateTransform>
  <animate attributeName="fill-opacity" keyTimes="0;1" dur="1s" repeatCount="indefinite" values="1;0" begin="-0.75s" style="animation-play-state: running; animation-delay: 0s;"></animate>
</circle>
</g>
</g><g transform="translate(50,80)" style="animation-play-state: running; animation-delay: 0s;">
<g transform="rotate(90)" style="animation-play-state: running; animation-delay: 0s;">
<circle cx="0" cy="0" r="6" fill="#1d3f72" fill-opacity="0.75" style="animation-play-state: running; animation-delay: 0s;" transform="scale(1.01251 1.01251)">
  <animateTransform attributeName="transform" type="scale" begin="-0.625s" values="1.5 1.5;1 1" keyTimes="0;1" dur="1s" repeatCount="indefinite" style="animation-play-state: running; animation-delay: 0s;"></animateTransform>
  <animate attributeName="fill-opacity" keyTimes="0;1" dur="1s" repeatCount="indefinite" values="1;0" begin="-0.625s" style="animation-play-state: running; animation-delay: 0s;"></animate>
</circle>
</g>
</g><g transform="translate(28.786796564403577,71.21320343559643)" style="animation-play-state: running; animation-delay: 0s;">
<g transform="rotate(135)" style="animation-play-state: running; animation-delay: 0s;">
<circle cx="0" cy="0" r="6" fill="#1d3f72" fill-opacity="0.625" style="animation-play-state: running; animation-delay: 0s;" transform="scale(1.07501 1.07501)">
  <animateTransform attributeName="transform" type="scale" begin="-0.5s" values="1.5 1.5;1 1" keyTimes="0;1" dur="1s" repeatCount="indefinite" style="animation-play-state: running; animation-delay: 0s;"></animateTransform>
  <animate attributeName="fill-opacity" keyTimes="0;1" dur="1s" repeatCount="indefinite" values="1;0" begin="-0.5s" style="animation-play-state: running; animation-delay: 0s;"></animate>
</circle>
</g>
</g><g transform="translate(20,50.00000000000001)" style="animation-play-state: running; animation-delay: 0s;">
<g transform="rotate(180)" style="animation-play-state: running; animation-delay: 0s;">
<circle cx="0" cy="0" r="6" fill="#1d3f72" fill-opacity="0.5" style="animation-play-state: running; animation-delay: 0s;" transform="scale(1.13751 1.13751)">
  <animateTransform attributeName="transform" type="scale" begin="-0.375s" values="1.5 1.5;1 1" keyTimes="0;1" dur="1s" repeatCount="indefinite" style="animation-play-state: running; animation-delay: 0s;"></animateTransform>
  <animate attributeName="fill-opacity" keyTimes="0;1" dur="1s" repeatCount="indefinite" values="1;0" begin="-0.375s" style="animation-play-state: running; animation-delay: 0s;"></animate>
</circle>
</g>
</g><g transform="translate(28.78679656440357,28.786796564403577)" style="animation-play-state: running; animation-delay: 0s;">
<g transform="rotate(225)" style="animation-play-state: running; animation-delay: 0s;">
<circle cx="0" cy="0" r="6" fill="#1d3f72" fill-opacity="0.375" style="animation-play-state: running; animation-delay: 0s;" transform="scale(1.20001 1.20001)">
  <animateTransform attributeName="transform" type="scale" begin="-0.25s" values="1.5 1.5;1 1" keyTimes="0;1" dur="1s" repeatCount="indefinite" style="animation-play-state: running; animation-delay: 0s;"></animateTransform>
  <animate attributeName="fill-opacity" keyTimes="0;1" dur="1s" repeatCount="indefinite" values="1;0" begin="-0.25s" style="animation-play-state: running; animation-delay: 0s;"></animate>
</circle>
</g>
</g><g transform="translate(49.99999999999999,20)" style="animation-play-state: running; animation-delay: 0s;">
<g transform="rotate(270)" style="animation-play-state: running; animation-delay: 0s;">
<circle cx="0" cy="0" r="6" fill="#1d3f72" fill-opacity="0.25" style="animation-play-state: running; animation-delay: 0s;" transform="scale(1.26251 1.26251)">
  <animateTransform attributeName="transform" type="scale" begin="-0.125s" values="1.5 1.5;1 1" keyTimes="0;1" dur="1s" repeatCount="indefinite" style="animation-play-state: running; animation-delay: 0s;"></animateTransform>
  <animate attributeName="fill-opacity" keyTimes="0;1" dur="1s" repeatCount="indefinite" values="1;0" begin="-0.125s" style="animation-play-state: running; animation-delay: 0s;"></animate>
</circle>
</g>
</g><g transform="translate(71.21320343559643,28.78679656440357)" style="animation-play-state: running; animation-delay: 0s;">
<g transform="rotate(315)" style="animation-play-state: running; animation-delay: 0s;">
<circle cx="0" cy="0" r="6" fill="#1d3f72" fill-opacity="0.125" style="animation-play-state: running; animation-delay: 0s;" transform="scale(1.32501 1.32501)">
  <animateTransform attributeName="transform" type="scale" begin="0s" values="1.5 1.5;1 1" keyTimes="0;1" dur="1s" repeatCount="indefinite" style="animation-play-state: running; animation-delay: 0s;"></animateTransform>
  <animate attributeName="fill-opacity" keyTimes="0;1" dur="1s" repeatCount="indefinite" values="1;0" begin="0s" style="animation-play-state: running; animation-delay: 0s;"></animate>
</circle>
</g>
</g>
<!-- [ldio] generated by https://loading.io/ --></svg>