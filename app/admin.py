# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.contrib import admin
from .models import (
    Product, ProductScene, Scene, SceneTitle, SceneTitleBookmark, 
    BookmarkListBookMarks, Variation, SceneShareLink, SceneTaken, 
    SceneTakenScenes, DownloadedScene, SceneComment, SceneCommentFolder, 
    SceneCommentFile, DownloadedSceneComment, SceneCommentReceiver, 
    OfferCreator, OfferMessage, OfferMessageReceiver, ChatRoom, 
    ProductOrder, ProductOrderUpload, PreviewCommentOrder, ProductComment, 
    ProductCommentFolder, ProductCommentFile, DownloadedProductComment, 
    PreviewProductComment, ProductCommentDownloaded, OfferProduct, 
    SelectionOffer, ProductMessage, ProductMessageFolder, ProductMessageFile, 
    FormContractAndPlan, DownloadedProductMessageFile, MessageReceiver, 
    PlanOffer, VariationOffer, Post, PostItem, BlockListArtist, 
    OfferProject, OfferUser, ListWork, SaleContentListWork, TopicGallery,
    TopicTag, Category, TopicCategory, SelectionGallery, SaleContentSelection, 
    SectionCredit, ItemSectionCredit, OrderData, DraftMessage, 
    UserOnlineStatus, UserProductCharge, CreatorOfferFile, 
    ColorProjectSetting, FontProjectSetting, ReviewOffer, MediaConvertJob
)

admin.site.register([
    ProductScene, Scene, SceneTitle, SceneTitleBookmark, 
    BookmarkListBookMarks, Variation, SceneShareLink, SceneTaken, 
    SceneTakenScenes, DownloadedScene, SceneComment, SceneCommentFolder, 
    SceneCommentFile, DownloadedSceneComment, SceneCommentReceiver, 
    OfferCreator, OfferMessage, OfferMessageReceiver, ChatRoom, 
    ProductOrder, ProductOrderUpload, PreviewCommentOrder, ProductComment, 
    ProductCommentFolder, ProductCommentFile, DownloadedProductComment, 
    PreviewProductComment, ProductCommentDownloaded, OfferProduct, 
    SelectionOffer, ProductMessage, ProductMessageFolder, ProductMessageFile, 
    FormContractAndPlan, DownloadedProductMessageFile, MessageReceiver, 
    PlanOffer, VariationOffer, Post, PostItem, BlockListArtist, 
    OfferProject, OfferUser, ListWork, SaleContentListWork, 
    TopicTag, Category, TopicCategory, SelectionGallery, SaleContentSelection, 
    SectionCredit, ItemSectionCredit, OrderData, DraftMessage, 
    UserOnlineStatus, UserProductCharge, CreatorOfferFile, 
    ColorProjectSetting, FontProjectSetting, ReviewOffer
])

@admin.register(TopicGallery)
class TopicGalleryAdmin(admin.ModelAdmin):
    list_display = ('title', 'overview', 'description', 'order')
    search_fields = ('title', 'overview', 'description')

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'last_update', 'max_scene', 'current_scene', 'progress')
    search_fields = ('name', 'code_name', 'client_name')
    list_filter = ('genres', 'acr_status')


@admin.register(MediaConvertJob)
class MediaConvertJobAdmin(admin.ModelAdmin):
    """
    Admin interface for MediaConvertJob
    Note: Status values are flexible - no enum constraints
    """
    list_display = ('original_object_key_short', 'status', 'job_id_short', 'file_size_mb', 'duration_formatted', 'created')
    list_filter = ('status', 'created', 'modified')
    search_fields = ('original_object_key', 'job_id', 'converted_media_key')
    readonly_fields = ('id', 'created', 'modified', 'job_id')
    ordering = ('-created',)
    
    fieldsets = (
        ('File Information', {
            'fields': ('id', 'original_object_key', 'status')
        }),
        ('AWS Job Details', {
            'fields': ('job_id', 'file_size', 'duration')
        }),
        ('Conversion Results', {
            'fields': ('converted_media_key', 'waveform_data_key'),
            'classes': ('collapse',)
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created', 'modified'),
            'classes': ('collapse',)
        })
    )
    
    def original_object_key_short(self, obj):
        """Show shortened version of object key for better display"""
        if len(obj.original_object_key) > 50:
            return obj.original_object_key[:47] + '...'
        return obj.original_object_key
    original_object_key_short.short_description = 'Object Key'
    
    def job_id_short(self, obj):
        """Show shortened version of job ID"""
        if obj.job_id and len(obj.job_id) > 20:
            return obj.job_id[:17] + '...'
        return obj.job_id or '-'
    job_id_short.short_description = 'Job ID'
    
    def file_size_mb(self, obj):
        """Show file size in MB"""
        if obj.file_size:
            return f"{obj.file_size / (1024*1024):.1f} MB"
        return '-'
    file_size_mb.short_description = 'File Size'
    
    def duration_formatted(self, obj):
        """Show duration in readable format"""
        if obj.duration:
            minutes = int(obj.duration // 60)
            seconds = int(obj.duration % 60)
            return f"{minutes}:{seconds:02d}"
        return '-'
    duration_formatted.short_description = 'Duration'
    
    def has_add_permission(self, request):
        """Disable adding new records through admin - should be created by system"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Allow deletion for cleanup purposes"""
        return request.user.is_superuser
    
    def get_readonly_fields(self, request, obj=None):
        """Make most fields readonly, only allow status changes for debugging"""
        if request.user.is_superuser:
            return ('id', 'created', 'modified', 'job_id')
        else:
            return ('id', 'original_object_key', 'job_id', 'file_size', 'duration', 
                   'created', 'modified', 'converted_media_key', 'waveform_data_key')
    
    actions = ['retry_failed_conversions', 'mark_as_pending']
    
    def retry_failed_conversions(self, request, queryset):
        """Custom action to retry failed conversions (adjust status values as needed)"""
        failed_jobs = queryset.filter(status__in=['failed', 'error'])
        count = failed_jobs.update(status='pending', error_message=None)
        self.message_user(request, f'{count} jobs marked for retry.')
    retry_failed_conversions.short_description = 'Retry failed conversions'
    
    def mark_as_pending(self, request, queryset):
        """Mark selected jobs as pending for re-processing"""
        count = queryset.update(status='pending', error_message=None)
        self.message_user(request, f'{count} jobs marked as pending.')
    mark_as_pending.short_description = 'Mark as pending'

 
