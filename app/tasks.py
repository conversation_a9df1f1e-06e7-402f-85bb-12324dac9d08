import json
import time

import jwt
import vlc
import datetime
from celery.schedules import crontab
from django.contrib.auth.tokens import default_token_generator
from django.core.exceptions import ObjectDoesNotExist
from django.core.mail import send_mail
from django.template import loader
from django.urls import reverse
from django.utils.encoding import force_bytes
from django.utils.html import strip_tags
from django.utils.http import urlsafe_base64_encode
from moviepy.video.io.VideoFileClip import VideoFileClip

from app.models import SceneComment, Scene, SceneTitle, PreviewVideo, PreviewScene, ProductOrder, Product, Variation, \
    PreviewProductComment, OfferProduct, ProductComment, OfferCreator, SceneCommentFile, MessageFolder, \
    ProductCommentFolder, \
    SceneCommentFolder, OfferMessage, ProductMessageFolder, ProductMessage, MessageFile, ProductMessageFile, \
    ProductCommentFile, OfferProject, OfferUser

from common.base_tasks import make_new_version_of_img
from landingPage.models import Contact<PERSON>older, ContactFile, ContactInfo
from voice.celery import app
from accounts.models import AuthUser, ProductUser
import os, base64
from django.db.models import Q
from django.conf import settings
import boto3
from common.custom_acr import customrecognize
from django.utils.translation import gettext as _

ROLE_DEFAULT_AVATAR = {
    AuthUser.CREATOR: '/static/images/default-avatar-creator.png',
    AuthUser.MASTERADMIN: '/static/images/default-avatar-master-admin.png',
    AuthUser.MASTERCLIENT: '/static/images/default-avatar-client.png',
}
DEFAULT_AVATAR = '/static/images/default-avt.png'


@app.task
def send_verification_email(list_user, comment, secure, host, url):
    list_user_name = AuthUser.objects.filter(id__in=list_user, is_active=True).values("id", "email", "username")
    subject = "【SOREMO】新しいメッセージが届いています。"
    comment = SceneComment.objects.get(pk=comment)
    scene = Scene.objects.get(scene_id=str(comment.scene))
    user_comment = AuthUser.objects.get(id=comment.owner_id)
    from_email = os.getenv('EMAIL_ADDRESS')

    for item in list_user_name:
        user = AuthUser.objects.get(id=item["id"])

        context = {
            'user': user,
            'uidb64': urlsafe_base64_encode(force_bytes(user.pk)),
            'token': default_token_generator.make_token(user),
            'secure': secure,
            'host': host,
            'comment': comment,
            'product': scene.product,
            "product_scene": scene.product_scene,
            "scene_title": scene.title,
            'url': url,
            "user_comment": user_comment
        }
        html_message = loader.render_to_string('emails/email-template-view1.html', context).strip()
        body = strip_tags(html_message)
        send_mail(subject, message=body, from_email=from_email,
                  recipient_list=[item["email"]], fail_silently=False, html_message=html_message)


@app.task
def send_email_when_upload_video(list_user, scene_id, secure, host):
    list_user_name = AuthUser.objects.filter(id__in=list_user, is_active=True).values("id", "email")
    subject = "【SOREMO】新しい演出が届いています。"
    scene_obj = Scene.objects.get(scene_id=scene_id)

    title_id = scene_obj.title_id or ''
    product_id = scene_obj.product_id or ''
    product_scene_id = scene_obj.product_scene_id or ''
    if title_id:
        url = reverse('app:scene_list')
    else:
        url = reverse('app:scene_index')
    url += '?product_id={}&title_id={}&product_scene_id={}'.format(
        product_id, title_id, product_scene_id,
    )
    from_email = os.getenv('EMAIL_ADDRESS')
    for item in list_user_name:
        user = AuthUser.objects.get(id=item["id"])

        context = {
            'user': user,
            'uidb64': urlsafe_base64_encode(force_bytes(item['id'])),
            'token': default_token_generator.make_token(user),
            'secure': secure,
            'host': host,
            'product': scene_obj.product,
            'scene': scene_obj,
            "product_scene": scene_obj.product_scene,
            "scene_title": scene_obj.title,
            "scene_created": scene_obj.created.strftime('%Y/%m/%d/ %H:%M'),
            'url': url
        }

        html_message = loader.render_to_string('emails/email-template-view2.html', context).strip()
        body = strip_tags(html_message)
        send_mail(subject, message=body, from_email=from_email,
                  recipient_list=[item["email"]], fail_silently=False, html_message=html_message)


@app.task
def update_preview(scene_comment_id_list, product_id, user_id):
    need_update = False
    for comment in scene_comment_id_list:
        try:
            ps, created = PreviewScene.objects.get_or_create(comment_id=comment, owner_id=user_id)
            if created and not need_update:
                need_update = True
        except:
            pass
    if need_update:
        from accounts.services import update_product_user_new_video
        users = AuthUser.objects.filter(id=user_id)
        products = Product.objects.filter(pk=product_id)
        update_product_user_new_video(products, users)


@app.task
def send_email_create_order(list_user, type, product_id, host, url):
    from app.templatetags.util import get_image
    
    button = "WEBで見る"
    content = ""
    content1 = ""
    if type == "create":
        subject = "【SOREMO】新しいオーダーが届いています。"
        button = "ORDERを見る"
    elif type == "message":
        subject = " 【SOREMO】新しいメッセージが届いています。"
    elif type == "estimate":
        subject = " 【SOREMO】見積書のご案内"
        content = "SOREMOサービスをご利用頂き、ありがとうございます。"
        content1 = "御見積書の準備ができましたのでご案内申し上げます。"
    elif type == "bill":
        subject = "【SOREMO】請求書のご案内"
        content = "SOREMOサービスをご利用頂き、ありがとうございます。"
        content1 = "御利用明細および御請求書の準備ができましたのでご案内申し上げます。"
    product = Product.objects.get(product_id=product_id)
    from_email = os.getenv('EMAIL_ADDRESS')
    for item in list_user:
        user = AuthUser.objects.get(id=item)
        if user.role == 'admin' or not user.is_active:
            continue

        context = {
            'user': user,
            'host': host,
            'url': url,
            "content": content,
            "content1": content1,
            "button": button,
            'product_image': get_image(product, host),
            "product_id": product_id
        }
        html_message = loader.render_to_string('emails/email_template_order.html', context).strip()
        body = strip_tags(html_message)
        send_mail(subject, message=body, from_email=from_email,
                  recipient_list=[user.email], fail_silently=False, html_message=html_message)


@app.task
def update_fps_when_create_scene(list_scene):
    for scene in Scene.objects.filter(scene_id__in=list_scene):
        try:
            video = VideoFileClip(scene.movie.url)
            fps = video.fps
            video.close()
            Scene.objects.filter(pk=scene.scene_id).update(fps_movie=round(fps))
        except Exception as e:
            raise e


@app.task
def send_invitation_email(sender_id, sender_name, product_id, user_invited_id, path):
    from app.templatetags.util import get_image
    
    host = settings.HOST
    sender = AuthUser.objects.get(pk=sender_id)
    product = Product.objects.get(pk=product_id)
    user_invited = AuthUser.objects.get(pk=user_invited_id)
    sender_avt = get_user_url(sender, host, 'medium')
    user_invited_avt = get_user_url(user_invited, host, 'medium')

    subject = f'【SOREMO】{sender_name}よりプロジェクトの招待が届いています。'
    context = {
        'sender_name': sender_name,
        'sender_avt': sender_avt,
        'product': product,
        'user_invited': user_invited,
        'user_invited_avt': user_invited_avt,
        'path': path,
        'host': host,
        'product_image': get_image(product, host),
    }
    html_message = loader.render_to_string('emails/email-template-invitation-user.html', context).strip()
    from_email = os.getenv('EMAIL_ADDRESS')
    body = strip_tags(html_message)
    send_mail(
        subject=subject,
        message=body,
        from_email=from_email,
        recipient_list=[user_invited.email],
        fail_silently=False,
        html_message=html_message
    )


@app.task
def check_acr_for_scene_comment_b64(scene_comment_id, b64encoded_buffer, duration=-1):
    try:
        check_file_buffer = base64.b64decode(b64encoded_buffer)
        acr_check = None
        if duration > 0:
            acr_check = '\n'.join(str(result) for result in customrecognize.recognize_by_filebuffer_with_duration(
                re_config=customrecognize.config, buffer=check_file_buffer, duration=duration))
        else:
            acr_check = '\n'.join(str(result) for result in
                                  customrecognize.recognize_by_filebuffer(re_config=customrecognize.config,
                                                                          buffer=check_file_buffer))
        if acr_check is not None:
            scene_comments = SceneComment.objects.filter(pk=scene_comment_id).update(acr_result=acr_check)
    except:
        pass


def exchangeNumberToTime(time_ms):
    if time_ms == 0:
        return '00:00'
    seconds = round(int(time_ms) / 1000)
    minutes = seconds // 60
    seconds = seconds % 60
    return '%02d:%02d' % (minutes, seconds)


def handlePlayOffset(play_offset_ms):
    if play_offset_ms:
        play_offset_result = ''
        for play_offset in play_offset_ms:
            start = play_offset[0]
            end = play_offset[1]
            start_time = exchangeNumberToTime(start)
            end_time = exchangeNumberToTime(end)
            play_offset_result += f'{start_time}-{end_time},'
        play_offset_result = play_offset_result.strip(',')
        return play_offset_result
    else:
        return ""


def update_acr_result_object(type, object_id, acr_check):
    try:
        model_name = Scene if type == 'scene' else SceneCommentFile
        target_object = model_name.objects.get(pk=object_id)
        if not len(acr_check):
            target_object.acr_result = ''
            target_object.save()
            return

        str_result = ''
        for obj_result in acr_check:
            arr_result = []
            arr_result.append(str(obj_result.get('score') or ''))
            duration = obj_result.get('duration_ms', 0)
            arr_result.append(exchangeNumberToTime(int(duration)))

            title = str(obj_result.get('title') or '')
            if type == 'scene' and len(title) > 6 and title[0:6] == 'movie/':
                title = title[6:]
            arr_result.append(title)
            play_offset_ms = obj_result.get('play_offset_ms') or []
            sample_begin_time = int(obj_result.get('sample_begin_time_offset_ms') or 0)
            sample_end_time = int(obj_result.get('sample_end_time_offset_ms') or 0)
            arr_result.append(handlePlayOffset(play_offset_ms))
            arr_result.append(exchangeNumberToTime(sample_begin_time))
            arr_result.append(exchangeNumberToTime(sample_end_time))

            str_result += '::'.join(arr_result)
            str_result += ';;'
        str_result = str_result.strip(';;')
        target_object.acr_result = str_result
        target_object.save()
    except:
        return


@app.task
def check_acr_for_scene(scene, re_config=None):
    try:
        scene = Scene.objects.get(pk=scene)
        if scene.acr_result != _('acr checking message'):
            return
        if not scene or not scene.movie:
            return
        if not re_config:
            re_config = customrecognize.config
            product = scene.product
            if product:
                re_config['host'] = product.acr_host
                re_config['access_key'] = product.acr_access_key
                re_config['access_secret'] = product.acr_access_secret
        try:
            check_file_buffer = scene.movie.read()
            acr_check = customrecognize.recognize_by_filebuffer(re_config=re_config, buffer=check_file_buffer)
            update_acr_result_object('scene', str(scene.pk), acr_check)
        except:
            error_dict = {'error': customrecognize.ERROR_READFILE_MESSAGE}
            scene.acr_result = json.dumps(error_dict)
            scene.save()
            return
    except:
        raise ValueError('Cannot recognize! Check config and try again!')


@app.task
def check_acr_for_scene_comment(file, re_config=None, duration=-1):
    try:
        file = SceneCommentFile.objects.get(pk=file)
        if file.acr_result != _('acr checking message'):
            return
        if not file:
            return
        comment = file.message
        if not re_config:
            re_config = customrecognize.config
            if comment.scene is not None:
                product = comment.scene.product
            elif comment.scene_title is not None:
                product = comment.scene_title.product_scene.product_scene.first()
            if product:
                re_config['host'] = product.acr_host
                re_config['access_key'] = product.acr_access_key
                re_config['access_secret'] = product.acr_access_secret
            else:
                return
        try:
            check_file_buffer = file.file.read()
            if duration > 0:
                acr_check = customrecognize.recognize_by_filebuffer_with_duration(re_config=re_config,
                                                                                            buffer=check_file_buffer,
                                                                                            duration=duration)
            else:
                acr_check = customrecognize.recognize_by_filebuffer(re_config=re_config,
                                                                              buffer=check_file_buffer)
            update_acr_result_object('file', str(file.pk), acr_check)
        except:
            if file:
                error_dict = {'error': customrecognize.ERROR_READFILE_MESSAGE}
                file.acr_result = json.dumps(error_dict)
                file.save()
    except:
        raise ValueError('Cannot recognize! Check config and try again!')


def update_number_rating(product_scene, number):
    try:
        from django.db.models import F
        product_scene.number_rating -= number
        product_scene.save()
    except:
        pass


@app.task
def check_acr_for_object_with_config(re_config, obj_id, obj_class, duration=-1):
    try:
        objs = None
        check_file_buffer = None
        re_config = re_config
        if obj_class == "Scene":
            objs = Scene.objects.filter(pk=obj_id)
            check_file_buffer = objs.first().movie.read()
        elif obj_class == "SceneComment":
            objs = SceneComment.objects.filter(pk=obj_id)
            check_file_buffer = objs.first().file.read()

        if not objs or not objs.exists():
            return

        acr_check = None

        if duration > 0:
            acr_check = '\n'.join(str(result) for result in
                                  customrecognize.recognize_by_filebuffer_with_duration(re_config=re_config,
                                                                                        buffer=check_file_buffer,
                                                                                        duration=duration))
        else:
            acr_check = '\n'.join(str(result) for result in
                                  customrecognize.recognize_by_filebuffer(re_config=re_config,
                                                                          buffer=check_file_buffer))
        if acr_check is not None:
            objs.update(acr_result=acr_check)
    except:
        raise ValueError("Cannot recognize! Check config and try again!")


@app.task
def check_acr_for_product(product_id):
    try:
        products = Product.objects.filter(pk=product_id)
        if not products.exists():
            return
        product = products.first()
        re_config = customrecognize.config
        if product.acr_host is None or product.acr_access_key is None or product.acr_access_secret is None:
            return

        re_config["host"] = product.acr_host
        re_config["access_key"] = product.acr_access_key
        re_config["access_secret"] = product.acr_access_secret

        if product:
            uncheck_acr_scenes = product.scene_product.filter(Q(acr_result__isnull=True) or Q(acr_result__exact=''))
            uncheck_acr_comments = SceneComment.objects.filter(
                Q(scene__in=product.scene_product.all(), acr_result__isnull=True),
                Q(file__isnull=False)).exclude(file='')
            uncheck_acr_scene_title_comments = SceneComment.objects.exclude(file='').filter(
                file__isnull=False, acr_result__isnull=True, scene_title__in=SceneTitle.objects.filter(
                    product_scene__in=product.scene_list.all()))
            for scene in uncheck_acr_scenes:
                check_acr_for_object_with_config.delay(re_config=re_config, obj_id=scene.pk, obj_class="Scene")
            for comment in uncheck_acr_comments:
                check_acr_for_object_with_config.delay(re_config=re_config, obj_id=comment.pk, obj_class="SceneComment")
            for comment in uncheck_acr_scene_title_comments:
                check_acr_for_object_with_config.delay(re_config=re_config, obj_id=comment.pk, obj_class="SceneComment")
    except:
        raise ValueError("Cannot recognize! Check config and try again!")


@app.task
def update_variation_status(variation):
    try:
        top_scene = variation.scene
        last_scene = top_scene.get_latest_version()
        if last_scene == top_scene:
            comments = SceneComment.objects.filter(
                Q(scene=top_scene) & Q(user__role__in=['master_client', 'client']))

            if comments.exists():
                if comments.filter(resolved=False).exists():
                    variation = top_scene.variation
                    variation.status = '3'
                    variation.save()
                    return
                else:
                    variation = top_scene.variation
                    variation.status = '4'
                    variation.save()
                    return
            else:
                if top_scene.ok:
                    variation = top_scene.variation
                    variation.status = '4'
                    variation.save()
                    return
                else:
                    variation = top_scene.variation
                    variation.status = '1'
                    variation.save()
                    return
        else:
            comments = SceneComment.objects.filter(
                (Q(scene=top_scene) | Q(scene_id__in=top_scene.other_versions.all())) &
                Q(user__role__in=['master_client', 'client']))
            if comments.exists():
                if comments.filter(scene=last_scene):
                    variation = top_scene.variation
                    variation.status = '2'
                    variation.save()
                    return
                elif comments.filter(resolved=False).exists():
                    variation = top_scene.variation
                    variation.status = '3'
                    variation.save()
                    return
                else:
                    variation = top_scene.variation
                    variation.status = '4'
                    variation.save()
                    return
            else:
                variation = top_scene.variation
                variation.status = '2'
                variation.save()
                return
    except:
        pass


@app.task
def update_preview_product(comment_ids, user_id):
    for comment in comment_ids:
        try:
            ps, created = PreviewProductComment.objects.get_or_create(comment_id=comment, owner_id=user_id)
        except:
            pass


@app.task
def send_email_when_create_offer(offer_id, recipients, scheme, host, path, type_mail, email_value):
    from app.templatetags.util import get_image
    
    from_email = os.getenv('EMAIL_ADDRESS')
    master_client = AuthUser.objects.filter(email=email_value).first()
    for recipient_email in recipients:
        try:
            offer = OfferProduct.objects.filter(pk=offer_id).first()
            recipient = AuthUser.objects.filter(email=recipient_email).first()
            recipient_avt = get_user_url(recipient, host, 'medium')
            context = {
                'offer': offer,
                'recipient': recipient,
                'recipient_avt': recipient_avt,
                'url': path,
                'host': host,
                'scheme': scheme,
                'type_mail': type_mail,
                'master_client': master_client
            }
            if type_mail == 'master_client':
                subject = _('【SOREMO】We have received your consultation')
            else:
                subject = f'{master_client.get_display_name()}より新しいオーダーが届いています。'
            html_message = loader.render_to_string('emails/template_email_create_offer_product.html', context).strip()
            body = strip_tags(html_message)
            send_mail(
                subject=subject,
                message=body,
                from_email=from_email,
                recipient_list=[recipient_email],
                fail_silently=False,
                html_message=html_message
            )
        except:
            print(recipients)


@app.task
def send_email_when_create_offer_by_sale(offer_id, recipients, scheme, host, path, type_mail, email_value, album_name,
                                         producer_email):
    from_email = os.getenv('EMAIL_ADDRESS')
    master_client = AuthUser.objects.filter(email=email_value).first()
    producer = AuthUser.objects.filter(email=producer_email).first()
    offer = OfferProduct.objects.filter(pk=offer_id).first()
    for recipient_email in recipients:
        try:
            recipient = AuthUser.objects.filter(email=recipient_email).first()
            recipient_avt = get_user_url(recipient, host, 'medium')
            context = {
                'offer': offer,
                'recipient': recipient,
                'recipient_avt': recipient_avt,
                'url': path,
                'host': host,
                'scheme': scheme,
                'type_mail': type_mail,
                'master_client': master_client,
                'producer': producer,
                'album_name': album_name
            }

            if type_mail != 'master_client':
                subject = f'{master_client.get_display_name()}より、{album_name} へのオーダーが届いています'
            else:
                if producer.user_creator.last().is_direct:
                    subject = f'{album_name}のご相談を承りました'
                else:
                    subject = f'{album_name}へのご相談を承りました'

            html_message = loader.render_to_string('emails/template_email_create_offer_product_by_sale.html',
                                                   context).strip()
            body = strip_tags(html_message)
            send_mail(
                subject=subject,
                message=body,
                from_email=from_email,
                recipient_list=[recipient_email],
                fail_silently=False,
                html_message=html_message
            )
        except:
            print(recipients)


@app.task
def send_email_when_create_offer_by_contact_artist(offer_id, recipients, scheme, host, path, type_mail, email_value,
                                         producer_email):
    from_email = os.getenv('EMAIL_ADDRESS')
    master_client = AuthUser.objects.filter(email=email_value).first()
    producer = AuthUser.objects.filter(email=producer_email).first()
    offer = OfferProduct.objects.filter(pk=offer_id).first()
    artist_name = producer.get_display_name()
    for recipient_email in recipients:
        try:
            recipient = AuthUser.objects.filter(email=recipient_email).first()
            recipient_avt = get_user_url(recipient, host, 'medium')
            context = {
                'offer': offer,
                'recipient': recipient,
                'recipient_avt': recipient_avt,
                'url': path,
                'host': host,
                'scheme': scheme,
                'type_mail': type_mail,
                'master_client': master_client,
                'producer': producer,
                'artist_name': artist_name
            }

            if type_mail != 'master_client':
                if type_mail == 'master_admin':
                    subject = f'{master_client.get_display_name()}より、{artist_name} へのオーダーが届いています'
                else:
                    subject = f'{master_client.get_display_name()}より、プロフィールサイトでオーダーが届いています。'
            else:
                if producer.user_creator.last().is_direct:
                    subject = f'{artist_name}のご相談を承りました'
                else:
                    subject = f'{artist_name}へのご相談を承りました'

            html_message = loader.render_to_string('emails/template_email_create_offer_product_by_contact_artist.html',
                                                   context).strip()
            body = strip_tags(html_message)
            send_mail(
                subject=subject,
                message=body,
                from_email=from_email,
                recipient_list=[recipient_email],
                fail_silently=False,
                html_message=html_message
            )
        except:
            print(recipients)


@app.task
def send_email_when_upload_plan(offer_id, recipient_ids, scheme, host, url):
    from app.templatetags.util import get_avatar
    
    try:
        offer = OfferProduct.objects.get(pk=offer_id)
        receivers = AuthUser.objects.filter(pk__in=recipient_ids)
        subject = "【SOREMO】見積書のご案内"
        from_email = os.getenv('EMAIL_ADDRESS')
        for recipient in receivers:
            recipient_avt = get_avatar(recipient, 'medium', host=host, is_owner=True)
            path = get_path_do_to_project(offer, recipient, url, host)
            context = {
                'offer': offer,
                'user': recipient,
                'master_client_avt': recipient_avt,
                'host': host,
                'scheme': scheme,
                'url': path
            }
            html_message = loader.render_to_string('emails/email-template-upload-plan.html', context).strip()
            body = strip_tags(html_message)
            send_mail(
                subject=subject,
                message=body,
                from_email=from_email,
                recipient_list=[recipient.email],
                fail_silently=False,
                html_message=html_message
            )
    except OfferProduct.DoesNotExist:
        print(offer_id)


@app.task
def send_email_when_upload_plan_and_contract(offer_id, recipient_ids, scheme, host, url):
    from app.templatetags.util import get_avatar
    
    try:
        offer = OfferProduct.objects.get(pk=offer_id)
        receivers = AuthUser.objects.filter(pk__in=recipient_ids)
        subject = "【SOREMO】見積書と契約書のご案内"
        from_email = os.getenv('EMAIL_ADDRESS')
        for recipient in receivers:
            recipient_avt = get_avatar(recipient, 'medium', host=host, is_owner=True)
            path = get_path_do_to_project(offer, recipient, url, host)
            context = {
                'offer': offer,
                'user': recipient,
                'master_client_avt': recipient_avt,
                'host': host,
                'scheme': scheme,
                'url': path
            }
            html_message = loader.render_to_string('emails/email-template-upload-plan-and-contract.html', context).strip()
            body = strip_tags(html_message)
            send_mail(
                subject=subject,
                message=body,
                from_email=from_email,
                recipient_list=[recipient.email],
                fail_silently=False,
                html_message=html_message
            )
    except OfferProduct.DoesNotExist:
        print(offer_id)


@app.task
def send_email_when_upload_contract(offer_id, recipient_ids, scheme, host, url, action_check):
    from app.templatetags.util import get_avatar
    
    try:
        from_email = os.getenv('EMAIL_ADDRESS')
        offer = OfferProduct.objects.get(pk=offer_id)
        receivers = AuthUser.objects.filter(pk__in=recipient_ids)
        for recipient in receivers:
            path = get_path_do_to_project(offer, recipient, url, host)
            recipient_avt = get_avatar(recipient, 'medium', host=host, is_owner=True)
            context = {
                'offer': offer,
                'user': recipient,
                'master_client_avt': recipient_avt,
                'host': host,
                'scheme': scheme,
                'url': path
            }
            if action_check == 'upload_contract':
                subject = _('subject upload contract')
                html_message = loader.render_to_string('emails/email-template-upload-contact.html', context).strip()
            elif action_check == 'upload_bill':
                subject = _('subject upload bill')
                html_message = loader.render_to_string('emails/email-template-upload-bill.html', context).strip()
            else:
                break
            body = strip_tags(html_message)
            send_mail(
                subject=subject,
                message=body,
                from_email=from_email,
                recipient_list=[recipient.email],
                fail_silently=False,
                html_message=html_message
            )
    except OfferProduct.DoesNotExist:
        print(offer_id)


SECRET = settings.SECRET_KEY


def generate_jwt_token_invite_owner(product_id, offer_id, user_invited_id, user_invited_email):
    data = {'user_invited_pk': user_invited_id, 'user_invited_email': user_invited_email, 'product_pk': product_id,
            'offer_pk': offer_id}
    payload = {'exp': int(time.time()) + 86400, 'data': data}
    return jwt.encode(payload, SECRET, algorithm='HS256')


def get_path_do_to_project(offer, recipient, url, host):
    jwt_token = generate_jwt_token_invite_owner(str(offer.project.pk), str(offer.pk), recipient.id,
                                                recipient.email)
    if not recipient.is_active and not recipient.is_verify:
        url = f'/top/invite_user?jwt={jwt_token}&next_url={url}'
    return "{host}{url}".format(host=host, url=url)

@app.task
def send_email_when_create_product(offer_id, product_id, sender_avt, scheme, host, path):
    from app.templatetags.util import get_image
    
    try:
        offer = OfferProduct.objects.filter(pk=offer_id).first()
        product = Product.objects.filter(pk=product_id).first()
        recipient = offer.master_client
        subject = _('subject create offer product')
        context = {
            'offer': offer,
            'user': recipient,
            'master_client_avt': sender_avt,
            'url': path,
            'host': host,
            'scheme': scheme,
            'product_image': get_image(product, host),
        }
        html_message = loader.render_to_string('emails/email-template-create-product.html', context).strip()
        from_email = os.getenv('EMAIL_ADDRESS')
        body = strip_tags(html_message)
        send_mail(
            subject=subject,
            message=body,
            from_email=from_email,
            recipient_list=[recipient.email],
            fail_silently=False,
            html_message=html_message
        )
    except:
        print(offer_id)


@app.task
def send_email_when_new_message(offer_id, message_id, recipients, scheme, host, path):
    from app.templatetags.util import get_image, get_avatar
    
    from_email = os.getenv('EMAIL_ADDRESS')
    offer = OfferProduct.objects.filter(pk=offer_id).first()
    message = ProductMessage.objects.filter(pk=message_id).first()
    if not offer or not message:
        return
    for recipient_id in recipients:
        try:
            message_content = _("More messages have arrived")

            recipient = AuthUser.objects.filter(pk=recipient_id).first()
            sender = message.user
            if not recipient:
                continue
            recipient_avt = get_user_url(recipient, host, 'medium')
            sender_avt = get_user_url(recipient, host, 'medium')
            if recipient.role == AuthUser.MASTERCLIENT:
                recipient_avt = get_avatar(recipient, 'medium', host=host, is_owner=True)
                text = _('Thank you for using the SOREMO service.')
                text1 = _('We are pleased to confirm that you have received a new message.')
            else:
                text = f'{message.user.get_display_name()}{message_content}'
                text1 = ''

            if sender.role == AuthUser.MASTERCLIENT:
                sender_avt = get_avatar(sender, host=host, is_owner=True)
            context = {
                'offer': offer,
                'recipient': recipient,
                'sender': sender,
                'sender_avt': sender_avt,
                'recipient_avt': recipient_avt,
                'product_image': get_image(offer.project, host),
                'host': host,
                'scheme': scheme,
                'url': path,
                'text': text,
                'text1': text1,
                'type': 'new_message',
                'message': message
            }

            html_message = loader.render_to_string('emails/email-template-new-message.html', context).strip()

            subject = f'【SOREMO】{message.user.get_display_name()}{message_content}'

            body = strip_tags(html_message)
            send_mail(
                subject=subject,
                message=body,
                from_email=from_email,
                recipient_list=[recipient.email],
                fail_silently=False,
                html_message=html_message
            )
        except:
            print(recipient_id)


@app.task
def send_email_when_create_comment_product(comment_id, recipient_ids, sender_id, scheme, host, path):
    from app.templatetags.util import get_image
    
    try:
        from_email = os.getenv('EMAIL_ADDRESS')
        sender = AuthUser.objects.filter(pk=sender_id).first()
        comment = ProductComment.objects.filter(pk=comment_id).first()
        product = comment.project
        sender_avt = get_user_url(sender, host, 'medium')
        default_owner_avt = f"{host}/static/images/default-avatar-owner.png"

        text_file = 'メッセージ'
        if comment.files.exists():
            text_file = 'ファイル'
        subject = f'【SOREMO】{sender.get_display_name()}より新しい{text_file}が届いています'

        for recipient_id in recipient_ids:
            recipient = AuthUser.objects.filter(pk=recipient_id).first()
            recipient_avt = get_user_url(recipient, host, 'medium')
            context = {
                'comment': comment,
                'recipient': recipient,
                'sender': sender,
                'sender_avt': sender_avt,
                'recipient_avt': recipient_avt,
                'default_owner_avt': default_owner_avt,
                'url': path,
                'host': host,
                'scheme': scheme,
                'product_image': get_image(product, host),
            }
            html_message = loader.render_to_string('emails/email-template-product-comment.html', context).strip()
            body = strip_tags(html_message)
            send_mail(
                subject=subject,
                message=body,
                from_email=from_email,
                recipient_list=[recipient.email],
                fail_silently=False,
                html_message=html_message
            )
    except:
        print(comment_id)


@app.task
def send_email_when_upload_bill(offer_id, recipient_ids, scheme, host, path):
    from app.templatetags.util import get_avatar
    
    try:
        offer = OfferProduct.objects.get(pk=offer_id)
        receivers = AuthUser.objects.filter(pk__in=recipient_ids)
        subject = "【SOREMO】請求書のご案内"
        from_email = os.getenv('EMAIL_ADDRESS')

        for recipient in receivers:
            recipient_avt = get_avatar(recipient, 'medium', host=host, is_owner=True)
            context = {
                'user': recipient,
                'master_client_avt': recipient_avt,
                'host': host,
                'scheme': scheme,
                'url': path
            }
            html_message = loader.render_to_string('emails/email-template-upload-bill.html', context).strip()
            body = strip_tags(html_message)
            send_mail(
                subject=subject,
                message=body,
                from_email=from_email,
                recipient_list=[recipient.email],
                fail_silently=False,
                html_message=html_message
            )
    except OfferProduct.DoesNotExist:
        print(offer_id)


def update_rating_for_scene_title():
    titles = SceneTitle.objects.all()

    for title in titles:
        scene = Scene.objects.filter(title_id=title.pk).first()
        if scene:
            title.update_rating(scene)


def update_tag_for_scene():
    scene_titles = SceneTitle.objects.filter(tag=3)
    for st in scene_titles:
        Scene.objects.filter(title_id=st.title_id).update(tag=2)


def update_owner_for_comment_productorder():
    for comment in SceneComment.objects.all():
        if comment.owner_id:
            comment.user = AuthUser.objects.get(id=comment.owner_id)
            comment.save()

    for po in ProductOrder.objects.all():
        if po.owner_id:
            po.user = AuthUser.objects.get(id=po.owner_id)
            po.save()


def update_products_progress():
    products = Product.objects.filter(is_active=True)
    for p in products:
        p.save()


def update_status_product_user():
    for pu in ProductUser.objects.filter(is_active=False):
        pu.is_active = True
        pu.save()


def update_order_variation():
    conn = boto3.resource('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                          aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)
    variations = Variation.objects.filter(order=0, scene__isnull=False)
    total = variations.count()
    import datetime
    start_at = datetime.datetime.now()
    current_percent = 0
    for i, variation in enumerate(variations):
        current_percent = calculate_processing(i, total, start_at, current_percent)
        try:
            video = conn.Object(settings.AWS_STORAGE_BUCKET_NAME, str(variation.scene.movie))
            variation.order = video.content_length
            variation.save()
        except:
            print(variation.pk)


def update_folder(list_folder_ids, type, message_id):
    mapping_comment_class = {
        'messenger': (OfferMessage, MessageFolder),
        'messenger_owner': (ProductMessage, ProductMessageFolder),
        'scene': (SceneComment, SceneCommentFolder),
        'offer_product': (OfferProduct, ProductMessageFolder),
        'default': (ProductComment, ProductCommentFolder),
    }
    message_model, folder_model = mapping_comment_class.get(type, mapping_comment_class["default"])
    list_folder_ids = list_folder_ids.split(",")

    list_folder_ids = list(
        filter(lambda folder_id: delete_folder_empty(folder_id, type), list_folder_ids))

    list_folder = folder_model.objects.filter(pk__in=list_folder_ids)
    message = message_model.objects.get(pk=message_id)
    if type == 'offer_product':
        first_message = message.message_product.first()
        list_folder.update(message=first_message, offer=message)
    else:
        list_folder.update(message=message)


def delete_folder_empty(folder_id, type):
    try:
        mapping_type_to_class = {
            'messenger': MessageFolder,
            'messenger_owner': ProductMessageFolder,
            'scene': SceneCommentFolder,
            'contact': ContactFolder,
            'offer_product': ProductMessageFolder,
            'default': ProductCommentFolder,
        }
        model = mapping_type_to_class.get(type, mapping_type_to_class['default'])
        folder = model.objects.get(pk=folder_id)
        has_children = folder.child_folders.exists() or folder.children.exists()
        if not has_children:
            folder.delete()
        return has_children
    except:
        return False


def create_message_file(key_file, message_type, folder_id, file_name, offer_id, result_info_file):
    mapping_file_class = {
        'message': MessageFile,
        'message_owner': ProductMessageFile,
        'scene_comment': SceneCommentFile,
        'contact': ContactFile,
        'offer': ProductMessageFile,
        'default': ProductCommentFile,
    }
    if message_type in ['message', 'message_owner']:
        real_offer = OfferProject.objects.filter(pk=offer_id).first()
        if real_offer:
            file_model = MessageFile if real_offer.offer_creator else ProductMessageFile
    else:
        file_model = mapping_file_class.get(message_type, mapping_file_class["default"])

    if message_type == 'contact':
        created_file = file_model.objects.create(file=key_file,
                                                 real_name=file_name,
                                                 folder_id=folder_id)
    else:
        type_file = None
        file_info = None
        if result_info_file:
            if 'type_file' in result_info_file:
                type_file = result_info_file['type_file']
            if 'file_info' in result_info_file:
                file_info = result_info_file['file_info']
        created_file = file_model.objects.create(file=key_file,
                                                 real_name=file_name,
                                                 folder_id=folder_id,
                                                 type_file_name=type_file,
                                                 file_info=file_info
                                                 )

    if file_model.__name__ == 'ProductCommentFile':
        if created_file.is_audio_file() in ['audio', 'video']:
            sendFileToACRFileScanning.delay(str(created_file.pk), created_file.__class__.__name__)
    return created_file


def create_message_folder(parent_id, name, message_type, offer_id):
    mapping_folder_class = {
        'message': MessageFolder,
        'message_owner': ProductMessageFolder,
        'scene_comment': SceneCommentFolder,
        'contact': ContactFolder,
        'offer': ProductMessageFolder,
        'default': ProductCommentFolder,
    }
    if message_type in ['message', 'message_owner']:
        real_offer = OfferProject.objects.filter(pk=offer_id).first()
        if real_offer:
            file_model = MessageFolder if real_offer.offer_creator else ProductMessageFolder
    else:
        file_model = mapping_folder_class.get(message_type, mapping_folder_class["default"])

    folder = file_model.objects.create(name=name, parent_id=parent_id)
    return folder


def calculate_processing(current, total, start_at, current_percent):
    new_percent = current/total*100
    if not int(new_percent) == current_percent:
        import datetime
        diff = (datetime.datetime.now() - start_at)/new_percent
        min_left = str(int(diff.total_seconds()/60/new_percent))
        print(str(int(new_percent)) + '%, estimate time left: ' + min_left + ' min(s)')
    return int(new_percent)


def update_product_scene_order():
    products = Product.objects.filter(is_active=True)
    current_percent = 0
    import datetime
    start_at = datetime.datetime.now()
    total = products.count()

    for i, product in enumerate(products):
        current_percent = calculate_processing(i, total, start_at, current_percent)
        for index, ps in enumerate(product.scene_list.all().order_by('order')):
            try:
                ps.order = index
                ps.save()
            except:
                print('PRD', product.pk)
                print('PS', ps.pk)


def update_status_offer_product():
    offers_product = OfferProduct.objects.filter(status='4')
    for offer in offers_product:
        offer.status = '6'
        offer.save()


def update_order_product_user():
    users = AuthUser.objects.all()
    for user in users:
        product_ids = list(user.products.all().order_by('created').values_list('pk', flat=True))
        product_users = user.productuser_set.all()
        for pu in product_users:
            product_id = pu.product.pk
            index = product_ids.index(product_id)
            pu.order = index
            pu.save()


def update_owner_product():
    products = Product.objects.all()
    for product in products:
        owner = None
        try:
            offer = OfferProduct.objects.filter(id=product.pk).first()
            owner = offer.master_client
        except:
            owner = product.authuser_set.filter(role='master_client').first()
            print(product.pk)
        if owner:
            product.owner = owner
            product.save()


def change_role():
    users = AuthUser.objects.filter(role='client')
    for user in users:
        user.role = 'master_client'
        user.save()


@app.task
def send_mail_offer_creator(type, offer_id, recipient_id, sender_id, scheme, host, path, message_id=None):
    from app.templatetags.util import get_image
    
    from_email = os.getenv('EMAIL_ADDRESS')
    message = None

    try:
        recipient = AuthUser.objects.get(pk=recipient_id)
        sender = AuthUser.objects.get(pk=sender_id)
        offer = OfferCreator.objects.get(pk=offer_id)
        recipient_avt = get_user_url(recipient, host, 'medium')
        sender_avt = get_user_url(sender, host, 'medium')

        text = "お世話になっております。"
        text1 = ''
        text2 = ''
        subject = ''
        if type == 'new_offer':
            subject = f'【SOREMO】オファーのお知らせ -{sender.get_display_name()} より'
            text1 = f'{sender.get_display_name()} からあなた宛に下記プロジェクトのオファーが届いています。ぜひご検討ください。'
            text2 = "下記より、オファーの詳細を確認し、ご意向をお知らせください。お早めのご確認をお願いいたします。"
        elif type == 'new_message_now':
            subject = f'【SOREMO】{sender.get_display_name()} よりメッセージが届いています'
            message = OfferMessage.objects.get(pk=message_id)
            if sender in offer.offer.get_admin_in_offer_creator():
                text2 = "確認して、対応を進めましょう。"
            text1 = "下記プロジェクト、新しいメッセージが届いています。"
        elif type == "file_checked":
            subject = "【SOREMO】検収が完了しました"
            text1 = "下記プロジェクトのオファーについて、検収が完了しました。"
            text2 = ""
        elif type == "offer_accepted":
            subject = f'【SOREMO】{offer.creator.get_display_name()} へのオファーが成立しました '
            text1 = f'下記プロジェクト、{offer.creator.get_display_name()} へのオファーが成立しました。'
            text2 = ""
        elif type == "file_uploaded":
            subject = f'【SOREMO】{offer.creator.get_display_name()} より納品がありました'
            text1 = f'下記プロジェクト、{offer.creator.get_display_name()} より納品がありました。'
            text2 = "確認して、対応を進めましょう。"

        context = {
            'offer': offer,
            'recipient': recipient,
            'sender': sender,
            'sender_avt': sender_avt,
            'recipient_avt': recipient_avt,
            'product_image': get_image(offer.project, host),
            'host': host,
            'scheme': scheme,
            'url': path,
            'text': text,
            'text1': text1,
            'text2': text2,
            'type': type,
            'message': message
        }

        html_message = loader.render_to_string('emails/email-template-offer-creator.html', context).strip()

        body = strip_tags(html_message)
        send_mail(
            subject=subject,
            message=body,
            from_email=from_email,
            recipient_list=[recipient.email],
            fail_silently=False,
            html_message=html_message
        )
    except ObjectDoesNotExist:
        print(recipient_id)


ROLE_DEFAULT_AVATAR = {
    AuthUser.CREATOR: '/static/images/default-avatar-creator.png',
    AuthUser.MASTERADMIN: '/static/images/default-avatar-master-admin.png',
    AuthUser.MASTERCLIENT: '/static/images/default-avatar-client.png',
}


def get_user_url(user, host, avatar_type=None):
    try:
        if avatar_type and getattr(user, f'{avatar_type}_avatar'):
            return getattr(user, f'{avatar_type}_avatar').url

        if user.avatar:
            return user.avatar.url
        return f'{host}{ROLE_DEFAULT_AVATAR.get(user.role, DEFAULT_AVATAR)}'
    except:
        return f'{host}{DEFAULT_AVATAR}'


@app.task
def update_image_resized(product_id):
    product = Product.objects.filter(product_id=product_id).first()
    if not product:
        return

    if product.image:
        make_new_version_of_img('resized', 'image_resized', 'image', (1920, 384), product)
        product.save()


def add_member_into_offer_creator(offer_creator):
    offer, created = OfferProject.objects.get_or_create(offer_creator=offer_creator,
                                                        project=offer_creator.project,
                                                        type_offer=OfferProject.OFFER_CREATOR)
    if offer_creator.admin.role == AuthUser.MASTERADMIN:
        master_admins = offer_creator.project.get_owner_system_project().filter(role=AuthUser.MASTERADMIN)
        for master_admin in master_admins:
            OfferUser.objects.get_or_create(offer=offer, user=master_admin, position=OfferUser.ADMIN)
    OfferUser.objects.get_or_create(offer=offer, user=offer_creator.admin, position=OfferUser.ADMIN)
    OfferUser.objects.get_or_create(offer=offer, user=offer_creator.creator, position=OfferUser.CREATOR)


def add_remove_member_into_offer_product(offer_product, has_master_admin=False, producer=None):
    offer, created = OfferProject.objects.get_or_create(offer_product=offer_product,
                                                        project=offer_product.project,
                                                        type_offer=OfferProject.OFFER_PRODUCT)

    if has_master_admin or not offer_product.project.contact_artist:
        master_admins = offer_product.project.get_owner_system_project()
        for master_admin in master_admins:
            OfferUser.objects.get_or_create(offer=offer, user=master_admin, position=OfferUser.MASTER_ADMIN)

    elif producer:
        OfferUser.objects.get_or_create(offer=offer, user=producer, position=OfferUser.MASTER_ADMIN)

    pus = offer_product.project.productuser_set.filter(is_invited=False, position=ProductUser.OWNER)
    owners = AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))
    members = offer_product.project.get_client_system_project()
    OfferUser.objects.filter(offer=offer, user_id__in=members.values_list('pk')).delete()
    for owner in owners:
        offer_user = OfferUser.objects.filter(offer=offer, user=owner)
        if offer_user.count() > 1:
            offer_user.delete()
        offer_user, created = OfferUser.objects.get_or_create(offer=offer, user=owner)
        offer_user.position = OfferUser.OWNER
        offer_user.save()


@app.task
def sendFileToACRFileScanning(pk, class_name):
    try:
        if class_name == 'Scene':
            target = Scene.objects.get(pk=pk)
            file_url = get_object_link(target.movie.name)
        elif class_name == 'SceneCommentFile':
            target = SceneCommentFile.objects.get(pk=pk)
            file_url = get_object_link(target.file.name)
        else:
            target = ProductCommentFile.objects.get(pk=pk)
            file_url = get_object_link(target.file.name)
        if not target.acr_status == '1':
            return True
        import requests, json
        ACCESS_TOKEN = settings.ACR_ACCESS_TOKEN
        FS_KEY = settings.ACR_FS_KEY
        url = "https://api-v2.acrcloud.com/api/fs-containers/"+ FS_KEY + "/files"

        payload={'data_type': 'audio_url', 'url': file_url}
        headers = {
        'Accept': 'application/json',
        'Authorization': 'Bearer ' + ACCESS_TOKEN
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        result = json.loads(response.text)
        file_id = result['data']['id']
        target.__class__.objects.filter(pk=pk).update(acr_filescanning_id=file_id, acr_status='2', acr_result_check_count=0)
        return True
    except:
        import sys
        print(sys.exc_info()[1])
        pass
    return False


@app.task
def checkACRForProduct(project_pk):
    try:
        project = Product.objects.get(pk=project_pk)
        if not project.acr_status == '2':
            return True
        scenes = Scene.objects.filter(product=project, movie__isnull=False, acr_status='1').exclude(movie='')
        for scene in scenes:
            if scene.is_audio_file() in 'audio,video':
                sendFileToACRFileScanning(str(scene.pk), scene.__class__.__name__)
                time.sleep(1)

        pdcm = ProductCommentFile.objects.filter(message__project=project, file__isnull=False, acr_status='1').exclude(file='')
        for cm in pdcm:
            if cm.is_audio_file() in 'audio,video':
                sendFileToACRFileScanning(str(cm.pk), cm.__class__.__name__)
                time.sleep(1)

        scm = SceneCommentFile.objects.filter(message__scene_title__product_scene__product_scene__product_id=project_pk, file__isnull=False, acr_status='1').exclude(file='') 
        for cm in scm:
            if cm.is_audio_file() in 'audio,video':
                sendFileToACRFileScanning(str(cm.pk), cm.__class__.__name__)
                time.sleep(1)
        project.acr_status == '3'
        project.save()
    except:
        pass
    return False


def get_object_link(object_name):
    try:
        from django.conf import settings
        import boto3

        s3 = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                          aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)

        url = s3.generate_presigned_url(
            ClientMethod='get_object',
            Params={
                'Bucket': settings.AWS_STORAGE_BUCKET_NAME,
                'Key': object_name,
            },
            ExpiresIn=43200
        )
        return url
    except:
        pass
    return ''

@app.task
def reCheckACRForProduct(project_pk):
    try:
        project = Product.objects.get(pk=project_pk)
        scenes = Scene.objects.filter(product=project, movie__isnull=False, acr_status__in=['3', '4']).exclude(movie='')
        scenes.update(acr_status='2')
        pdcm = ProductCommentFile.objects.filter(message__project=project, file__isnull=False, acr_status__in=['3', '4']).exclude(file='')
        pdcm.update(acr_status='2')
        scm = SceneCommentFile.objects.filter(message__scene_title__product_scene__product_scene__product_id=project_pk, file__isnull=False, acr_status__in=['3', '4']).exclude(file='')
        scm.update(acr_status='2')
        print('ok')
    except:
        print('not ok')
        return False
        pass
    return True

@app.task
def send_email_when_user_contact_mgk(user_email, contact_info_id, host):
    template_email = 'email/template_mail_contact_mgk_info.html'
    print('Send email')
    contact_info = ContactInfo.objects.filter(pk=contact_info_id).first()
    if contact_info:
        master_admins = AuthUser.objects.filter(role=AuthUser.MASTERADMIN, is_active=True)
        from_email = os.getenv('EMAIL_ADDRESS')
        context = {}
        for user in master_admins:
            subject = "【SOREMO】お問い合わせが届いています（MGK）"
            text2 = '内容を確認の上、折り返し対応を進めてください。'
            recipient_avt = get_user_url(user, host, 'medium')
            context = {}
            context.update({'recipient': user,
                            'text2': text2,
                            'recipient_avt': recipient_avt,
                            'host': host,
                            'contact_info': contact_info
                            })
            html_message = loader.render_to_string(template_email, context).strip()
            body = strip_tags(html_message)
            send_mail(subject, message=body, from_email=from_email,
                      recipient_list=[user.email], fail_silently=False, html_message=html_message)

        subject = "【SOREMO】お問い合わせを承りました（MGK）"
        text2 = '内容を確認の上、折り返しご連絡いたします。'
        context = {}
        context.update({'recipient': None,
                        'text2': text2,
                        'host': host,
                        'contact_info': contact_info
                        })
        html_message = loader.render_to_string(template_email, context).strip()
        body = strip_tags(html_message)
        send_mail(subject, message=body, from_email=from_email,
                  recipient_list=[user_email], fail_silently=False, html_message=html_message)