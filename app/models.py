# 01EG89H6SS2141VNGDDEHBMV4Q
# # -*- coding: utf-8 -*-
import datetime
import io
import re
import uuid
import json
from base64 import urlsafe_b64encode
import requests

from PIL import Image
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db import models, transaction
from django.db.models import Q, QuerySet, Prefetch, Avg, Max, Sum, Case, When
from model_utils import FieldTracker
from django.utils.translation import gettext as _
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>, GenericForeignKey
from django.db import connection
from django.utils import timezone

from accounts.models import AuthUser, ProductUser, BaseModelCropImg
from app.util import strf_date, strf_today, get_type_file, calculator_budget
from voice.constants import CONST_REGEX_FILE, FORMAT_DATE, FORMAT_TIME

DAY_SHOW_NEW_SCENE = 7


class ModelBase(models.Model):
    modified = models.DateTimeField(auto_now=True, )
    created = models.DateTimeField(auto_now_add=True, )
    tracker = FieldTracker()

    class Meta:
        abstract = True


class ParanoidQuerySet(QuerySet):
    def delete(self):
        for obj in self:
            obj.deleted_on = datetime.datetime.now()
            obj.save()

    def undelete(self):
        for obj in self:
            obj.deleted_on = None
            obj.save()


class ParanoidManager(models.Manager):
    def get_queryset(self):
        return ParanoidQuerySet(self.model, using=self._db).filter(
            deleted_on__isnull=True)


class ParanoidModel(models.Model):
    class Meta:
        abstract = True

    deleted_on = models.DateTimeField(null=True, blank=True)
    objects = ParanoidManager()
    original_objects = models.Manager()

    def delete(self):
        self.deleted_on = datetime.datetime.now()
        self.save()

    def undelete(self):
        self.deleted_on=None
        self.save()


class SceneTitleManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(product_scene__deleted_on__isnull=True, last_version__isnull=False)


class SceneTitle(ModelBase): # Shot (Deliverable)

    TAG_CHOICES = (
        ('1', '新着'),
        ('2', '進行中'), # unused
        ('3', '完了'), # unused
    )

    STATUS_CHOICES = (
        ('1', 'new'), # NEW
        ('2', 'update'), # UPDATED
        ('3', 'process_top'), # PRODUCER_PENDING
        ('4', 'process_bot'), # CLIENT_PENDING
        ('5', 'done_top'), # APPROVED
        ('6', 'done_bot'), # unused
    )

    objects = SceneTitleManager()
    original_objects = models.Manager()

    #ID(pk)
    title_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False) 

    # relations
    product_scene = models.ForeignKey('ProductScene', related_name='title_product_scene', blank=True, null=True,
                                      on_delete=models.CASCADE) # tags
    last_version = models.ForeignKey('Scene', related_name='title_scene', null=True, blank=True, default=None,
                                     on_delete=models.SET_NULL) # latest_media_item

    # core fields
    title = models.CharField(max_length=300, default='') # shot_name
    rating = models.FloatField(default=0) # average_rating

    # status fields
    status = models.CharField(choices=STATUS_CHOICES, max_length=100, default='4')  # Should use integer, not string
    updated_at = models.DateTimeField(null=True, blank=True, default=None)
    can_share = models.BooleanField(default=False) # is_shared
    
    # reserved
    production_file = models.FileField(upload_to='production_file', default=None) # ?

    # legacy
    is_done = models.BooleanField(default=False) # is_approved
    tag = models.BooleanField(default=False) # ?
    admin_updated_at = models.DateTimeField(null=True, blank=True, default=None) # ?


    def __str__(self):
        return str(self.title_id)

    def modified_str(self):
        if self.updated_at:
            return self.updated_at.strftime('%y/%m/%d %a %H:%M')
        return self.created.strftime('%y/%m/%d %a %H:%M')
    
    @property
    def latest_media_item(self):
        """最終更新日順で取得し、同日の場合はvariation_orderが最も高いものを取得"""
        return self.scene_title.filter(
            product_scene__deleted_on__isnull=True
        ).annotate(
            update_date=models.functions.TruncDate('take_uploaded')
        ).order_by(
            '-update_date',
            '-version_order'
        ).first()

    class Meta:
        ordering = ["-created"]

    def update_rating(self):
        rating_scene = RatingSceneTitle.objects.filter(title=self)
        if rating_scene.count():
            count_rating = 0
            rating_sum = 0
            for r in rating_scene:
                rating_sum += r.rating
                count_rating += 1
            rating = rating_sum / count_rating
            rating = round(rating, 2)
            if self.rating != rating:
                self.rating = rating
                self.save()
                self.product_scene.update_rating()
                self.product_scene.product_scene.first().update_product_rating()
        else:
            self.rating = 0
            self.save()
        return self.rating

    def delete(self, *args, **kwargs):
        variations = Variation.original_objects.filter(scene_title=self)
        if variations.exists():
            for variation in variations:
                variation.delete()
        else:
            scenes = Scene.original_objects.filter(title=self, product_scene=self.product_scene)
            if scenes.exists():
                product_scene = self.product_scene
                for scene in scenes:
                    scene.other_versions.all().delete()
                    Scene.original_objects.filter(pk=scene.pk, product_scene=product_scene).delete()
        super(SceneTitle, self).delete(*args, **kwargs)
        if self.product_scene_id:
            for product in self.product_scene.product_scene.all():
                product.update_current_and_heart_scene()

    def save(self, *args, **kwargs):
        super(SceneTitle, self).save(*args, **kwargs)
        if self.product_scene_id:
            for product in self.product_scene.product_scene.all():
                product.update_current_and_heart_scene()

    def get_file_name(self):
        name = self.production_file.name
        file_extension = re.search(CONST_REGEX_FILE, name)
        if file_extension:
            file_extension = file_extension.group()
        name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z-]{3,5}$)", '', name)
        name += file_extension
        return name

    def is_audio_file(self):
        if self.production_file:
            name = self.production_file.name
            return get_type_file(name)
        return 'other'

    def get_scene_list(self):
        return Scene.objects.filter(title_id=self.title_id)

    def get_scene_ones(self):
        return Scene.objects.filter(title_id=self.title_id).first()

    def move_product_scene(self, product_scene_id):
        product_scene = ProductScene.objects.get(product_scene_id=product_scene_id)
        for scene in self.get_scene_list():
            scene.product_scene = product_scene
            scene.save()

    def get_scene_ones_params(self, tag='', product_id='', product_scene_id=''):
        queryset = Scene.objects.reverse().filter(title_id=self.title_id)
        if tag:
            queryset = queryset.filter(title__tag=tag)
        if product_id:
            queryset = queryset.filter(product_id=product_id)
        if product_scene_id and product_scene_id != 'all':
            queryset = queryset.filter(product_scene_id=product_scene_id)
        if queryset.count() <= 0:
            return None
        return queryset[0]

    def update_last_version(self):
        id = self.pk
        scene_titles = SceneTitle.original_objects.filter(pk=id)
        scene_titles = scene_titles.prefetch_related(
            Prefetch('scene_title', queryset=Scene.objects \
                     .filter(version=None) \
                     .order_by('-order') \
                     .prefetch_related(Prefetch('other_versions',
                                                queryset=Scene.objects.order_by('version_order')))))

        last_scene = None
        st = scene_titles.first()
        if st.scene_title.exists():
            # if st.scene_title.all().first().other_versions.exists():
            #     last_scene = st.scene_title.all().first().other_versions.all().first()
            # if not last_scene:
            last_scene = st.scene_title.all().first()
        st.last_version = last_scene
        st.save()
        scene_taken = SceneTaken.objects.filter(title_id=st.pk).exists()
        if not scene_taken:
            SceneTaken.objects.create(title_id=st.pk)

        # add taken_scenes
        # if last_scene:
        #     try:
        #         title_taken_scene = st.title_taken_scene
        #         old_scene_taken_scenes = SceneTakenScenes.objects.filter(
        #             taken=title_taken_scene
        #         ).values_list('scene_id', flat=True)
        #         old_scene_taken_scenes = list(old_scene_taken_scenes)
        #         SceneTakenScenes.objects.filter(taken=title_taken_scene).delete()
        #         list_bulk_create = []
        #         for scene_id in old_scene_taken_scenes:
        #             if scene_id.hex != last_scene.pk.hex:
        #                 list_bulk_create.append(SceneTakenScenes(taken=title_taken_scene, scene_id=scene_id.hex))
        #         list_bulk_create.append(SceneTakenScenes(taken=title_taken_scene, scene=last_scene))
        #         SceneTakenScenes.objects.bulk_create(list_bulk_create)
        #
        #     except Exception as e:
        #         pass

    def get_last_message_scene_title(self, user_role):
        title = self
        all_scenes = Scene.objects.filter(title_id=title.pk)
        last_comment = SceneComment.objects.filter(~Q(user__role=user_role) &
                                                   (Q(scene__in=all_scenes) | Q(
                                                       scene_title=title))).last()
        return last_comment

    def get_last_version(self):
        last_version = self.last_version
        try:
            title_taken_scene = self.title_taken_scene
            for taken_scene in title_taken_scene.taken_scenes.all():
                last_version = taken_scene
        except Exception:
            pass
        return last_version

    def update_taken_scenes(self, last_scene):
        scene_titles = SceneTitle.original_objects.filter(pk=self.pk)
        scene_titles = scene_titles.prefetch_related(
            Prefetch('scene_title', queryset=Scene.objects \
                     .filter(version=None) \
                     .order_by('-order') \
                     .prefetch_related(Prefetch('other_versions',
                                                queryset=Scene.objects.order_by('version_order')))))
        st = scene_titles.first()
        scene_taken = SceneTaken.objects.filter(title_id=st.pk).exists()
        if not scene_taken:
            SceneTaken.objects.create(title_id=st.pk)
        try:
            title_taken_scene = st.title_taken_scene
            old_scene_taken_scenes = SceneTakenScenes.objects.filter(
                taken=title_taken_scene
            ).values_list('scene_id', flat=True)
            old_scene_taken_scenes = list(old_scene_taken_scenes)
            SceneTakenScenes.objects.filter(taken=title_taken_scene).delete()
            list_bulk_create = []
            for scene_id in old_scene_taken_scenes:
                if scene_id.hex != last_scene.pk.hex:
                    list_bulk_create.append(SceneTakenScenes(taken=title_taken_scene, scene_id=scene_id.hex))
            list_bulk_create.append(SceneTakenScenes(taken=title_taken_scene, scene=last_scene))
            SceneTakenScenes.objects.bulk_create(list_bulk_create)
        except Exception as e:
            pass

    def remove_taken_scene(self, scene):
        scene_titles = SceneTitle.original_objects.filter(pk=self.pk)
        scene_titles = scene_titles.prefetch_related(
            Prefetch('scene_title', queryset=Scene.objects \
                     .filter(version=None) \
                     .order_by('-order') \
                     .prefetch_related(Prefetch('other_versions',
                                                queryset=Scene.objects.order_by('version_order')))))
        st = scene_titles.first()
        try:
            title_taken_scene = st.title_taken_scene
            SceneTakenScenes.objects.filter(taken=title_taken_scene, scene=scene).delete()
        except Exception:
            pass


class SceneTitleBookmark(models.Model):
    TAG_CHOICES = (
        ('scene', 'scene'),
        ('sale', 'sale'),
    )
    TYPE_BOOKMARK = ['scene', 'sale']
    BOOKMARK_SCENE = 'scene'
    BOOKMARK_SALE = 'sale'
    type_bookmark = models.CharField(choices=TAG_CHOICES, max_length=50, default='scene')
    user = models.ForeignKey('accounts.AuthUser', related_name="bookmarks", on_delete=models.CASCADE)
    title = models.ForeignKey('SceneTitle', related_name="bookmarks", on_delete=models.CASCADE, blank=True, null=True)
    sale = models.ForeignKey('SaleContent', related_name="bookmarks", on_delete=models.CASCADE, blank=True, null=True)


class ListBookMark(models.Model):
    user = models.ForeignKey('accounts.AuthUser', related_name="list_bookmarks", on_delete=models.CASCADE)
    title = models.CharField(max_length=64)
    order = models.IntegerField(default=1)
    created = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["order"]


class BookmarkListBookMarks(models.Model):
    scenetitlebookmark = models.ForeignKey(SceneTitleBookmark, related_name="list_ids", on_delete=models.CASCADE)
    listbookmark = models.ForeignKey(ListBookMark, related_name="item_ids", on_delete=models.CASCADE)
    order = models.IntegerField(default=1)

    class Meta:
        ordering = ["-order"]


class VariationManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(scene_title__product_scene__deleted_on__isnull=True)


class Variation(ModelBase): # Take
    STATUS_CHOICES = (
        ('1', 'new'),
        ('2', 'update'),
        ('3', 'process_top'),
        ('4', 'process_bot'),
        ('5', 'done_top'),
        ('6', 'done_bot'),
    )

    objects = VariationManager()
    original_objects = models.Manager()

    # id(pk)
    variation_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # relations
    scene_title = models.ForeignKey("SceneTitle", related_name="take", blank=True, null=True,
                                    on_delete=models.CASCADE)

    name = models.CharField(max_length=500) # ?
    status = models.CharField(choices=STATUS_CHOICES, max_length=100, default='4') # ?
    order = models.IntegerField(default=0) # ?

    def __str__(self):
        return self.name

    def delete(self, *args, **kwargs):
        with transaction.atomic():
            try:
                product_scene = self.scene_title.product_scene
                scene = self.scene
                if scene:
                    scene.other_versions.filter(product_scene=product_scene).delete()
                    Scene.original_objects.filter(pk=scene.pk, product_scene=product_scene).delete()
            except:
                pass
            super(Variation, self).delete(*args, **kwargs)


class SceneManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(
            title__product_scene__deleted_on__isnull=True)


class Scene(ModelBase): #MediaItem
    TAG_CHOICES = (
        ('1', '新着'),
        ('2', '完了'),
    )

    ACR_STATUS_CHOICES = (
        ('1', 'not_start'),
        ('2', 'uploaded'),
        ('3', 'has_result'),
        ('4', 'not_match'),
        ('5', 'error'),
    )

    objects = SceneManager()
    original_objects = models.Manager()

    # id(pk)
    scene_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False) # media_item_id

    # relations
    product = models.ForeignKey('Product', related_name='scene_product', blank=True,
                                null=True, on_delete=models.CASCADE) # project
    title = models.ForeignKey('SceneTitle', related_name='scene_title', blank=True,
                              null=True, on_delete=models.CASCADE) # shot
    variation = models.OneToOneField('Variation', related_name='scene', blank=True, null=True, on_delete=models.DO_NOTHING) # ?
    version = models.ForeignKey('self', related_name='other_versions', null=True, blank=True, on_delete=models.DO_NOTHING) # ?

    # core fields
    real_name = models.CharField(max_length=512, null=True, blank=True) # variation_name
    thumbnail = models.FileField(upload_to='thumbnail', null=True, blank=True)
    movie = models.FileField(upload_to='movie', null=True, blank=True) # media_file
    version_order = models.IntegerField(default=1) # variation_order
    schedule_date = models.DateTimeField(null=True, blank=True) # next_delivery_date - TODO: This should be stored in SceneTitle (Shot) model instead
    take_uploaded = models.DateTimeField(auto_now_add=True, blank=True, null=True) # updated_at

    order = models.IntegerField(default=1) # take_order
    tag = models.CharField(choices=TAG_CHOICES, max_length=100, default='1') # ?
    flag_tag = models.BooleanField(default=False) # ?
    owner_id = models.IntegerField(default=0) # ?

    ## video fields
    fps_movie = models.IntegerField(null=True) # frame_rate
    video_width = models.IntegerField(null=True)
    video_height = models.IntegerField(null=True)

    ## audio fields
    peaks = models.TextField(null=True, blank=True, default="") # waveform_data
    audio_pin = models.CharField(null=True, blank=True, max_length=50) # ?
    small_peaks = models.TextField(null=True, blank=True, default="") # ?

    ## acr fields
    acr_status = models.CharField(choices=ACR_STATUS_CHOICES, max_length=100, default='1')
    acr_result = models.TextField(null=True, blank=True, default="チェッキング")
    acr_filescanning = models.TextField(null=True, blank=True, default="")
    acr_filescanning_id = models.CharField(null=True, blank=True, max_length=50, default='')
    acr_result_check_count = models.IntegerField(null=True, default=0)

    # reserved
    ok_by = models.ForeignKey('accounts.AuthUser', related_name='ok_scene', blank=True, null=True,
                              on_delete=models.CASCADE) # approved_by
    ok = models.BooleanField(default=False) # is_approved

    # legacy
    product_scene = models.ForeignKey('ProductScene', related_name='scene_product_scene', blank=True, null=True,
                                      on_delete=models.CASCADE) # tags

    class Meta:
        ordering = ["order", "created"]

    def created_str(self):
        return self.created.strftime('%y/%m/%d %a %H:%M')

    def __str__(self):
        return str(self.scene_id)

    def is_audio_file(self):
        if self.movie:
            name = self.movie.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            file_extension = file_extension.lower()
            if file_extension in '.mp3,.wav':
                return 'audio'
            elif file_extension in '.pdf':
                return 'document'
            elif file_extension in '.png,.jpg,.jpeg, .bmp, .gif, .svg':
                return 'image'
        return 'video'

    def save(self, *args, **kwargs):
        super(Scene, self).save(*args, **kwargs)
        # 最終更新日
        title = self.title
        if title is not None:
            title.modified = datetime.datetime.now()
            title.save()
        if self.product:
            self.product.save()

    def delete(self, *args, **kwargs):
        with transaction.atomic():
            if self.version is None:
                children = self.other_versions.order_by('created')
                num_child = children.count()
                first_child = children.first()
                try:
                    variation = self.variation
                except:
                    variation = None
                self.variation = None
                self.save()
                if num_child > 1:
                    children.exclude(pk=first_child.pk).update(version=first_child)
                    first_child.version = None
                    first_child.variation = variation
                    first_child.save()
                elif num_child == 1:
                    first_child.version = None
                    first_child.variation = variation
                    first_child.save()
                elif num_child == 0 and variation:
                    variation.delete()
            # self.movie.delete(save=False)
            self.title.modified = Scene.objects.filter(title_id=self.title_id).order_by('-modified').first().modified
            self.title.save()
            super(Scene, self).delete(*args, **kwargs)

    def get_comments(self):
        return SceneComment.objects.filter(scene_id=self.scene_id)

    def get_comment_form(self, user_id=1):
        from .forms import SceneCommentForm
        return SceneCommentForm(initial={
            'owner_id': user_id,
            'scene': self.scene_id,
        })

    def carousel_scene_list_index(self):
        arr_anpha = list("bcdefghijklmnopqrstuvwxyz")
        scenes = Scene.objects.filter(version=self.scene_id).order_by('created')
        data = []
        if scenes:
            if self.movie.name == '':
                arr_anpha.insert(0, 'a')
            for index, scene in enumerate(scenes):
                json = {
                    'pk': scene.pk,
                    'movie': scene.movie.url,
                    'flag_tag': scene.flag_tag,
                    'index': arr_anpha[index],
                    'fps_movie': scene.fps_movie
                }
                data.append(json)
        return data

    def check_tag(self):
        root_scene = self
        if self.version:
            root_scene = Scene.objects.filter(pk=self.version_id).first()

        tag = root_scene.tag
        tag_child = Scene.objects.filter(Q(version=root_scene.scene_id), ~Q(tag=1))
        if tag_child:
            tag = tag_child[0].tag
        return tag

    def have_child(self):
        return Scene.objects.filter(Q(version=self.scene_id) & (~Q(product_scene_id=None) | ~Q(title_id=None))).exists()

    def get_file_name(self):
        if self.variation:
            return self.variation.name

        if self.real_name:
            return self.real_name

        if self.movie:
            name = self.movie.name
            name = re.sub(r"movie\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            return name
        return 'Unknown'

    def get_real_movie_name(self):
        if self.movie:
            name = self.movie.name
            name = re.sub(r"movie\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            return name
        return "Unknown"

    def get_file_movie_name(self):
        if self.movie:
            name = self.movie.name
            name = re.sub(r"movie\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name) + re.search(".[0-9a-zA-Z]{3,4}$", name).group().lower()
            return name
        return ''

    def get_latest_version(self):
        if self.version is None:
            scenes = self.other_versions.order_by('-version_order')
            return self if not scenes.exists() else scenes.first()
        else:
            scenes = self.version.other_versions.order_by('-version_order')
            return self if not scenes.exists() else scenes.first()

    def get_first_variation_take(self):
        return self if self.version is None else self.version

    def get_older_version(self):
        if self.version:
            return self.version.other_versions.exclude(pk=self.pk).order_by('-created')
        else:
            return self.other_versions.order_by('-created')

    def get_slug(self):
        num_link = SceneShareLink.objects.filter(scene=self).count() + 1
        return str(num_link) + "-" + urlsafe_b64encode(self.scene_id.bytes).rstrip(b'=').decode('ascii')

    def get_variation(self):
        if self.version:
            return self.version.variation
        else:
            return self.variation

    def get_last_expired_time(self):
        share_link = SceneShareLink.objects.filter(scene=self).order_by('-created', '-id').first()

        if not share_link:
            return strf_today()

        return strf_date(share_link.expired_time)

    def get_step_expired_time(self):
        scenes = Scene.objects.filter(title_id=self.title.pk)
        share_link = SceneShareLink.objects.filter(scene__in=scenes).order_by('-created', '-id').first()
        today = datetime.date.today()

        if (
            share_link
            and share_link.expired_time
            and share_link.expired_time > today
        ):
            return (share_link.expired_time - today).days

        return 0

    def update_can_share_link(self):
        scene_title = self.title
        if not scene_title.can_share:
            return

        scenes = Scene.objects.filter(title_id=scene_title.pk)
        link = SceneShareLink.objects.filter(scene__in=scenes).order_by('-created', '-id').first()
        if (
            not link or (
                link and link.expired_time < datetime.date.today()
            )
        ):
            scene_title.can_share = False
            scene_title.save()


class SceneShareLink(ModelBase):
    name = models.CharField(max_length=256, default='', null=True, blank=True)
    scene = models.ForeignKey("Scene", related_name="share_links", on_delete=models.CASCADE, blank=True, null=True)
    expired_time = models.DateField(null=True, blank=True, default=datetime.date.today)
    show_comment = models.BooleanField(default=False)
    user_share = models.ForeignKey("accounts.AuthUser", on_delete=models.CASCADE, null=True)


class SceneTaken(ModelBase):
    title = models.OneToOneField(
        SceneTitle, on_delete=models.SET_NULL, related_name="title_taken_scene", null=True, db_constraint=False
    )
    taken_scenes = models.ManyToManyField(Scene, through='SceneTakenScenes', blank=True)
    user = models.ForeignKey("accounts.AuthUser", on_delete=models.SET_NULL, related_name="taken_scenes", null=True)


class SceneTakenScenes(ModelBase):
    taken = models.ForeignKey(SceneTaken, on_delete=models.CASCADE)
    scene = models.ForeignKey(Scene, on_delete=models.CASCADE, db_constraint=False)


class DownloadedScene(ModelBase):
    download_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file = models.ForeignKey('Scene', related_name='user_downloaded', blank=True,
                             null=True, on_delete=models.CASCADE)
    user = models.ForeignKey('accounts.AuthUser', related_name='scene_download', blank=True,
                             null=True, on_delete=models.CASCADE)


class SceneComment(ModelBase):
    comment_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    comment = models.TextField(verbose_name='コメント', max_length=255)
    owner_id = models.IntegerField(default=0)
    user = models.ForeignKey('accounts.AuthUser', related_name='owner_scene_comment', blank=True,
                             null=True, on_delete=models.CASCADE)
    scene = models.ForeignKey('Scene', related_name='scene_comment_scene', blank=True,
                              null=True, on_delete=models.CASCADE)
    scene_title = models.ForeignKey('SceneTitle', related_name='comments', blank=True, null=True, on_delete=models.CASCADE)
    pin_time = models.CharField(max_length=20, default='', null=True, blank=True)
    file = models.FileField(upload_to='file', blank=True, null=True)
    stamp = models.BooleanField(default=False, blank=True)
    pin_video = models.CharField(max_length=50, null=True, blank=True)
    real_name = models.CharField(max_length=512, null=True, blank=True)
    parent = models.ForeignKey('SceneComment', related_name='child_comment', blank=True, null=True, on_delete=models.SET_NULL)
    resolved = models.BooleanField(default=False, blank=True)
    pin_endtime = models.CharField(max_length=20, null=True, blank=True)
    acr_result = models.TextField(null=True, blank=True, default="チェッキング")
    peaks = models.CharField(default="", max_length=1024)
    has_file = models.BooleanField(default=False)
    receivers = models.ManyToManyField('accounts.AuthUser', through='SceneCommentReceiver', blank=True)
    updated_at = models.DateTimeField(null=True, blank=True, default=None)
    is_near = models.BooleanField(default=False, blank=True)

    class Meta:
        ordering = ["created"]

    def created_en(self):
        return self.created.strftime('%y/%m/%d %a')

    def __str__(self):
        return str(self.comment_id)

    def get_user(self):
        return AuthUser.objects.get(pk=self.owner_id)

    def created_str(self):
        return self.created.strftime('%y/%m/%d %a %H:%M')

    def get_file_name(self):
        if self.real_name:
            return self.real_name
        else:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            return name

    def check_pin_time(self):
        flag_pin_time = 0
        if self.pin_time:
            flag_pin_time = 1
            if self.file and re.search("[.](mp3|wav|ogg|MP3|WAV|OGG)", self.get_file_name()):
                flag_pin_time = 2
        else:
            if self.file and re.search("[.](mp3|wav|ogg|MP3|WAV|OGG)", self.get_file_name()):
                flag_pin_time = 3
        return flag_pin_time

    def is_audio_file(self):
        if self.file:
            name = self.file.name
            return get_type_file(name)
        return 'other'

    def save(self, *args, **kwargs):
        if self.file and not self.real_name:
            self.real_name = self.file.name
        self.updated_at = datetime.datetime.now()
        super(SceneComment, self).save(*args, **kwargs)
        # 最終更新日
        scene = self.scene
        if scene:
            scene.modified = datetime.datetime.now()
            from accounts.services import update_product_user_new_video
            product = scene.product
            products = list()
            products.append(product)
            users = product.authuser_set.all()
            update_product_user_new_video(products, users)
            scene.save()


class SceneCommentFolder(ModelBase):
    folder_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    parent = models.ForeignKey('SceneCommentFolder', related_name="child_folders", null=True, on_delete=models.CASCADE)
    name = models.CharField(max_length=512, null=False)
    message = models.ForeignKey('SceneComment', related_name="folders", null=True, on_delete=models.CASCADE)


class SceneCommentFile(ModelBase):
    ACR_STATUS_CHOICES = (
        ('1', 'not_start'),
        ('2', 'uploaded'),
        ('3', 'has_result'),
        ('4', 'not_match'),
        ('5', 'error'),
    )

    file_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey('SceneComment', related_name="files", null=True, on_delete=models.SET_NULL)
    file = models.FileField(upload_to='file', blank=True, max_length=1024)
    real_name = models.CharField(max_length=512, blank=True, null=True)
    peaks = models.TextField(null=True, blank=True, default="")
    folder = models.ForeignKey('SceneCommentFolder', related_name="children", null=True, on_delete=models.CASCADE)
    acr_result = models.TextField(null=True, blank=True, default='チェッキング')
    acr_status = models.CharField(choices=ACR_STATUS_CHOICES, max_length=100, default='1')
    acr_filescanning = models.TextField(null=True, blank=True, default="")
    acr_filescanning_id = models.CharField(null=True, blank=True, max_length=50, default='')
    acr_result_check_count = models.IntegerField(null=True, default=0)
    type_file_name = models.CharField(null=True, max_length=20)
    file_info = models.CharField(null=True, max_length=500)

    def is_audio_file(self):
        if self.file:
            name = self.file.name
            return get_type_file(name)
        return 'other'

    def save(self, *args, **kwargs):
        if self.file and not self.real_name:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            self.real_name = name
        super(SceneCommentFile, self).save(*args, **kwargs)


class DownloadedSceneComment(ModelBase):
    download_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file = models.ForeignKey('SceneCommentFile', related_name='user_downloaded', blank=True,
                             null=True, on_delete=models.CASCADE)
    user = models.ForeignKey('accounts.AuthUser', related_name='comment_downloaded', blank=True,
                             null=True, on_delete=models.CASCADE)

    def __str__(self):
        return str(self.download_id)


class SceneCommentReceiver(models.Model):
    user = models.ForeignKey('accounts.AuthUser', on_delete=models.CASCADE)
    message = models.ForeignKey('SceneComment', default=None, on_delete=models.CASCADE, null=True, blank=True,)
    product_comment = models.ForeignKey('ProductComment', default=None, on_delete=models.CASCADE, null=True, blank=True,)
    seen_date = models.DateTimeField(null=True)


class OfferCreatorManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(type='1')


class OfferCreator(ModelBase):
    objects = OfferCreatorManager()
    original_objects = models.Manager()

    STATUS_IN_PROGRESS = ['1', '2', '3']
    STATUS_OFFER_ACTIVE = ['1', '2', '3', '4']
    STATUS_NEW = '1'
    TYPE_NORMAL = '1'
    STATUS_PROGRESS = ['2', '3']
    STATUS_COMPLETED = '4'
    STATUS_REJECT = '5'
    STATUS_CLOSED = '6'

    TAG_CHOICES = (
        ('1', '新着'),
        ('2', 'ファイルがない'),
        ('3', '進行中'),
        ('4', '完了'),
        ('5', '拒否'),
        ('6', 'オファーを閉じる')
    )
    TAG_CONTRACT = (
        ('1', '作編曲'),
        ('2', '編曲'),
        ('3', '作詞'),
        ('4', 'サウンドデザイン'),
        ('5', 'エンジニア業務'),
        ('6', 'ボイスの実演'),
        ('7', '歌唱の実演'),
        ('8', '楽器の演奏'),
        ('9', 'ディレクター')
    )
    TAG_REVIEWS = (
        ('1', 'OK'),
        ('2', 'NORMAL'),
        ('3', 'NG'),
    )

    TYPE_CONTRACT = (
        ('1', 'コンポーザー'),
        ('2', 'サウンドデザイナー'),
        ('3', 'オーディオエンジニア'),
        ('4', '声優・ナレーター'),
        ('5', 'ボーカリスト'),
        ('6', '演奏家')
    )

    TYPE_OFFER = (
        ('1', 'normal'),
        ('2', 'producer')
    )

    project = models.ForeignKey('app.Product', related_name='product_offers', null=True, on_delete=models.CASCADE) # 関連プロジェクト
    admin = models.ForeignKey('accounts.AuthUser', related_name='offer_by', null=False, on_delete=models.CASCADE) #委託者
    creator = models.ForeignKey('accounts.AuthUser', related_name='offer_to_creator', null=False, on_delete=models.CASCADE) #受託者
    scenes = models.CharField(max_length=500, null=False, default='')
    status = models.CharField(choices=TAG_CHOICES, max_length=100, default='1')
    file = models.FileField(upload_to='file', blank=True, null=True)
    contract = models.CharField(choices=TAG_CONTRACT, max_length=100, default='1')
    message = models.TextField(max_length=500, default='')
    reward = models.FloatField(default=0)
    payment_status = models.BooleanField(default=False)
    start_time = models.DateTimeField(null=True, blank=True)
    deadline = models.DateTimeField(null=True, blank=True)
    review = models.CharField(choices=TAG_REVIEWS, max_length=100, null=True, blank=True)
    review_admin = models.CharField(choices=TAG_REVIEWS, max_length=100, null=True, blank=True)
    accept_time = models.DateTimeField(null=True, blank=True, default=None)
    check_time = models.DateTimeField(null=True, blank=True, default=None)
    reject_time = models.DateTimeField(null=True, blank=True, default=None)
    quantity = models.TextField(max_length=500, null=True, blank=True, verbose_name='数量')
    data_format = models.TextField(max_length=500, null=True, blank=True, verbose_name='データ形式')
    type_contract = models.CharField(max_length=100, default='コンポーザー')
    admin_payment_request = models.ForeignKey('payments.PaymentRequest', related_name='owner_offers', null=True, on_delete=models.SET_NULL)
    creator_payment_request = models.ForeignKey('payments.PaymentRequest', related_name='recipient_offers', null=True, on_delete=models.SET_NULL)
    legaxy_payment = models.BooleanField(default=False)
    type = models.CharField(choices=TYPE_OFFER, max_length=20, default='1')
    contract_file = models.FileField(upload_to='file', blank=True, null=True)
    contract_file_raw = models.FileField(upload_to='file', blank=True, null=True)
    contract_time = models.DateTimeField(null=True, blank=True, default=None)
    valid_date = models.DateTimeField(null=True, blank=True)
    note = models.CharField(null=True, blank=True, default=None, max_length=1000)
    release_time = models.DateTimeField(null=True, blank=True)
    pick_up_method = models.CharField(null=True, blank=True, max_length=500)
    delivery_place = models.CharField(null=True, blank=True, max_length=500)
    note_type = models.CharField(null=True, max_length=32)
    allow_subcontracting = models.BooleanField(default=True)
    range_deadline = models.CharField(null=True ,max_length=32)
    selected_job_type = models.CharField(null=True, blank=True, max_length=500)

    class Meta:
        ordering = ["created"]

    def custom_contract_dsp(self):
        mapping = {
            '1': _('作編曲'),
            '2': _('編曲'),
            '3': _('作詞'),
            '4': _('SFX　効果音'),
            '5': _('レコーディング　ミキシング'),
            '6': _('キャラクターボイス　ナレーション'),
            '7': _('歌唱'),
            '8': _('演奏'),
            '9': _('進行管理'),
        }
        if self.type == '2':
            return '' if self.contract == '1' else self.contract
        else:
            return self.contract

    def last_message(self):
        last_message = self.message
        if self.message_offer.exists():
            last_message = self.message_offer.last()
        return last_message

    def get_deadline_date(self):
        if self.deadline:
            return self.deadline.strftime('%y年%m月%d日')
        return ''

    def get_deadline_time(self):
        if self.deadline:
            return self.deadline.strftime('%H:%M')
        return ''

    def get_remaining_day(self):
        if self.deadline:
            difference = self.deadline - datetime.datetime.now()
            return difference.days
        return ''

    def get_file_name(self):
        first_message = self.message_offer.first()
        if self.file:
            return first_message.files.first().real_name
        else:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
        return name

    def get_file_messages(self):
        first_message = self.message_offer.first()
        if first_message and first_message.files.exists():
            files = first_message.files.values('file', 'real_name').all()
            return files
        return []
    
    def get_contract_file_name(self):
        name = ''
        if self.contract_file_raw:
            name = self.contract_file_raw.name
        elif self.contract_file:
            name = self.contract_file.name
            
        if name: 
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            return name
        return ''

    def query_unread_message_in_offer(self, user):
        return user.offermessagereceiver_set.filter(seen_date__isnull=True,
                                                    message__offer=self)

    def count_unread_message_in_offer(self, user):
        return self.query_unread_message_in_offer(user).count()

    def check_offer_is_unread(self, user):
        return self.query_unread_message_in_offer(user).exists()

    def get_other_member_offer(self, user):
        offer = self.offer
        offer_user = OfferUser.objects.filter(offer=offer, user=user).first()
        if offer_user:
            offer_users = OfferUser.objects.filter(Q(offer=offer) & ~Q(position=offer_user.position))
            return AuthUser.objects.filter(pk__in=offer_users.values_list('user_id', flat=True), is_active=True)
        return AuthUser.objects.none()

    def get_member_offer(self):
        offer = self.offer
        offer_users = OfferUser.objects.filter(offer=offer)
        return AuthUser.objects.filter(pk__in=offer_users.values_list('user_id', flat=True), is_active=True)

    def save(self, *args, **kwargs):
        self.modified = datetime.datetime.now()
        try:
            self.offer.save()
        except:
            pass
        super(OfferCreator, self).save(*args, **kwargs)

    def check_can_delete_offer(self):
        if self.type != OfferCreator.TYPE_NORMAL or self.payment_status \
                or self.admin_payment_request or self.creator_payment_request:
            return False
        return True


class MessageFolder(ModelBase):
    folder_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    parent = models.ForeignKey('MessageFolder', related_name="child_folders", null=True, on_delete=models.CASCADE)
    name = models.CharField(max_length=512, null=False)
    message = models.ForeignKey('OfferMessage', related_name="folders", null=True, on_delete=models.CASCADE)


class MessageFile(ModelBase):
    file_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey('OfferMessage', related_name="files", null=True, on_delete=models.SET_NULL)
    file = models.FileField(upload_to='file', blank=True, max_length=1024)
    real_name = models.CharField(max_length=512, blank=True, null=True)
    peaks = models.TextField(null=True, blank=True, default="")
    folder = models.ForeignKey('MessageFolder', related_name="children", null=True, on_delete=models.CASCADE)
    type_file_name = models.CharField(null=True, max_length=20)
    file_info = models.CharField(null=True, max_length=500)

    def is_audio_file(self):
        if self.file:
            name = self.file.name
            return get_type_file(name)
        return 'other'

    def save(self, *args, **kwargs):
        if self.file and not self.real_name:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            self.real_name = name
        super(MessageFile, self).save(*args, **kwargs)

class DownloadedMessageFile(ModelBase):
    downloaded_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('accounts.AuthUser', related_name='message_files', blank=True,
                             null=True, on_delete=models.CASCADE)
    file = models.ForeignKey('MessageFile', related_name='user_downloaded', blank=True,
                             null=True, on_delete=models.CASCADE)


class OfferMessage(ModelBase):
    SYSTEM_MESSAGE = '2'
    TYPE_MESSAGE = (
        ('1', 'default'),
        ('2', 'system'),
    )
    message_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    content = models.TextField(verbose_name='コメント', blank=True, null=True)
    owner = models.ForeignKey('accounts.AuthUser', related_name='from_user', null=True, on_delete=models.CASCADE)
    seen_date = models.DateTimeField(null=True)
    chatroom = models.ForeignKey('ChatRoom', related_name='room_message', null=True, on_delete=models.CASCADE)
    offer = models.ForeignKey('OfferCreator', related_name='message_offer', null=True, on_delete=models.CASCADE)
    file = models.FileField(upload_to='file', blank=True, null=True)
    real_name = models.CharField(max_length=512, blank=True, null=True)
    is_near = models.BooleanField(default=False, blank=True)
    peaks = models.CharField(default="", max_length=1024)
    has_file = models.BooleanField(default=False)
    type_message = models.CharField(choices=TYPE_MESSAGE, max_length=100, default='1')
    system_message = models.TextField(verbose_name='コメント', blank=True, null=True)
    user = models.ForeignKey('accounts.AuthUser', related_name='offer_messages', null=True, on_delete=models.CASCADE)
    receivers = models.ManyToManyField('accounts.AuthUser', through='OfferMessageReceiver', blank=True)
    comment = models.TextField(verbose_name='コメント', blank=True, null=True)

    class Meta:
        ordering = ["created"]

    def is_audio_file(self):
        if self.file:
            name = self.file.name
            return get_type_file(name)
        return 'other'

    def make_message_status_seen(self):
        self.seen_date = datetime.datetime.now()
        self.save()

    def get_queryset(self):
        query_set = self.objects.get(Q(owner=self.owner))
        return query_set

    def created_day_in_week(self):
        return self.created.strftime('%a')

    def get_file_name(self):
        if self.real_name:
            return self.real_name
        else:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            return name

    def save(self, *args, **kwargs):
        if self.file and not self.real_name:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            self.real_name = name
        super(OfferMessage, self).save(*args, **kwargs)


class OfferMessageReceiver(models.Model):
    user = models.ForeignKey('accounts.AuthUser', on_delete=models.CASCADE)
    message = models.ForeignKey('OfferMessage', on_delete=models.CASCADE)
    seen_date = models.DateTimeField(null=True)
    
    def count_unread_offer(project_id, user_id):
        count = 0
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT COUNT(U0.`id`) AS `unread_count`
                FROM app_offermessagereceiver U0
                    INNER JOIN app_offermessage U2 ON (U0.`message_id` = U2.`message_id`)
                    INNER JOIN app_offerproject U3 ON (U2.`offer_id` = U3.`offer_creator_id`)
                WHERE U0.`user_id` = %s
                    AND U0.`seen_date` IS NULL
                    AND U3.`project_id` = %s
                    AND U3.`offer_status` != 3
                ORDER BY NULL
            """, [user_id, project_id.replace("-","")])
            count += cursor.fetchall()[0][0]
        with connection.cursor() as cursor:
            cursor.execute("""
				SELECT COUNT(U0.`id`) AS `unread_count`
                FROM app_messagereceiver U0
                    INNER JOIN app_productmessage U1 ON (U0.`message_id` = U1.`message_id`)
                    INNER JOIN app_offerproject U2 ON (U1.`offer_id` = U2.`offer_product_id`)
                WHERE U0.`user_id` = %s
                    AND U0.`seen_date` IS NULL
                    AND U2.`project_id` = %s
                    AND U2.`offer_status` != 3
                ORDER BY NULL;
			""", [user_id, project_id.replace("-","")])
            count += cursor.fetchall()[0][0]
        return count
    
    def count_unread_message(user_id, product_id):
        results = []
        with connection.cursor() as cursor:
            cursor.execute("""
				SELECT U3.`offer_id`, COUNT(U0.`id`) AS `unread_count`
				FROM app_offermessagereceiver U0
					INNER JOIN app_offermessage U2 ON (U0.`message_id` = U2.`message_id`)
					INNER JOIN app_offerproject U3 ON (U2.`offer_id` = U3.`offer_creator_id`)
				WHERE U0.`user_id` = %s
					AND U0.`seen_date` IS NULL
					AND U3.`project_id` = %s
				GROUP BY U2.`offer_id`
				ORDER BY NULL
			""", [user_id, product_id.replace("-","")])

            results.extend(cursor.fetchall())
            
        with connection.cursor() as cursor:
            cursor.execute("""
				SELECT U2.`offer_id`, COUNT(*) AS `unread_count`
                FROM
                    app_messagereceiver U0
                        INNER JOIN
                    app_productmessage U1 ON (U0.`message_id` = U1.`message_id`)
                        INNER JOIN
                    app_offerproject U2 ON (U1.`offer_id` = U2.`offer_product_id`)
                WHERE
                    U0.`user_id` = %s
                        AND U0.`seen_date` IS NULL
                        AND U2.`project_id` = %s
                        AND U2.`offer_status` != 3
                GROUP BY U2.`offer_id`
                ORDER BY NULL;
			""", [user_id, product_id.replace("-","")])

            results.extend(cursor.fetchall())
        return list(set(results))

class ChatRoom(ModelBase):
    room_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    admin = models.ForeignKey('accounts.AuthUser', related_name='chatroom_from', null=True, on_delete=models.CASCADE)
    creator = models.ForeignKey('accounts.AuthUser', related_name='chatroom_to', null=True, on_delete=models.CASCADE)
    conversation_time = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-conversation_time"]

    def last_creator_message(self):
        message = OfferMessage.objects.filter(chatroom=self, owner=self.creator)[:1]
        if message and not message[0].seen_date:
            return True
        return False

    def last_admin_message(self):
        message = OfferMessage.objects.filter(chatroom=self, owner=self.admin)[:1]
        if message and not message[0].seen_date:
            return True
        return False

    def last_message(self):
        return OfferMessage.objects.filter(chatroom=self)[:1]


class ProductOrder(ModelBase):
    order_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    comment = models.TextField(verbose_name='コメント')
    owner_id = models.IntegerField(default=0)
    user = models.ForeignKey('accounts.AuthUser', related_name='owner_product_order', blank=True,
                             null=True, on_delete=models.CASCADE)
    product_order_upload = models.ForeignKey('ProductOrderUpload', related_name='product_order_upload', blank=True,
                                             null=True, on_delete=models.SET_NULL, max_length=50)
    product = models.ForeignKey('Product', related_name='order_product', blank=True,
                                null=True, on_delete=models.SET_NULL)
    file = models.FileField(upload_to='file', blank=True, null=True)

    class Meta:
        ordering = ["created"]

    def created_en(self):
        return self.created.strftime('%y/%m/%d %a')

    def __str__(self):
        return str(self.order_id)

    def get_user(self):
        return AuthUser.objects.get(pk=self.owner_id)

    def created_str(self):
        return self.created.strftime('%y/%m/%d %a %H:%M')

    def get_file_name(self):
        return self.file.name.replace('file/((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)', '')


class ProductOrderUpload(ModelBase):
    product_order_upload_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file = models.FileField(upload_to='order')
    product = models.ForeignKey('Product', related_name='order_upload_product', blank=True,
                                null=True, on_delete=models.SET_NULL)
    owner_id = models.IntegerField(default=0)
    type_file_upload = models.IntegerField(default=0)
    type_file_pdf = models.IntegerField(null=True, blank=True)
    price_bill_pdf = models.IntegerField(null=True, blank=True)
    version = models.CharField(null=True, blank=True, max_length=50)
    tag = models.IntegerField(default=0)

    class Meta:
        ordering = ["created"]

    def created_str(self):
        return self.created.strftime('%y/%m/%d %a %H:%M')

    def __str__(self):
        return str(self.product_order_upload_id)

    def check_favorite(self):
        result = False
        files = ProductOrderUpload.objects.filter(
            Q(product_order_upload_id=self.product_order_upload_id) | Q(version=self.product_order_upload_id))
        for file in files:
            if file.tag == 3:
                return True
        return result

    def carousel_product_order_upload_list_index(self):
        arr_anpha = list("bcdefghijklmnopqrstuvwxyz")
        product_order_uploads = ProductOrderUpload.objects.filter(version=self.product_order_upload_id).order_by(
            'created')
        data = []
        if product_order_uploads:
            if self.file.name == '':
                arr_anpha.insert(0, 'a')
            for index, product_order_upload in enumerate(product_order_uploads):
                json = {
                    'pk': product_order_upload.pk,
                    'file': product_order_upload.file.url,
                    'index': arr_anpha[index],
                    'tag': product_order_upload.tag
                }
                data.append(json)
        return data

    def get_comments(self):
        return ProductOrder.objects.filter(product_order_upload_id=self.product_order_upload_id)

    def carousel_product_order_upload_list(self):
        return ProductOrderUpload.objects.filter(version=self.product_order_upload_id)


class PreviewCommentOrder(ModelBase):
    preview_comment_order_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    comment = models.ForeignKey('ProductOrder', related_name='preview_comment_order', blank=True,
                                null=True, on_delete=models.CASCADE)
    owner = models.ForeignKey('accounts.AuthUser', related_name='owner_comment_order', blank=True,
                              null=True, on_delete=models.SET_NULL)

    def __str__(self):
        return str(self.preview_comment_order_id)


class ProductManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_active=True)


class Product(ModelBase, BaseModelCropImg):
    objects = ProductManager()
    original_objects = models.Manager()

    GENRES = (
        ('1', 'サウンドロゴ'),
        ('2', 'ポッドキャスト'),
        ('3', '広告'),
        ('4', 'エンタメ')
    )

    ACR_STATUS_CHOICES = (
        ('1', 'not_start'),
        ('2', 'waiting'),
        ('3', 'run_check'),
    )
    product_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    last_update = models.DateTimeField(verbose_name='updated_at', default=datetime.datetime.now)

    code_name = models.CharField(max_length=255, null=True, blank=True, verbose_name='code_name')
    name = models.CharField(max_length=256, verbose_name='final_work')
    description = models.TextField(max_length=1000, null=True, blank=True, verbose_name='overview')
    client_name = models.CharField(max_length=255, null=True, blank=True, verbose_name='end_client')
    start_time = models.DateTimeField(null=True, blank=True, verbose_name='start_date')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='end_date')
    artists_block = models.ManyToManyField('accounts.AuthUser', through='app.BlockListArtist', blank=True, verbose_name='blocked_users')

    # for managing project progress and deliverables
    max_scene = models.IntegerField(blank=False, default=5, verbose_name='total_deliverbles_count')
    current_scene = models.IntegerField(blank=False, default=0, verbose_name='current_deliverbles_count')
    current_heart = models.FloatField(default=0, verbose_name='approval_deliverbles_count')
    progress = models.IntegerField(default=0)
    rating = models.FloatField(default=0)
    is_active = models.BooleanField(default=True, blank=True)

    # Settings for customizing the appearance of project banner
    image = models.FileField(blank=True, upload_to='images', verbose_name='banner_image_original')
    image_resized = models.FileField(blank=True, upload_to='images', verbose_name='banner_image_optimized')
    x = models.FloatField(default=0)
    y = models.FloatField(default=0)
    width = models.FloatField(default=0)
    height = models.FloatField(default=0)
    banner_font = models.CharField(max_length=10, null=True, blank=True, verbose_name='banner_font', default='1') # For React FE
    banner_color = models.CharField(max_length=10, null=True, blank=True, verbose_name='banner_color', default='1') # For React FE

    font_setting = models.ForeignKey('app.FontProjectSetting', blank=True,
                                    null=True, on_delete=models.SET_NULL, verbose_name='banner_font_old') # For Django, to be removed
    color_setting = models.ForeignKey('app.ColorProjectSetting', blank=True,
                                    null=True, on_delete=models.SET_NULL, verbose_name='banner_color_old') # For Django, to be removed


    owner = models.ForeignKey('accounts.AuthUser', related_name='owner_products', blank=True,
                              null=True, on_delete=models.SET_NULL, verbose_name='owner')
    created_by = models.ForeignKey('accounts.AuthUser', related_name='projects', blank=True, null=True,
                                   on_delete=models.SET_NULL)
    contact_artist = models.BooleanField(default=False)

    scene_list = models.ManyToManyField('ProductScene', related_name='product_scene')
    total_budget = models.FloatField(null=True, default=0,
                                 validators=[MaxValueValidator(***************)], verbose_name='total_budget')
    allow_url_share = models.BooleanField(verbose_name='allow_url_share（URLリンク共有を許可）', default=False)
    auto_use_last = models.BooleanField(verbose_name='auto_use_last（最終動画ファイルの納品データに。未使用だが復活の可能性あり）', default=False)
    share_link = models.BooleanField(verbose_name='share_link（URLリンク共有を許可）', default=True)
    genre = models.CharField(max_length=255, null=True, blank=True, verbose_name='genre（未使用）')
    genres = models.CharField(choices=GENRES, default='1', max_length=50, null=True, blank=True, verbose_name='genres（未使用）')
    information = models.FileField(blank=True, upload_to='file', verbose_name="information（旧スタッフクレジットPDF。復活可能性あり）")

    # ACR Cloud
    acr_status = models.CharField(choices=ACR_STATUS_CHOICES, max_length=100, default='1')
    acr_host = models.CharField(blank=True, null=True, max_length=100)
    acr_access_key = models.CharField(blank=True, null=True, max_length=100)
    acr_access_secret = models.CharField(blank=True, null=True, max_length=100)

    # These fields appear to be unused and can be safely removed
    logo = models.FileField(blank=True, upload_to='images')
    x_logo = models.FloatField(default=0)
    y_logo = models.FloatField(default=0)
    width_logo = models.FloatField(default=0)
    height_logo = models.FloatField(default=0)
    logo_name = models.CharField(max_length=1024, blank=True, null=True)
    image_name = models.CharField(max_length=1024, blank=True, null=True)
    real_name = models.CharField(max_length=1024, null=True, blank=True)
    text_note = models.CharField(max_length=255, null=True, blank=True, default='')

    class Meta:
        ordering = ["-created"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._original_image = self.image
        self._original_logo = self.logo

    def __str__(self):
        return self.name

    def get_master_producer_is_artist(self):
        return self.productuser_set.filter(user__role=AuthUser.CREATOR, user__is_active=True, position=ProductUser.PRODUCER, is_super_producer=True).first()

    def get_master_producer_is_admin(self):
        return self.productuser_set.filter(user__role=AuthUser.MASTERADMIN,
                                                        user__is_active=True).order_by('order_user').first()

    def get_master_producer_project(self):
        master_producer = self.get_master_producer_is_artist()
        return master_producer if master_producer else self.get_master_producer_is_admin()

    def get_sections_jp(self):
        return self.sections.filter(section_name__isnull=False).exclude(section_name='')

    def get_sections_en(self):
        return self.sections.filter(section_name_en__isnull=False).exclude(section_name_en='')

    def get_artist_has_offer(self):
        offers = OfferCreator.objects.filter(project=self).exclude(status=OfferCreator.STATUS_REJECT)
        artist_ids = list(offers.values_list('admin_id', flat=True))
        artist_ids += list(offers.values_list('creator_id', flat=True))
        pu = ProductUser.objects.filter(product=self, position__in=[ProductUser.PRODUCER])
        artist_ids += list(pu.values_list('user_id', flat=True))
        artists = AuthUser.objects.filter(pk__in=artist_ids, role=AuthUser.CREATOR, is_active=True)
        return artists

    def get_artist_has_offer_exists(self):
        offers = OfferCreator.objects.filter(project=self).exclude(status=OfferCreator.STATUS_REJECT).exists()
        if offers:
            return True
        pu = ProductUser.objects.filter(product=self, position__in=[ProductUser.PRODUCER]).exists()
        if pu:
            return True
        return False

    def get_owner(self):
        product_user = ProductUser.objects.filter(product_id=self.product_id, position=ProductUser.OWNER, user__is_active=True).first()
        if product_user:
            return product_user.user
    
    def get_owner_include_inactive(self):
        product_user = ProductUser.objects.filter(product_id=self.product_id, position=ProductUser.OWNER).first()
        if product_user:
            return product_user.user


    def last_update_str(self):
        return self.last_update.strftime('%y/%m/%d %a %H:%M')

    def get_current_scene(self):
        #get all scene_title of this product
        current_scene = SceneTitle.objects.filter(product_scene__in=self.scene_list.all())\
            .exclude(scene_title__isnull=True).count()
        return current_scene

    def get_current_heart(self):
        # get all scene_title has been mark as done of this product
        current_heart = SceneTitle.objects.filter(status__in=['5', '6'], product_scene__in=self.scene_list.all())\
            .exclude(scene_title__isnull=True).count()
        return current_heart

    def get_current_scene_rate(self):
        if self.max_scene == 0:
            return 0
        else:
            current_scene_rate = int((self.current_scene / self.max_scene) * 100)
            current_scene_rate = 100 if current_scene_rate > 100 else current_scene_rate
            value = current_scene_rate - self.get_current_heart_rate()
            return value if value > 0 else 0

    def get_current_heart_rate(self):
        if self.max_scene == 0:
            return 100
        else:
            value = round(self.current_heart * 100 / self.max_scene)
            return value if value < 100 else 100

    def update_current_and_heart_scene(self, *args, **kwargs):
        self.current_heart = self.get_current_heart()
        self.current_scene = self.get_current_scene()
        if self.max_scene < self.current_scene:
            self.max_scene = self.current_scene
        super(Product, self).save(*args, **kwargs)

    def get_director_in_project(self):
        pu = ProductUser.objects.filter(product=self, position=ProductUser.DIRECTOR, user__is_active=True)
        return AuthUser.objects.filter(pk__in=pu.values_list('user_id'))

    def get_producer_in_project(self):
        pu = ProductUser.objects.filter(product=self, position=ProductUser.PRODUCER, user__is_active=True)
        return AuthUser.objects.filter(pk__in=pu.values_list('user_id'))

    def get_director_producer_in_project(self):
        pu = ProductUser.objects.filter(product=self, position__in=[ProductUser.DIRECTOR, ProductUser.PRODUCER],
                                        user__is_active=True)
        return AuthUser.objects.filter(pk__in=pu.values_list('user_id'))

    def get_manage_director_in_project(self):
        pu = ProductUser.objects.filter(product=self, position__in=[ProductUser.PRODUCER, ProductUser.MASTERADMIN],
                                        user__is_active=True)
        return AuthUser.objects.filter(pk__in=pu.values_list('user_id'))

    def get_creator_in_project(self):
        pu = ProductUser.objects.filter(product=self, position=ProductUser.SPECIALIST, user__is_active=True)
        return AuthUser.objects.filter(pk__in=pu.values_list('user_id'))

    def get_director_producer_master_admin_in_project(self):
        master_admins = ProductUser.objects.filter(product=self,
                                                   position__in=[ProductUser.PRODUCER, ProductUser.MASTERADMIN],
                                                   user__is_active=True).order_by('order_user')
        directors = ProductUser.objects.filter(product=self,
                                               position=ProductUser.DIRECTOR,
                                               user__is_active=True).order_by('order_user')
        list_admin_ids = list(master_admins.values_list('user_id', flat=True)) + list(
            directors.values_list('user_id', flat=True))
        preserved = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(list_admin_ids)])
        return self.authuser_set.filter(pk__in=list_admin_ids, is_active=True).order_by(preserved)

    def get_master_admin_master_producer_in_project(self):
        pu = ProductUser.objects.filter(Q(product=self) & Q(user__is_active=True) & (
                    Q(user__role=AuthUser.MASTERADMIN) | Q(position=ProductUser.PRODUCER) & Q(is_super_producer=True)))
        return AuthUser.objects.filter(pk__in=pu.values_list('user_id'))

    def get_master_admin_in_project(self):
        pu = ProductUser.objects.filter(Q(product=self) & Q(user__is_active=True) &
                Q(user__role=AuthUser.MASTERADMIN))
        return AuthUser.objects.filter(pk__in=pu.values_list('user_id'))

    def save(self, *args, **kwargs):
        self.current_heart = self.get_current_heart()
        self.current_scene = self.get_current_scene()
        if self.current_scene > self.max_scene:
            self.max_scene = self.current_scene
        if self.max_scene == 0:
            self.progress = 100
        else:
            self.progress = round(self.current_heart / self.max_scene * 100)

        need_update_image_resized = False
        if self.image and self.image != self._original_image:
            img, output = self.crop_img('image', self.x, self.y, self.width, self.height, ['image_resized'])
            need_update_image_resized = True

        if self.logo and self.logo != self._original_logo:
            img, output = self.crop_img('logo', self.x_logo, self.y_logo, self.width_logo, self.height_logo)

        self.last_update = datetime.datetime.now()
        super(Product, self).save(*args, **kwargs)

        if need_update_image_resized:
            from app.tasks import update_image_resized

            update_image_resized.delay(self.pk)
            img.close()
            output.close()

    def add_product_scene(self, name):
        scene_list = ProductScene.original_objects.filter(product_scene=self)
        if scene_list.exists():
            order_max = scene_list.aggregate(Max('order'))['order__max'] + 1
        else:
            order_max = 0
        instance = ProductScene.objects.create(name=name, order=order_max)
        self.scene_list.add(instance)
        return instance

    def get_product_scene_choices(self):
        return [(ps.product_scene_id, ps.name) for ps in self.scene_list.all()]

    def get_product_scene(self):
        return self.scene_list.prefetch_related("scene_product_scene")

    def product_pk(self):
        return str(self.product_id)

    def check_export_csv(self):
        user_admin = list(map(lambda x: x['pk'], AuthUser.objects.filter(role__in=['admin', 'master_admin']).values('pk')))
        return SceneComment.objects.filter(scene__product=self,
                                           scene__tag=1,
                                           scene__product_scene__isnull=False,
                                           scene__title__isnull=False).exclude(
            owner_id__in=user_admin).order_by('-created').exists()

    def update_product_rating(self):
        rating = 0
        product_scenes = self.scene_list.all()
        rating_scene = SceneTitle.objects.filter(product_scene__in=product_scenes, rating__gt=0.0).exclude(last_version__isnull=True)
        if rating_scene:
            rating_sum = 0
            for r in rating_scene:
                rating_sum += r.rating
            rating = rating_sum / rating_scene.count()
            rating = round(rating, 2)
        if self.rating != rating:
            self.rating = rating
            self.save()

    def get_unread_offer_message(self, user):
        offer_users = OfferUser.objects.filter(offer__project=self, user=user)
        unread_seen = user.messagereceiver_set.filter(seen_date__isnull=True,
                                                      message__offer__project=self).values_list(
            'message_id', flat=True)
        offer_products = OfferProduct.objects.prefetch_related(Prefetch('message_product')) \
            .filter(
            Q(message_product__pk__in=unread_seen),
            Q(message_product__isnull=False),
            ~Q(condition=OfferProduct.STATUS_DELETED)
        ).distinct()

        unread_seen_creators = user.offermessagereceiver_set.filter(seen_date__isnull=True,
                                                                    message__offer__project=self).values_list(
            'message_id', flat=True)

        offer_creators = OfferCreator.objects.prefetch_related(Prefetch('message_offer')) \
            .filter(
            Q(message_offer__pk__in=unread_seen_creators),
            Q(message_offer__isnull=False),
            ~Q(status=OfferCreator.STATUS_REJECT)
        ).distinct()

        offers = OfferProject.objects.filter(
            Q(offer_creator__in=offer_creators) | 
            (Q(offer_product__in=offer_products) & Q(pk__in=offer_users.values_list('offer_id', flat=True)))
        ).distinct()
        # offers = OfferProject.objects.filter(Q(offer_creator__in=offer_creators) | Q(offer_product=offer_products) & Q(
        #     pk__in=offer_users.values_list('offer_id'))).distinct()

        return offers

    def count_unread_offer_message_admin(self, user):
        unread_offers = self.get_unread_offer_message(user)
        return unread_offers.count() if unread_offers else 0

    def get_member_project(self, user):
        if user.role in [AuthUser.MASTERADMIN, AuthUser.CREATOR]:
            pus = self.productuser_set.filter(user__is_active=True, is_invited=False, position=ProductUser.OWNER)
        else:
            pus = self.productuser_set.filter(user__is_active=True, is_invited=False,
                                              position__in=[ProductUser.MASTERADMIN, ProductUser.PRODUCER])
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True)).distinct()

    def get_member_offer_project(self):
        pus = self.productuser_set.filter(user__is_active=True, is_invited=False,
                                          position__in=[ProductUser.OWNER, ProductUser.MASTERADMIN,
                                                        ProductUser.PRODUCER])
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))

    def get_owners_project(self):
        pus = self.productuser_set.filter(user__is_active=True, is_invited=False, position__in=[ProductUser.OWNER])
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))

    def get_member_project_detail(self):
        pus = self.productuser_set.filter(user__is_active=True, is_invited=False,
                                          position__in=[ProductUser.OWNER, ProductUser.DIRECTOR, ProductUser.REVIEWER,
                                                        ProductUser.PRODUCER])
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))

    def get_member_manage_project_detail(self):
        pus = self.productuser_set.filter(user__is_active=True, is_invited=False,
                                          position__in=[ProductUser.OWNER, ProductUser.DIRECTOR, ProductUser.MASTERADMIN,
                                                        ProductUser.PRODUCER])
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))

    def get_all_member_project_detail(self):
        pus = self.productuser_set.filter(user__is_active=True, is_invited=False)
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))

    def get_member_manage_scene(self):
        pus = self.productuser_set.filter(user__is_active=True, is_invited=False,
                                          position__in=[ProductUser.DIRECTOR, ProductUser.PRODUCER])
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))

    def get_owner_project(self):
        pus = self.productuser_set.filter(user__is_active=True, is_invited=False, position=ProductUser.OWNER)
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))

    def get_owner_system_project(self):
        pus = self.productuser_set.filter(user__is_active=True, is_invited=False,
                                          position__in=[ProductUser.MASTERADMIN, ProductUser.PRODUCER])
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))

    def get_client_system_project(self):
        pus = self.productuser_set.filter(user__is_active=True, is_invited=False,
                                          position__in=[ProductUser.REVIEWER])
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))

    def get_producer_system_project(self):
        pus = self.productuser_set.filter(user__is_active=True, is_invited=False,
                                          position__in=[ProductUser.PRODUCER])
        return AuthUser.objects.filter(pk__in=pus.values_list('user_id', flat=True))

    def check_has_scene_in_project(self):
        scenes = Scene.objects.filter(product=self, title__last_version__isnull=False).exists()
        product_comment = self.product_owner_comments.exists()
        if scenes or product_comment or (self.offer_product.first() and self.offer_product.first().condition in OfferProduct.STATUS_SHOW_MENU) or \
                self.created_by and self.created_by.role == AuthUser.MASTERADMIN:
            return True
        return False

    def count_undownloaded_file_storage(self, user):
        comments = ProductComment.objects.filter(project=self, resolved=False, has_file=True).exclude(
            user__role=user.role)
        files = ProductCommentFile.objects.filter(message__in=comments)
        file_download = DownloadedProductComment.objects.filter(user__role=user.role, file__in=files)
        folders = ProductCommentFolder.objects.filter(message__in=comments, parent__isnull=True).count()
        list_folder_ids = set(list(files.filter(folder__isnull=False, file_id__in=file_download.values_list('file_id')) \
                                   .values_list('folder_id', flat=True)))
        folder_download = 0
        list_folder = []
        for folder_id in list_folder_ids:
            folder = ProductCommentFolder.objects.get(pk=folder_id)
            while True:
                if not folder.parent:
                    if folder not in list_folder:
                        list_folder.append(folder)
                        folder_download += 1
                    break
                folder = folder.parent
        count_comments = files.filter(folder__isnull=True).exclude(
            file_id__in=file_download.values_list('file_id')).count()
        undownload_count = count_comments + folders - folder_download
        return undownload_count

    def master_admin_get_spent_budget(self):
        offers = self.product_offers.filter(admin__role=AuthUser.MASTERADMIN, status__in=['1', '2', '3', '4'])
        if not offers.exists():
            current_total = 0
        else:
            current_total = offers.aggregate(Sum('reward')).get('reward__sum', 0)
        return current_total

    def master_admin_get_spent_budget_for_artist(self, user):
        offers = self.product_offers.filter(admin__role=AuthUser.MASTERADMIN, creator=user,
                                            status__in=['1', '2', '3', '4'])
        if not offers.exists():
            current_total = 0
        else:
            current_total = offers.aggregate(Sum('reward')).get('reward__sum', 0)
        return current_total

    def artist_get_spent_budget(self, user):
        offers = self.product_offers.filter(admin=user, status__in=['1', '2', '3', '4'])
        if not offers.exists():
            current_total = 0
        else:
            current_total = offers.aggregate(Sum('reward')).get('reward__sum', 0)
        return current_total

    def get_budget_artist(self, user):
        offers = self.product_offers.filter(creator=user, status__in=['2', '3', '4'],
                                            creator_payment_request_id__isnull=True, payment_status=False,
                                            legaxy_payment=False)
        if not offers.exists():
            current_total = 0
        else:
            current_total = offers.aggregate(Sum('reward')).get('reward__sum', 0)
        return current_total

    def get_artist_in_project(self):
        # manage_directors = self.get_manage_director_in_project()
        pu = ProductUser.objects.filter(product=self, position__in=[ProductUser.PRODUCER, ProductUser.MASTERADMIN])
        manage_directors = AuthUser.objects.filter(pk__in=pu.values_list('user_id'))
        creators = self.get_creator_in_project()
        offers = OfferCreator.objects.filter(Q(project=self) & Q(creator__is_active=True) &
                                             ~Q(status__in=['1', '5']) & \
                                             Q(admin__in=manage_directors) & \
                                             Q(creator__in=creators))
        return AuthUser.objects.filter(pk__in=offers.values_list('creator_id'))

    def get_project_name(self):
        offer = self.offer_product.first()
        if offer and offer.topic and offer.is_not_approved():
            return offer.topic.title
        return self.name

    def get_status_offer_product(self):
        offer = self.offer_product.first()
        if offer and offer.condition in ['4', '5', '6']:
            return True
        return False

    def get_name_by_project_setting(self):
        if self.text_note and self.text_note != '':
            return self.text_note
        else:
            return self.get_name_by_contract()

    def get_name_by_contract(self):
        offer = self.offer_product.first()
        if offer and not offer.is_not_approved():
            return self.name
        return self.get_name_by_code_name()

    def get_name_by_code_name(self):
        offer = self.offer_product.first()
        if self.code_name and self.code_name != '':
            return self.code_name
        if offer and offer.topic and offer.is_not_approved():
            return offer.topic.title
        return self.name

    def get_name_in_page_title(self):
        if self.code_name and self.code_name != '':
            return self.code_name
        if self.text_note and self.text_note != '':
            return self.text_note
        return self.get_name_by_contract()

    def check_can_delete_project(self):
        if OfferCreator.original_objects.filter(Q(project=self) & Q(status__in=OfferCreator.STATUS_OFFER_ACTIVE) & (
                Q(payment_status=True) | Q(admin_payment_request__isnull=False) | Q(
                creator_payment_request__isnull=False))):
            return False
        return True

    def check_is_charged_product(self):
        if self.charged.exists():
            return True
        return False

    def nearest_milestone(self):
        today = datetime.datetime.now().date()
        return self.milestones.filter(time__date__gt=today).order_by('time').first()


class ProductSetting(ModelBase):
    product_setting_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product_id = models.ForeignKey('Product', related_name='product_setting', blank=True, on_delete=models.CASCADE)
    project_name = models.CharField(max_length=255, null=True, verbose_name='プロジェクト名')
    brand = models.CharField(max_length=255, null=True, verbose_name='ブランド')
    project_description = models.TextField(null=True, verbose_name='プロジェクト説明')
    delivery_format = models.TextField(null=True, verbose_name='納品形式')
    code_name = models.CharField(max_length=255, null=True, verbose_name='コードネーム')
    genre = models.CharField(max_length=255, null=True, verbose_name='ジャンル')
    total_product = models.IntegerField(null=True, verbose_name='総演出数')
    show_remain_day = models.BooleanField(verbose_name='マイルストーン迄の残り日数を表示', default=False)
    show_project_name = models.BooleanField(verbose_name='バナーにプロジェクト名を表示', default=False)
    show_progress = models.BooleanField(verbose_name='マイルストーン迄の残り日数を表示', default=False)
    allow_url_share = models.BooleanField(verbose_name='URLリンク共有を許可', default=False)
    allow_download_video = models.BooleanField(verbose_name='動画ファイルのダウンロードを許可', default=False)
    auto_heart = models.BooleanField(verbose_name='自動でハート', default=False)
    auto_heart_day = models.IntegerField(verbose_name='自動でハート日後', null=True)

    def __str__(self):
        return str(self.product_setting_id)


class ProductDevelopPeriod(ModelBase):
    product_develop_period_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    product_setting_id = models.ForeignKey('ProductSetting', related_name='product_setting_period', blank=True,
                                           on_delete=models.CASCADE)
    period_start = models.DateTimeField(null=True)
    period_end = models.DateTimeField(null=True)

    def __str__(self):
        return str(self.product_develop_period_id)


class ProductMilestone(ModelBase):
    name = models.CharField(max_length=255, null=True, blank=True)
    time = models.DateTimeField(null=True, blank=True)
    product = models.ForeignKey('app.Product', related_name='milestones', null=True, blank=True, on_delete=models.CASCADE)


class ProductScene(ParanoidModel, ModelBase):
    # id(pk)
    product_scene_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    name = models.CharField(max_length=64)
    order = models.IntegerField(default=0)
    rating = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(5.0)], default=0.0)
    number_rating = models.IntegerField(default=0)

    class Meta:
        ordering = ["order"]

    def __str__(self):
        return self.name

    def super_delete(self, *args, **kwargs):
        pk = self.pk
        scene_titles = SceneTitle.original_objects.filter(product_scene=self)
        for st in scene_titles:
            st.delete()
        ps = ProductScene.original_objects.filter(pk=pk)
        ps.delete()

    def update_rating(self):
        rating_sum = 0
        count_rating = 0
        for scene_title in self.title_product_scene.filter(last_version__isnull=False):
            for rating in scene_title.owner_rating_title.all():
                count_rating += 1
                rating_sum += rating.rating
        if count_rating:
            self.rating = round(rating_sum / count_rating, 2)
        else:
            self.rating = 0
        self.save()


    def update_number_rating(self):
        num_rate = 0
        for scene_title in self.title_product_scene.all():
            if not scene_title.last_version:
                continue
            for rating in scene_title.owner_rating_title.all():
                num_rate += 1
        self.number_rating = num_rate
        self.save()


class PreviewScene(ModelBase):
    preview_scene_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    comment = models.ForeignKey('SceneComment', related_name='preview_comment', blank=True,
                                null=True, on_delete=models.CASCADE)
    owner = models.ForeignKey('accounts.AuthUser', related_name='owner_comment', blank=True,
                              null=True, on_delete=models.CASCADE)

    def __str__(self):
        return str(self.preview_scene_id)


class PreviewVideo(ModelBase):
    """
    Tracks which videos have been viewed by users.
    
    Used to determine if a user has new unwatched videos in a project.
    Note: Currently not properly implemented - records are only created in special cases.
    """
    preview_scene_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    owner = models.ForeignKey('accounts.AuthUser', related_name='owner_video', blank=True, null=True,
                              on_delete=models.CASCADE)
    scene = models.ForeignKey('Scene', related_name='owner_viewed_videos', blank=True,
                              null=True, on_delete=models.CASCADE)

    def __str__(self):
        return str(self.preview_scene_id)

    def save(self, *args, **kwargs):
        super(PreviewVideo, self).save(*args, **kwargs)
        product = self.scene.product
        if product:
            from accounts.services import update_product_user_new_video
            user = list()
            user.append(self.owner)
            products = list()
            products.append(product)
            update_product_user_new_video(products, user)


class RatingScene(ModelBase):
    rating_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    owner = models.ForeignKey('accounts.AuthUser', related_name='owner_rating', blank=True, null=True,
                              on_delete=models.SET_NULL)
    scene = models.ForeignKey('Scene', related_name='owner_rating_videos', blank=True,
                              null=True, on_delete=models.SET_NULL)
    rating = models.PositiveIntegerField(default=0, null=True)

    def save(self, *args, **kwargs):
        super(RatingScene, self).save(*args, **kwargs)
        self.scene.title.update_rating(self.scene)


class RatingSceneTitle(ModelBase):
    rating_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('accounts.AuthUser', related_name='user_rating', blank=True, null=True,
                              on_delete=models.SET_NULL)
    title = models.ForeignKey('SceneTitle', related_name='owner_rating_title', blank=True,
                              null=True, on_delete=models.SET_NULL)
    rating = models.PositiveIntegerField(default=0, null=True)

    def save(self, *args, **kwargs):
        super(RatingSceneTitle, self).save(*args, **kwargs)
        return self.title.update_rating()


class Audio(ModelBase):
    TYPE_AUDIO = (
        ('music', 'MUSIC'),
        ('voice', 'VOICE'),
        ('sound effect', 'SOUND EFFECTS'),
        ('vocal', 'VOCAL')
    )
    user = models.ForeignKey('accounts.Creator', related_name='audio_creator', blank=True,
                             null=True, on_delete=models.CASCADE)
    name = models.CharField(default='', null=True, blank='', verbose_name='サンプル名', max_length=200)
    audio = models.FileField(upload_to='audio/', verbose_name='オーディオデータ')
    type = models.CharField(choices=TYPE_AUDIO, default='music', max_length=20, verbose_name='サンプルの種類')
    acoustic = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True)
    sound = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True)
    realistic = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True)
    elegant = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True)
    gender = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(2)], null=True, blank=True)
    key_note = models.IntegerField(validators=[MinValueValidator(40), MaxValueValidator(88)], null=True, blank=True)
    age =models.IntegerField(validators=[MinValueValidator(0), MaxValueValidator(80)], null=True, blank=True)
    low_high =models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(3)], null=True, blank=True)

    def __str__(self):
        audio_name = self.audio.name
        file_extension = re.search(".[0-9a-zA-Z]{3,4}$", audio_name).group()
        return re.sub(r"audio\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', audio_name) + file_extension


class ContentSale(ModelBase):
    STATUS_ALBUM = (
        ('submitted', '申請中'),
        ('reviewed', '審査中'),
        ('publish', '公開中'),
        ('unpublished', '非公開'),
        ('deleted', 'deleted'),
    )
    owner = models.ForeignKey('accounts.Creator', related_name='content_owner', blank=True,
                              null=True, on_delete=models.CASCADE)
    title = models.CharField(default='', null=True, blank='', verbose_name='タイトル', max_length=60)
    price = models.IntegerField(verbose_name='価格（税込）', null=True, blank=True)
    desc = models.TextField(verbose_name='説明（OPTIONAL）', max_length=400)
    version = models.IntegerField(null=True, blank=True)
    parent = models.ForeignKey('app.ContentSale', related_name='content_parent', blank=True, null=True, on_delete=models.CASCADE)
    status = models.CharField(choices=STATUS_ALBUM, default='submitted', max_length=20)
    reviewed_by = models.ForeignKey('accounts.AuthUser', related_name='reviewed_by', blank=True,
                                    null=True, on_delete=models.CASCADE)

    class Meta:
        ordering = ["-created"]

    def __str__(self):
        return self.title

    def format_price(self):
        price = format(self.price, ',d') if self.price else 0
        return price

    def get_img(self):
        if self.image_sale.all():
            return self.image_sale.all()[0].image.url

        return False


class AudioSale(ModelBase):
    id = models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')
    sale = models.ManyToManyField('app.ContentSale', related_name='audio_sale', blank=True)
    file = models.FileField(upload_to='audio/', verbose_name='オーディオデータ')
    order = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return re.sub(r"audio\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', self.file.name)


class ImageSale(ModelBase):
    id = models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')
    sale = models.ManyToManyField('app.ContentSale', related_name='image_sale', blank=True)
    image = models.FileField(blank=True, upload_to='images')
    x = models.FloatField(default=0)
    y = models.FloatField(default=0)
    width = models.FloatField(default=0)
    height = models.FloatField(default=0)

    def save(self, *args, **kwargs):
        image = Image.open(io.BytesIO(self.image.read())).convert("RGBA")
        area = (self.x, self.y, self.width + self.x, self.height + self.y)
        crop_image = image.crop(area)
        output = io.BytesIO()
        crop_image.convert('RGB').save(output, format='PNG')
        self.image = InMemoryUploadedFile(output, 'FileField', self.image.name, 'image/png',
                                          output.getbuffer().nbytes, None)

        super(ImageSale, self).save(*args, **kwargs)


class SaleContent(ModelBase):
    profile = models.ForeignKey('accounts.CreatorProfile', related_name='content_profile', blank=True,
                                null=True, on_delete=models.CASCADE)
    last_version = models.ForeignKey('app.SaleContentVersion', related_name='last_content', blank=True,
                                     null=True, on_delete=models.CASCADE)
    last_published_version = models.ForeignKey('app.SaleContentVersion', related_name='published_content',
                                               blank=True, null=True, on_delete=models.CASCADE)
    order = models.IntegerField(default=0)
    parent = models.ForeignKey('app.SaleContent', related_name='child', blank=True, null=True,
                               on_delete=models.SET_NULL)

    def get_custom_sale_type(self):
        sale_version = self.last_published_version
        mapping = {
            '1': '購入',
            '2': 'セミオーダー',
            '3': '利用権購入',
            '4': 'テイストで新規制作'
        }
        return mapping.get(sale_version.sale_type, '購入')


class SaleContentVersion(ModelBase):
    """
    クリエイタープロフィール画面での作品販売情報とバージョン管理
    
    販売価格、コンテンツタイプ、審査ステータス、作品メタデータを管理し、
    音楽・音声コンテンツの販売と公開プロセスを制御する。
    """
    STATUS_ALBUM = (
        ('submitted', '申請中'),
        ('reviewed', '審査中'),
        ('publish', '公開中'),
        ('unpublished', '非公開'),
        ('deleted', 'deleted'),
    )

    VERSION_STATUS = (
        ('1', 'approve'),
        ('2', 'editing'),
        ('3', 'old'),
        ('4', 'reject'),
    )

    SALE_TYPE = (
        ('1', 'そのまま販売'),
        ('2', 'セミオーダーで販売'),
        ('3', ' 利用権を販売'),
        ('4', '販売しない')
    )

    THUMBNAIL_TYPE = (
        ('color', 'color'),
        ('image', 'image')
    )

    CONTENT_TYPE = (
        ('2mix', '2mix'),
        ('music', 'MUSIC'),
        ('voice', 'VOICE'),
        ('sound_effect', 'SOUND EFFECTS'),
    )

    sale = models.ForeignKey('app.SaleContent', related_name='sale_content', blank=True,
                             null=True, on_delete=models.CASCADE)
    title = models.TextField(default='', null=True, blank='', verbose_name='タイトル') # 作品名
    created_year = models.CharField(null=True, blank=True, default='', max_length=20) # 制作年
    credit = models.CharField(null=True, blank=True, default='', max_length=1000) # クレジット
    desc = models.TextField(verbose_name='説明（OPTIONAL）', null=True, blank=True, default='', max_length=1000) # 解説

    price = models.FloatField(verbose_name='開始価格（税込）', null=True, blank=True, default=0)
    max_price = models.FloatField(verbose_name='最高価格（税込）', null=True, blank=True, default=0)
    status = models.CharField(choices=STATUS_ALBUM, default='submitted', max_length=20)

    image = models.FileField(blank=True, upload_to='images')
    x = models.FloatField(default=0)
    y = models.FloatField(default=0)
    width = models.FloatField(default=0)
    height = models.FloatField(default=0)

    version_status = models.CharField(choices=VERSION_STATUS, default='2', max_length=20)
    sale_type = models.CharField(choices=SALE_TYPE, default='4', max_length=20)
    content_type = models.CharField(choices=CONTENT_TYPE, default='1', max_length=20)
    song_attribute1_min = models.IntegerField(null=True, blank=True, default=2,
                                              validators=[MinValueValidator(1), MaxValueValidator(5)])
    song_attribute1_max = models.IntegerField(null=True, blank=True, default=4,
                                              validators=[MinValueValidator(1), MaxValueValidator(5)])
    song_attribute2_min = models.IntegerField(null=True, blank=True, default=2,
                                              validators=[MinValueValidator(1), MaxValueValidator(5)])
    song_attribute2_max = models.IntegerField(null=True, blank=True, default=4,
                                              validators=[MinValueValidator(1), MaxValueValidator(5)])
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    default_thumbnail = models.CharField(null=True, blank=True, max_length=20)
    tags = models.ManyToManyField('HashTag', through='SaleContentTag', blank=True) 
    customizable_sale_setting = models.CharField(null=True, blank=True, default='', max_length=500) 
    show_thumbnail = models.CharField(choices=THUMBNAIL_TYPE, default='color', max_length=20)
    sale_youtube_link = models.CharField(null=True, blank=True, max_length=1000)

    def get_hash_tags_in_sale(self):
        return ' '.join([f'#{str(elem)}' for elem in list(self.tags.all().values_list('tag_name', flat=True))])

    def save(self, *args, **kwargs):
        song_attribute1_min = self.song_attribute1_min
        song_attribute1_max = self.song_attribute1_max
        song_attribute2_min = self.song_attribute2_min
        song_attribute2_max = self.song_attribute2_max

        if not song_attribute1_min:
            song_attribute1_min = 2
        if not song_attribute1_max:
            song_attribute1_max = 4
        if not song_attribute2_min:
            song_attribute2_min = 2
        if not song_attribute2_max:
            song_attribute2_max = 4

        if song_attribute1_max < song_attribute1_min:
            temp = song_attribute1_max
            song_attribute1_max = song_attribute1_min
            song_attribute1_min = temp

        if song_attribute2_max < song_attribute2_min:
            temp = song_attribute1_max
            song_attribute2_max = song_attribute2_min
            song_attribute2_min = temp
        self.song_attribute1_min = song_attribute1_min
        self.song_attribute1_max = song_attribute1_max
        self.song_attribute2_min = song_attribute2_min
        self.song_attribute2_max = song_attribute2_max

        super(SaleContentVersion, self).save(*args, **kwargs)


class HashTag(models.Model):
    tag_name = models.CharField(max_length=60)


class SaleContentTag(models.Model):
    sale_content = models.ForeignKey('SaleContentVersion', on_delete=models.CASCADE)
    tag = models.ForeignKey('HashTag', on_delete=models.CASCADE)


class AlbumVariation(ModelBase):
    sale_content = models.ForeignKey('app.SaleContentVersion', related_name='album', blank=True,
                                     null=True, on_delete=models.CASCADE)
    last_version = models.ForeignKey('app.AlbumVersion', related_name='last_album', blank=True,
                                     null=True, on_delete=models.CASCADE)
    last_published_version = models.ForeignKey('app.AlbumVersion', related_name='published_album',
                                               blank=True, null=True, on_delete=models.CASCADE)


class AlbumVersion(ModelBase):
    VERSION_STATUS = (
        ('1', 'approve'),
        ('2', 'editing'),
        ('3', 'old'),
        ('4', 'reject'),
    )
    album = models.ForeignKey('app.AlbumVariation', related_name='album_versions', blank=True,
                              null=True, on_delete=models.CASCADE)
    status = models.CharField(choices=VERSION_STATUS, default='2', max_length=20)
    file = models.FileField(upload_to='audio/', verbose_name='オーディオデータ')
    # real_name = models.CharField(max_length=512, null=True, blank=True)


class ProductComment(ModelBase):
    comment_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    comment = models.TextField(verbose_name='コメント', max_length=255)
    user = models.ForeignKey('accounts.AuthUser', related_name='comment_in_projects', blank=True,
                             null=True, on_delete=models.CASCADE)
    project = models.ForeignKey('Product', related_name='product_owner_comments', blank=True,
                                null=True, on_delete=models.CASCADE)
    file = models.FileField(upload_to='file_storage', blank=True, null=True)
    stamp = models.BooleanField(default=False, blank=True)
    real_name = models.CharField(max_length=512, null=True, blank=True)
    parent = models.ForeignKey('ProductComment', related_name='child_comments', blank=True, null=True,
                               on_delete=models.SET_NULL)
    resolved = models.BooleanField(default=False, blank=True)
    acr_result = models.TextField(null=True, blank=True)
    downloaded_users = models.ManyToManyField('accounts.AuthUser', through='ProductCommentDownloaded',
                                              related_name="downloaded_comments", blank=True)
    peaks = models.CharField(default="", max_length=1024)
    has_file = models.BooleanField(default=False)
    receivers = models.ManyToManyField('accounts.AuthUser', through='SceneCommentReceiver', blank=True)
    is_near = models.BooleanField(default=False, blank=True)

    class Meta:
        ordering = ["created"]

    def created_en(self):
        return self.created.strftime('%y/%m/%d %a')

    def __str__(self):
        return str(self.comment_id)

    def created_str(self):
        return self.created.strftime('%y/%m/%d %a %H:%M')

    def get_file_name(self):
        if self.real_name:
            return self.real_name
        else:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            return name

    def is_audio_file(self):
        if self.file:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            if file_extension in '.mp3,.wav':
                return True
        return False

    def save(self, *args, **kwargs):
        if self.file and not self.real_name:
            self.real_name = self.file.name

        project = self.project
        if project:
            project.modified = datetime.datetime.now()
            project.save()

        super(ProductComment, self).save(*args, **kwargs)


class ProductCommentFolder(ModelBase):
    folder_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    parent = models.ForeignKey('ProductCommentFolder', related_name="child_folders", null=True, on_delete=models.CASCADE)
    name = models.CharField(max_length=512, null=False)
    message = models.ForeignKey('ProductComment', related_name="folders", null=True, on_delete=models.CASCADE)


class ProductCommentFile(ModelBase):
    ACR_STATUS_CHOICES = (
        ('1', 'not_start'),
        ('2', 'uploaded'),
        ('3', 'has_result'),
        ('4', 'not_match'),
        ('5', 'error'),
    )

    file_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey('ProductComment', related_name="files", null=True, on_delete=models.SET_NULL)
    file = models.FileField(upload_to='file_storage', blank=True, max_length=1024)
    real_name = models.CharField(max_length=512, blank=True, null=True)
    peaks = models.TextField(null=True, blank=True, default="")
    folder = models.ForeignKey('ProductCommentFolder', related_name="children", null=True, on_delete=models.CASCADE)
    acr_result = models.TextField(null=True, blank=True)
    acr_status = models.CharField(choices=ACR_STATUS_CHOICES, max_length=100, default='1')
    acr_filescanning = models.TextField(null=True, blank=True, default="")
    acr_filescanning_id = models.CharField(null=True, blank=True, max_length=50, default='')
    acr_result_check_count = models.IntegerField(null=True, default=0)
    type_file_name = models.CharField(null=True, max_length=20)
    file_info = models.CharField(null=True, max_length=500)

    def is_audio_file(self):
        if self.file:
            name = self.file.name
            return get_type_file(name)
        return 'other'

    def save(self, *args, **kwargs):
        if self.file and not self.real_name:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            self.real_name = name
        super(ProductCommentFile, self).save(*args, **kwargs)


class DownloadedProductComment(ModelBase):
    downloaded_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file = models.ForeignKey('ProductCommentFile', related_name='product_file', blank=True,
                             null=True, on_delete=models.CASCADE)
    user = models.ForeignKey('accounts.AuthUser', related_name='user_downloaded', blank=True,
                             null=True, on_delete=models.CASCADE)

    def __str__(self):
        return str(self.downloaded_id)


class PreviewProductComment(ModelBase):
    preview_product_comment_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    comment = models.ForeignKey('ProductComment', related_name='preview_product_comment', blank=True,
                                null=True, on_delete=models.CASCADE)
    owner = models.ForeignKey('accounts.AuthUser', related_name='owner_comments', blank=True,
                              null=True, on_delete=models.CASCADE)

    def __str__(self):
        return str(self.preview_product_comment_id)


class ProductCommentDownloaded(ModelBase):
    downloaded_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    comment = models.ForeignKey('ProductComment', related_name='product_comment', blank=True,
                                null=True, on_delete=models.CASCADE)
    owner = models.ForeignKey('accounts.AuthUser', related_name='downloaded_product_comments', blank=True,
                              null=True, on_delete=models.CASCADE)


class OfferProduct(ModelBase):
    STATUS_IN_PROGRESS = ['1', '2', '3', '8', '9']
    STATUS_IN_DONE = ['4', '5', '6']
    STATUS_SHOW_MENU = ['3', '4', '5', '6', '9']
    STATUS_OFFER_ACTIVE = ['1', '2', '3', '4', '5', '6', '8', '9']
    STATUS_DELETED = '7'
    STATUS_DONE = '4'
    STATUS_APPROVED_CONTRACT = '3'
    STATUS_UPLOAD_CONTRACT = ['1', '2', '8', '9'] #can upload contract
    STATUS_CONTRACT = '2'
    STATUS_NEW = '1' # condition new
    STATUS_BILL = '5'
    STATUS_PAYMENTED = '6'
    STATUS_UPLOADED_PLAN = '8'
    STATUS_APPROVED_PLAN = '9'

    TAG_CHOICES = (
        ('1', 'new'),
        ('2', 'uploaded_plan'),
        ('3', 'choosed_plan'),
        ('4', 'uploaded_contact'),
        ('5', 'choosed_contact'),
        ('6', 'created_project')
    )

    TYPE_CHOICES = (
        ('1', 'usual'),
        ('2', 'unusual'),
        ('3', 'deleted'),
        ('4', 'closed')
    )

    CONDITION_CHOICES = (
        ('1', 'new'),
        ('2', 'uploaded_contract'),
        ('3', 'checked_contract'),
        ('4', 'done_offer'),
        ('5', 'uploaded_bill'),
        ('6', 'paid'),
        ('7', 'deleted'),
        ('8', 'uploaded_plan'),
        ('9', 'approved_plan')
    )

    PROJECT_TYPE = (
        ('branding', 'ソニックブランディング'),
        ('entertainment', 'エンタメ'),
        ('program', '番組'),
    )

    BGM_TYPE = (
        ('yes', '新規制作したい'),
        ('prepare_with_license', '利用権（非独占）ライセンスで用意したい'),
        ('no', 'なし')
    )

    SE_TYPE = (
        ('yes', '新規制作したい'),
        ('no', 'なし')
    )

    VOICE_TYPE = (
        ('yes', '新規収録したい'),
        ('edit_current_resource', '収録したものを整音したい'),
        ('no', 'なし')
    )

    CONTRACT_TYPE = (
        ('buyout', 'バイアウト（著作権譲渡）'),
        ('usage_right', '利用権（非独占）'),
        ('other', 'その他')
    )

    OWNERSHIP_TYPE = (
        ('provide_from_us', 'ドラフトを提示してほしい'),
        ('provide_from_client', 'ドラフトを提示するので、内容を検討してほしい')
    )

    DISCLOSURE_RULE = (
        ('can_public', 'プロダクトリリース後の実績公開を認める'),
        ('discuss_later', '別途協議する')
    )

    type = models.CharField(choices=TYPE_CHOICES, max_length=100, default='1')
    master_admin = models.ForeignKey('accounts.AuthUser', related_name='master_admin', blank=True, null=True,
                               on_delete=models.CASCADE)
    master_client = models.ForeignKey('accounts.AuthUser', related_name='master_client', blank=True, null=True,
                               on_delete=models.CASCADE)
    file = models.FileField(upload_to='file', blank=True, null=True)
    real_name = models.CharField(max_length=512, blank=True, null=True)
    description = models.TextField(max_length=1000, default='')
    deadline = models.DateTimeField(null=True, blank=True, default=None)
    status = models.CharField(choices=TAG_CHOICES, max_length=100, default='1')
    product = models.CharField(max_length=512, blank=True, null=True)
    contact = models.FileField(upload_to='file', blank=True, null=True)
    contact_real_name = models.CharField(max_length=512, blank=True, null=True)
    bill = models.FileField(upload_to='file', blank=True, null=True)
    bill_real_name = models.CharField(max_length=512, blank=True, null=True)
    amount = models.FloatField(verbose_name='価格', null=True, blank=True)
    receipt_id = models.CharField(null=True, blank=True, max_length=200)
    budget = models.FloatField(null=True, default=0,
                                 validators=[MaxValueValidator(***************)])
    project = models.ForeignKey('Product', related_name='offer_product', blank=True, null=True,
                                on_delete=models.CASCADE)
    condition = models.CharField(choices=CONDITION_CHOICES, max_length=100, default='1')
    updated_at = models.DateTimeField(null=True, blank=True, default=None)
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    project_type = models.CharField(choices=PROJECT_TYPE, default='entertainment', max_length=50)
    contract_type = models.CharField(choices=CONTRACT_TYPE, default='buyout', max_length=50)
    ownership_type = models.CharField(choices=OWNERSHIP_TYPE, default='provide_from_us', max_length=50)
    bgm = models.CharField(choices=BGM_TYPE, default='yes', max_length=50)
    se = models.CharField(choices=SE_TYPE, default='yes', max_length=50)
    voice = models.CharField(choices=VOICE_TYPE, default='yes', max_length=50)
    disclosure_rule = models.CharField(choices=DISCLOSURE_RULE, default='can_public', max_length=50)
    artist = models.ForeignKey('accounts.AuthUser', related_name='artist', blank=True, null=True,
                               on_delete=models.SET_NULL)
    # delete project_type, bgm, se, voice
    topic = models.ForeignKey('TopicGallery', related_name='offers', blank=True, null=True,
                              on_delete=models.SET_NULL)

    sale = models.ForeignKey('SaleContent', related_name='sale', blank=True, null=True,
                             on_delete=models.SET_NULL)
    sale_name = models.CharField(null=True, blank=True, default='', max_length=500)

    class Meta:
        ordering = ["modified"]

    def last_message(self):
        last_message = self.description
        if self.message_product.exists():
            last_message = self.message_product.last()
        return last_message

    def get_deadline_date(self):
        if self.deadline:
            return self.deadline.strftime('%y年%m月%d日')
        return ''

    def get_deadline_time(self):
        if self.deadline:
            return self.deadline.strftime('%H:%M')
        return ''

    def get_remaining_day(self):
        if self.deadline:
            difference = self.deadline - datetime.datetime.now()
            return difference.days
        return ''

    def save(self, *args, **kwargs):
        self.modified = datetime.datetime.now()
        try:
            self.offer.save()
        except:
            pass
        super(OfferProduct, self).save(*args, **kwargs)

    def query_unread_message_in_offer(self, user):
        return user.messagereceiver_set.filter(seen_date__isnull=True,
                                               message__offer=self)

    def count_unread_message_in_offer(self, user):
        return self.query_unread_message_in_offer(user).count()

    def check_offer_is_unread(self, user):
        return self.query_unread_message_in_offer(user).exists()

    def get_contract_in_offer(self):
        return self.files.filter(type_file=ProductMessageFile.CONTRACT).order_by('-created').first()

    def get_plan_in_offer(self):
        return self.files.filter(type_file=ProductMessageFile.PLAN).order_by('-created').first()

    def get_latest_form_contract_file(self):
        if self.is_not_approved():
            return self.files.filter(type_file__in=[ProductMessageFile.CONTRACT, ProductMessageFile.PLAN]).order_by('-created')
        else:
            approved_contract = self.files.filter(type_file__in=ProductMessageFile.CONTRACT).order_by('-created')
            if approved_contract.exists():
                return approved_contract
            else:
                return self.files.filter(type_file__in=ProductMessageFile.PLAN).order_by('-created')


    def get_bill_in_offer(self):
        return self.files.filter(type_file=ProductMessageFile.BILL).order_by('-created').first()

    def get_display_project_total(self):
        project = self.project
        if self.condition == ['2', '8']:
            form_contract_and_plan = FormContractAndPlan.objects.filter(product_message_files__offer=self).order_by(
                '-created', '-id').first()
            return calculator_budget(form_contract_and_plan)
        return project.total_budget

    def get_offer_last_plan(self):
        form_contracts = FormContractAndPlan.objects.filter(product_message_files__offer=self, product_message_files__type_file='5', product_message_files__message__isnull=False).order_by(
                        '-created', '-id')
        if form_contracts:
            return form_contracts.first()
        return False

    def get_offer_last_contract(self):
        form_contracts = FormContractAndPlan.objects.filter(product_message_files__offer=self, product_message_files__type_file='2', product_message_files__message__isnull=False).order_by(
                        '-created', '-id')
        if form_contracts:
            return form_contracts.first()
        return False

    def get_offer_last_bill(self):
        files = ProductMessageFile.objects.filter(offer=self, type_file='3', message__isnull=False).order_by('-created')
        if files:
            return files.first()
        return False

    def get_offer_second_last_plan(self):
        form_contracts = FormContractAndPlan.objects.filter(product_message_files__offer=self, product_message_files__type_file='5', product_message_files__message__isnull=False).order_by(
                        '-created', '-id')
        if form_contracts and form_contracts.count() > 1:
            return form_contracts[1]
        return False

    def get_offer_second_last_contract(self):
        form_contracts = FormContractAndPlan.objects.filter(product_message_files__offer=self, product_message_files__type_file='2', product_message_files__message__isnull=False).order_by(
                        '-created', '-id')
        if form_contracts and form_contracts.count() > 1:
            return form_contracts[1]
        return False

    def get_offer_second_last_bill(self):
        files = ProductMessageFile.objects.filter(offer=self, type_file='3', message__isnull=False).order_by('-created')
        if files and files.count() > 1:
            return files[1]
        return False

    def is_deletable(self):
        if self.condition in ['1', '8']:
            return True
        elif self.condition == '2' and self.dont_have_approved_plan():
            return True
        return False

    def is_not_approved(self):
        if self.condition in ['1', '8']:
            return True
        elif self.condition == '2' and self.dont_have_approved_plan():
            return True
        return False

    def dont_have_approved_plan(self):
        last_plan = self.get_offer_last_plan()
        if not last_plan or not last_plan.is_approved:
            return True
        return False


    def can_be_done(self):
        try:
            if self.condition in ['3', '9']:
                return True
            elif self.condition == '2' and not self.is_not_approved():
                return True
        except:
            pass
        return False


class SelectionOffer(ModelBase):
    selection = models.ForeignKey('SelectionGallery', related_name='offers', on_delete=models.SET_NULL, blank=True,
                                  null=True)
    offer = models.ForeignKey('OfferProduct', related_name='selections', blank=True, null=True,
                              on_delete=models.CASCADE)
    selection_offer_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    selection_content = models.TextField(default='')
    toggle_content = models.TextField(default='{}')
    work_name = models.TextField(default='')

    class Meta:
        ordering = ["created"]

    def get_selection_content(self):
        return json.loads(self.selection_content)

    def get_toggle_content(self):
        return json.loads(self.toggle_content)

    def get_value_attr_json(self, attr_name):
        try:
            return json.loads(getattr(self, attr_name))
        except json.JSONDecodeError:
            return {'error': 'Error value format'}


class ProductMessage(ModelBase):
    NORMAL_MESSAGE = '1'
    SYSTEM_MESSAGE = '2'
    OWNER_SYSTEM_MESSAGE = '3'

    TYPE_CHOICES = (
        ('1', 'new'),
        ('2', 'plan'),
        ('3', 'contract'),
        ('4', 'bill')
    )
    TYPE_MESSAGE = (
        ('1', 'default'),
        ('2', 'system'),
        ('3', 'done_project'),
    )
    type = models.CharField(choices=TYPE_CHOICES, max_length=100, default='1')
    message_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    content = models.TextField(verbose_name='コメント') # Probably unused, candidate for removal
    user = models.ForeignKey('accounts.AuthUser', related_name='owner', null=True, on_delete=models.CASCADE)
    seen_date = models.DateTimeField(null=True)
    offer = models.ForeignKey('OfferProduct', related_name='message_product', null=True, on_delete=models.CASCADE)
    file = models.FileField(upload_to='file', blank=True, null=True) # Probably unused, candidate for removal
    real_name = models.CharField(max_length=512, blank=True, null=True)
    receivers = models.ManyToManyField('accounts.AuthUser', through='MessageReceiver', blank=True) # 既読
    variation = models.ForeignKey('VariationOffer', related_name='message', null=True, on_delete=models.CASCADE)
    resolved = models.BooleanField(default=False, blank=True)
    type_message = models.CharField(choices=TYPE_MESSAGE, max_length=100, default='1')
    has_file = models.BooleanField(default=False)
    is_near = models.BooleanField(default=False, blank=True)
    comment = models.TextField(max_length=1500, null=True)
    parent = models.ForeignKey('ProductMessage', related_name='child_comment', blank=True, null=True,
                               on_delete=models.CASCADE)
    is_first_message = models.BooleanField(default=False, blank=True)

    class Meta:
        ordering = ["-created"]

    def make_message_status_seen(self):
        self.seen_date = datetime.datetime.now()
        self.save()

    def get_queryset(self):
        query_set = self.objects.get(Q(owner=self.owner))
        return query_set

    def created_day_in_week(self):
        return self.created.strftime('%a')

    def get_file_name(self):
        if self.real_name:
            return self.real_name
        else:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            return name

    def save(self, *args, **kwargs):
        if self.file and not self.real_name:
            self.real_name = self.file.name
        # self.offer_product.save()
        super(ProductMessage, self).save(*args, **kwargs)


class ProductMessageFolder(ModelBase):
    OFFER = '2'
    TYPE_CHOICES = (
        ('1', 'default'),
        ('2', 'offer')
    )
    folder_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    parent = models.ForeignKey('ProductMessageFolder', related_name="child_folders", null=True,
                               on_delete=models.CASCADE)
    name = models.CharField(max_length=512, null=False)
    message = models.ForeignKey('ProductMessage', related_name="folders", null=True, on_delete=models.CASCADE)
    offer = models.ForeignKey('OfferProduct', related_name="folders", null=True, on_delete=models.CASCADE)
    type_file = models.CharField(choices=TYPE_CHOICES, max_length=100, default='1')
    order_data = models.ForeignKey('OrderData', related_name="folders", null=True, on_delete=models.CASCADE)


class ProductMessageFile(ModelBase):
    CONTRACT = '2'
    BILL = '3'
    OFFER = '4'
    CONTRACT_BILL = ['2', '3']
    PLAN = '5'
    TYPE_CHOICES = (
        ('1', 'default'),
        ('2', 'contract'),
        ('3', 'bill'),
        ('4', 'offer'),
        ('5', 'plan')
    )
    file_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey('ProductMessage', related_name="files", null=True, on_delete=models.CASCADE)
    offer = models.ForeignKey('OfferProduct', related_name="files", null=True, on_delete=models.CASCADE)
    file = models.FileField(upload_to='storage', blank=True, max_length=1024)
    real_name = models.CharField(max_length=512, blank=True, null=True)
    peaks = models.TextField(null=True, blank=True, default="")
    folder = models.ForeignKey('ProductMessageFolder', related_name="children", null=True, on_delete=models.CASCADE)
    type_file = models.CharField(choices=TYPE_CHOICES, max_length=100, default='1')
    user = models.ForeignKey('accounts.AuthUser', related_name="files", null=True, on_delete=models.CASCADE)
    content_type = models.ForeignKey(ContentType, null=True, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField(null=True)
    form_object = GenericForeignKey()
    amount = models.FloatField(verbose_name='価格', null=True, blank=True)
    order_data = models.ForeignKey('OrderData', related_name="files", null=True, on_delete=models.CASCADE)
    type_file_name = models.CharField(null=True, max_length=20)
    file_info = models.CharField(null=True, max_length=500)
    def is_audio_file(self):
        if self.file:
            name = self.file.name
            return get_type_file(name)
        return 'other'

    def save(self, *args, **kwargs):
        if self.file and not self.real_name:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            self.real_name = name
        super(ProductMessageFile, self).save(*args, **kwargs)


class FormContractAndPlan(ModelBase):
    GENERATE_METHOD = 'generate'
    UPLOAD_METHOD = 'upload'
    CREATION_METHODS = (
        (GENERATE_METHOD, _('Generate Method')),
        (UPLOAD_METHOD, _('Upload Method'))
    )

    SOREMO_SERVICE = 1
    DESIGNATED = 2
    PICK_UP_AND_DELIVERY_METHOD_TYPES = (
        (SOREMO_SERVICE, _('soremo_service')),
        (DESIGNATED, _('designated'))
    )

    PLAN_TYPE = 1
    CONTRACT_TYPE = 2
    BOTH_TYPE = 3
    FORM_TYPES = (
        (PLAN_TYPE, _('plan')),
        (CONTRACT_TYPE, _('contract')),
        (BOTH_TYPE, _('all'))
    )

    creation_method = models.CharField(choices=CREATION_METHODS, max_length=32, null=True)
    subject = models.CharField(max_length=256, blank=True, null=True)
    work_content = models.TextField(default='{}')
    delivery_format = models.CharField(max_length=1000, null=True, blank=True)
    semi_delegate = models.BooleanField(default=False)
    pick_up_method = models.IntegerField(choices=PICK_UP_AND_DELIVERY_METHOD_TYPES, default=None)
    delivery_place = models.IntegerField(choices=PICK_UP_AND_DELIVERY_METHOD_TYPES, default=None)
    release_time = models.DateTimeField(null=False, blank=False)
    start_schedule = models.DateTimeField(null=True, blank=True)
    end_schedule = models.DateTimeField(null=True, blank=True)
    deadline = models.DateTimeField(null=True, blank=True)
    note = models.CharField(max_length=4000, blank=True, null=True)
    allow_public_contract = models.BooleanField(default=False)
    owner_info = models.TextField(default='{}')
    producer_info = models.TextField(default='{}')
    product_message_files = GenericRelation(ProductMessageFile)
    contract_code = models.CharField(max_length=8, null=True, unique=True)
    form_type = models.IntegerField(choices=FORM_TYPES, default=None)
    offer_product = models.ForeignKey('OfferProduct', related_name='form_contract_and_plans', null=True, on_delete=models.CASCADE)
    note_type = models.CharField(null=True, max_length=32)
    valid_date = models.DateTimeField(null=True, blank=True)
    is_approved = models.BooleanField(default=False)
    owner_infor = models.CharField(max_length=256, blank=True, null=True) #for Plan
    pre_deadline = models.DateTimeField(null=True, blank=True)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.contract_code = uuid.uuid4().hex.upper()[:8]
        while FormContractAndPlan.objects.filter(contract_code=self.contract_code).exists():
            self.contract_code = uuid.uuid4().hex.upper()[:8]

    def get_work_content(self):
        return json.loads(self.work_content)

    def get_owner_info(self):
        if not self.product_message_files.filter(type_file='2').exists():
            if not self.owner_info or self.owner_info == '[]':
                owner = self.offer_product.project.get_owner()
                default_owner_info = {
                    'address': owner.full_address if owner and owner.full_address else '',
                    'company_name': owner.affiliation_jp if owner and owner.affiliation_jp else '',
                    'job_title': owner.title_en if owner and owner.title_en else '',
                    'fullname': owner.fullname if owner and owner.fullname else '',
                }
                return default_owner_info
        return json.loads(self.owner_info)

    def get_producer_info(self):
        return json.loads(self.producer_info)

    def get_schedule_with_format(self):
        if not self.id or not self.start_schedule or not self.end_schedule:
            return ''

        return f'{self.start_schedule.strftime(FORMAT_DATE)} - {self.end_schedule.strftime(FORMAT_DATE)}'

    def get_date_of_deadline(self):
        if not self.id or not self.deadline:
            return ''

        return self.deadline.strftime(FORMAT_DATE)

    def get_time_of_deadline(self):
        if not self.id or not self.deadline:
            return ''

        return self.deadline.strftime(FORMAT_TIME)

    def get_release_time_with_format_date(self):
        return self.release_time.strftime(FORMAT_DATE)

    def get_valid_time_with_format_date(self):
        if self.valid_date:
            return self.valid_date.strftime(FORMAT_DATE)
        else:
            return ''

    def get_pre_deadline_with_format_date(self):
        if self.pre_deadline:
            return self.pre_deadline.strftime(FORMAT_DATE)
        else:
            return ''

    def get_valid_time_with_format_time(self):
        if self.valid_date:
            return self.valid_date.strftime(FORMAT_TIME)
        else:
            return ''

    def get_value_attr_json(self, attr_name):
        try:
            return json.loads(getattr(self, attr_name))
        except json.JSONDecodeError:
            return {'error': 'Error value format'}


class DownloadedProductMessageFile(ModelBase):
    downloaded_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('accounts.AuthUser', related_name='product_message_files', blank=True,
                             null=True, on_delete=models.CASCADE)
    file = models.ForeignKey('ProductMessageFile', related_name='user_downloaded', blank=True,
                             null=True, on_delete=models.CASCADE)


class MessageReceiver(models.Model):
    user = models.ForeignKey('accounts.AuthUser', on_delete=models.CASCADE)
    message = models.ForeignKey('ProductMessage', on_delete=models.CASCADE)
    seen_date = models.DateTimeField(null=True)


class PlanOffer(ModelBase):
    TAG_CHOICES = (
        ('1', 'processing'),
        ('2', 'done'),
    )
    content = models.TextField(verbose_name='コメント', blank=True, null=True)
    owner = models.ForeignKey('accounts.AuthUser', related_name='owner_plan', null=True, on_delete=models.CASCADE)
    offer_product = models.ForeignKey('OfferProduct', related_name='plan_offer', null=True, on_delete=models.CASCADE)
    file = models.FileField(upload_to='file', blank=True, null=True)
    real_name = models.CharField(max_length=512, blank=True, null=True)
    status = models.CharField(choices=TAG_CHOICES, max_length=100, default='1')
    class Meta:
        ordering = ["-created"]

    def make_message_status_seen(self):
        self.seen_date = datetime.datetime.now()
        self.save()

    def get_queryset(self):
        query_set = self.objects.get(Q(owner=self.owner))
        return query_set

    def created_day_in_week(self):
        return self.created.strftime('%a')

    def get_file_name(self):
        if self.real_name:
            return self.real_name
        else:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
            return name

    def save(self, *args, **kwargs):
        if self.file and not self.real_name:
            self.real_name = self.file.name
        super(PlanOffer, self).save(*args, **kwargs)


class VariationOffer(ModelBase):
    TAG_CHOICES = (
        ('1', 'processing'),
        ('2', 'chosen'),
    )
    variation_offer_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    owner = models.ForeignKey('accounts.AuthUser', related_name='owner_variation', null=True, on_delete=models.CASCADE)
    offer = models.ForeignKey('OfferProduct', related_name='variation_offer', null=True,
                                      on_delete=models.CASCADE)

    plan = models.FileField(upload_to='file', blank=True, null=True)
    plan_name = models.CharField(max_length=512, blank=True, null=True)
    contract = models.FileField(upload_to='file', blank=True, null=True)
    contract_name = models.CharField(max_length=512, blank=True, null=True)
    bill = models.FileField(upload_to='file', blank=True, null=True)
    bill_name = models.CharField(max_length=512, blank=True, null=True)
    is_chosen = models.CharField(choices=TAG_CHOICES, max_length=100, default='1')

    class Meta:
        ordering = ["modified"]

    def save(self, *args, **kwargs):
        if self.plan and not self.plan_name:
            self.plan_name = self.plan.name
        if self.contract and not self.contract_name:
            self.contract_name = self.contract.name
        if self.bill and not self.bill_name:
            self.bill_name = self.bill.name
        self.modified = datetime.datetime.now()
        super(VariationOffer, self).save(*args, **kwargs)


class Post(ModelBase):
    post_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=64, null=True, blank=True)
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    text_range = models.CharField(max_length=64, null=True, blank=True)

    class Meta:
        ordering = ["-created"]

    def __str__(self):
        return self.title


class PostItem(ModelBase):
    TAG_CHOICES = (
        ('1', 'new'),
        ('2', 'update'),
    )
    post_item_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    post = models.ForeignKey('app.Post', related_name='items', null=True, on_delete=models.CASCADE)
    content = models.TextField(verbose_name='コメント', blank=True, null=True)
    type = models.CharField(choices=TAG_CHOICES, max_length=20, default='1')
    file = models.FileField(upload_to='info', blank=True, null=True)
    order = models.IntegerField(default=0)

    class Meta:
        ordering = ["order"]


class BlockListArtist(ModelBase):
    user = models.ForeignKey('accounts.AuthUser', related_name='project_block', on_delete=models.CASCADE)
    project = models.ForeignKey('Product', related_name='artist_blocked', on_delete=models.CASCADE)


class OfferProject(ModelBase):
    OFFER_CREATOR = '1'
    OFFER_PRODUCT = '2'
    STATUS_IN_PROGRESS = ['1', '2', '4']
    STATUS_IN_DONE = ['2']
    STATUS_OFFER_ACTIVE = ['1', '2']
    STATUS_DELETED = '3'

    TYPE_CHOICES = (
        ('1', 'offer_creator'),
        ('2', 'offer_product'),
    )

    STATUS_CHOICES = (
        ('1', 'progress'),
        ('2', 'done'),
        ('3', 'deleted'),
    )
    offer_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    offer_creator = models.OneToOneField('OfferCreator', related_name='offer', blank=True, null=True,
                                         on_delete=models.CASCADE)
    offer_product = models.OneToOneField('OfferProduct', related_name='offer', blank=True, null=True,
                                         on_delete=models.CASCADE)
    project = models.ForeignKey('Product', related_name='offers', on_delete=models.CASCADE)
    type_offer = models.CharField(choices=TYPE_CHOICES, max_length=20, default='1')
    offer_status = models.CharField(choices=STATUS_CHOICES, max_length=20, default='1')

    def get_creator_in_offer_creator(self):
        offer_creator = OfferUser.objects.filter(offer=self, position=OfferUser.CREATOR)
        return AuthUser.objects.filter(pk__in=offer_creator.values_list('user_id', flat=True), is_active=True)

    def get_admin_in_offer_creator(self):
        offer_admin = OfferUser.objects.filter(offer=self, position=OfferUser.ADMIN)
        return AuthUser.objects.filter(pk__in=offer_admin.values_list('user_id', flat=True), is_active=True)

    def save(self, *args, **kwargs):
        if self.type_offer == OfferProject.OFFER_CREATOR:
            offer_creator = self.offer_creator
            if offer_creator.status == OfferCreator.STATUS_REJECT:
                offer_status = '3'
            elif offer_creator.status in OfferCreator.STATUS_IN_PROGRESS:
                offer_status = '1'
            else:
                offer_status = '2'
        else:
            offer_product = self.offer_product
            if offer_product.condition == OfferProduct.STATUS_DELETED:
                offer_status = '3'
            elif offer_product.condition in OfferProduct.STATUS_IN_PROGRESS:
                offer_status = '1'
            else:
                offer_status = '2'
        self.offer_status = offer_status
        if not (self.type_offer == OfferProject.OFFER_CREATOR and self.offer_creator.type == '2'):
            self.project.last_update = datetime.datetime.now()
            self.project.save()
        super(OfferProject, self).save(*args, **kwargs)


class OfferUser(ModelBase):
    ADMIN = 'admin'
    CREATOR = 'creator'
    OWNER = 'owner'
    MASTER_ADMIN = 'master_admin'

    USER_ROLES = (
        (MASTER_ADMIN, '管理者'),
        (ADMIN, 'ディレクター'),
        (OWNER, 'オーナー'),
        (CREATOR, 'アーティスト'),
    )

    position = models.CharField(choices=USER_ROLES, default=ADMIN, max_length=20, verbose_name='権限')
    user = models.ForeignKey('accounts.AuthUser', on_delete=models.CASCADE)
    offer = models.ForeignKey('OfferProject', on_delete=models.CASCADE)


class ListWork(ModelBase):
    title = models.CharField(max_length=64)
    description = models.TextField(null=True)
    order = models.IntegerField(default=1)
    sample_list_work = models.ManyToManyField(
        to="SaleContent",
        through='SaleContentListWork',
    )

    class Meta:
        ordering = ["order"]

    def get_max_order_in_list(self):
        last_sale = self.salecontentlistwork_set.all().order_by('-order').first()
        return (last_sale.order + 1) if last_sale else 1


class SaleContentListWork(models.Model):
    sale_content = models.ForeignKey(SaleContent, on_delete=models.CASCADE)
    list_work = models.ForeignKey(ListWork, on_delete=models.CASCADE)
    order = models.IntegerField(default=1)

    class Meta:
        ordering = ["-order"]


class TopicGallery(ModelBase, BaseModelCropImg):
    topic_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    video = models.FileField(upload_to='movie', verbose_name='動画ファイル', null=True, blank=True, default=None)
    thumbnail = models.FileField(upload_to='thumbnail', null=True, blank=True)
    x = models.FloatField(default=0)
    y = models.FloatField(default=0)
    width = models.FloatField(default=0)
    height = models.FloatField(default=0)
    title = models.CharField(max_length=255, default='')
    categories = models.ManyToManyField('Category', through='TopicCategory', blank=True)
    overview = models.CharField(max_length=255, default='')
    description = models.TextField(max_length=1000, default='')
    tags = models.ManyToManyField('HashTag', through='TopicTag', blank=True)
    file = models.FileField(upload_to='file', null=True, blank=True, default=None)
    file_real_name = models.CharField(max_length=512, null=True, blank=True)
    thumbnail_real_name = models.CharField(max_length=512, null=True, blank=True)
    video_real_name = models.CharField(max_length=512, null=True, blank=True)
    order = models.IntegerField(default=1)
    is_deleted = models.BooleanField(default=False)
    user = models.ForeignKey('accounts.AuthUser', related_name="topics", on_delete=models.CASCADE, blank=True,
                             null=True)
    image = models.FileField(upload_to='thumbnail', null=True, blank=True)
    image_real_name = models.CharField(max_length=512, null=True, blank=True)

    class Meta:
        ordering = ["-order"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._original_thumbnail = self.thumbnail

    def save(self, *args, **kwargs):
        # if self.thumbnail and self.thumbnail != self._original_thumbnail:
        #     img, output = self.crop_img('thumbnail', self.x, self.y, self.width, self.height)
        super(TopicGallery, self).save(*args, **kwargs)

    def get_sale_contents_in_topic(self):
        selections = self.selections.all()
        sale_selection = SaleContentSelection.objects.filter(selection__in=selections)
        return SaleContent.objects.filter(pk__in=sale_selection.values_list('sale_content_id'))

    def get_categories_in_topic(self):
        return ' '.join([str(elem) for elem in list(self.categories.all().values_list('category_name', flat=True))])

    def get_hash_tags_in_topic(self):
        return ' '.join([f'#{str(elem)}' for elem in list(self.tags.all().values_list('tag_name', flat=True))])

    def get_thumbnail_in_topic(self):
        if self.image:
            return self.image.url
        if self.thumbnail:
            return self.thumbnail.url
        return ''


class TopicTag(ModelBase):
    topic = models.ForeignKey('TopicGallery', on_delete=models.CASCADE)
    tag = models.ForeignKey('HashTag', on_delete=models.CASCADE)


class Category(ModelBase):
    category_name = models.CharField(max_length=60)


class TopicCategory(ModelBase):
    topic = models.ForeignKey('TopicGallery', on_delete=models.CASCADE)
    category = models.ForeignKey('Category', on_delete=models.CASCADE)


class SelectionGallery(ModelBase):
    topic = models.ForeignKey('TopicGallery', related_name='selections', on_delete=models.CASCADE, blank=True, null=True,)
    selection_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255, default='')
    description = models.TextField(max_length=1000, default='')
    sale_contents = models.ManyToManyField(to="SaleContent", through='SaleContentSelection')
    selection_content = models.TextField(default='{}')
    toggle_content = models.TextField(default='{}')
    order = models.IntegerField(default=1)

    class Meta:
        ordering = ["order"]

    def get_selection_content(self):
        return json.loads(self.selection_content)

    def get_toggle_content(self):
        return json.loads(self.toggle_content)

    def get_value_attr_json(self, attr_name):
        try:
            return json.loads(getattr(self, attr_name))
        except json.JSONDecodeError:
            return {'error': 'Error value format'}


class SaleContentSelection(models.Model):
    sale_content = models.ForeignKey(SaleContent, on_delete=models.CASCADE)
    selection = models.ForeignKey(SelectionGallery, on_delete=models.CASCADE)
    order = models.IntegerField(default=1)
    default_thumbnail = models.CharField(null=True, blank=True, max_length=20, default='C4C4C4')

    class Meta:
        ordering = ["order"]


class SectionCredit(ModelBase):
    section_name = models.CharField(max_length=256, blank=True, null=True, default='')
    section_name_en = models.CharField(max_length=256, blank=True, null=True, default='')
    explanation = models.CharField(max_length=1000, blank=True, null=True, default='')
    explanation_en = models.CharField(max_length=1000, blank=True, null=True, default='')
    project = models.ForeignKey('Product', related_name='sections', on_delete=models.CASCADE, blank=True,
                                null=True)
    order = models.IntegerField(default=1)

    class Meta:
        ordering = ["order"]

    def save(self, *args, **kwargs):
        if not self.explanation:
            self.explanation = ''
        if not self.explanation_en:
            self.explanation_en = ''
        if not self.section_name:
            self.section_name = ''
        if not self.section_name_en:
            self.section_name_en = ''
        super(SectionCredit, self).save(*args, **kwargs)
    
    def to_dict(self):
        return {
            "section_name": self.section_name,
            "section_name_en": self.section_name_en,
            "explanation": self.explanation,
            "explanation_en": self.explanation_en,
            "order": self.order
        }


class ItemSectionCredit(ModelBase):
    #Item no longer foreign with section as section and item now in same level, this field can remove after deploy, use project foreignkey instead
    section = models.ForeignKey('SectionCredit', related_name='items', on_delete=models.CASCADE, blank=True,
                                null=True)
    project_artist = models.ForeignKey('accounts.ProductUser', related_name='item_sections', on_delete=models.CASCADE,
                               blank=True, null=True)
    link_profile = models.BooleanField(default=True)
    title = models.CharField(max_length=256, blank=True, null=True, default='')
    position = models.CharField(max_length=256, blank=True, null=True, default='')
    artist_name = models.CharField(max_length=256, blank=True, null=True, default='')
    artist_name_en = models.CharField(max_length=256, blank=True, null=True, default='')
    order = models.IntegerField(default=1)
    project = models.ForeignKey('Product', related_name='items_staff_credit', on_delete=models.CASCADE, blank=True,
                                null=True)

    class Meta:
        ordering = ["order"]


class OrderData(ModelBase):
    TYPE_CONTACT = (
        ('1', 'sale'),
        ('2', 'artist'),
    )
    data_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.CharField(max_length=1000, null=False, default='')
    offer = models.ForeignKey('OfferProduct', related_name="order_data", null=True, on_delete=models.CASCADE)
    type_contact = models.CharField(choices=TYPE_CONTACT, max_length=100, default='1')
    sale_content = models.ForeignKey(SaleContent, null=True, on_delete=models.CASCADE)
    artist = models.ForeignKey('accounts.AuthUser', on_delete=models.CASCADE)
    sale_name = models.CharField(max_length=500, null=False, default='')
    budget = models.FloatField(null=True, default=0,
                               validators=[MaxValueValidator(***************)])

class DraftMessage(ModelBase):
    TYPE_COMMENT = (
        ('1', 'ProductMessage'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('accounts.AuthUser', related_name="draft_user", null=False, on_delete=models.CASCADE)
    type_comment = models.CharField(choices=TYPE_COMMENT, max_length=100, default='1')
    draft_content = models.CharField(max_length=1000, null=True, default='')
    offer_project = models.ForeignKey('OfferProject', related_name='draft_offer', on_delete=models.CASCADE, null=False, db_column='offer_id')
    file_id = models.TextField(max_length=1000000, null=True)
    folder_id = models.TextField(max_length=1000000, null=True)
    list_name_file = models.TextField(max_length=1000000, blank=True)

class UserOnlineStatus(ModelBase):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('accounts.AuthUser', related_name='user_status', null=False, on_delete=models.CASCADE)
    status = models.BooleanField(default=False)

    class Meta:
        ordering = ["-modified"]


class UserProductCharge(ModelBase):
    STATUS = (
        ('1', 'process'),
        ('2', 'paid'),
        ('3', 'cancelled'),
        ('4', 'closed')
    )
    code_name = models.CharField(max_length=255, null=True, blank=True)
    name = models.CharField(max_length=256)
    total_budget = models.FloatField(null=True, default=0,
                                     validators=[MaxValueValidator(***************)])
    creator = models.ForeignKey('accounts.AuthUser', on_delete=models.CASCADE, null=True)
    amount = models.FloatField(validators=[MaxValueValidator(***************)], null=True, default=0)
    status = models.CharField(choices=STATUS, default='1', max_length=50, null=False, blank=True)
    product = models.ForeignKey('app.Product', related_name='charged',
                                on_delete=models.CASCADE, null=True, blank=True)
    stripe_charge_id = models.CharField(null=True, blank=True, max_length=200)
    stripe_receipt_id = models.CharField(null=True, blank=True, max_length=200)
    service_fee = models.FloatField(validators=[MaxValueValidator(100)], null=True, default=0)


class CreatorOfferFile(ModelBase):
    offer = models.ForeignKey('app.OfferCreator', related_name='offer_files', on_delete=models.CASCADE, null=False, blank=False)
    file = models.FileField(upload_to='file', blank=True, null=True)

    def get_file_name(self):
        first_message = self.message_offer.first()
        if self.file:
            return first_message.files.first().real_name
        else:
            name = self.file.name
            file_extension = re.search(".[0-9a-zA-Z]{3,4}$", name).group()
            name = re.sub(r"file\/|((_[0-9a-zA-Z]{7})?.[0-9a-zA-Z]{3,4}$)", '', name)
            name += file_extension
        return name


class ColorProjectSetting(models.Model):
    active = models.BooleanField(default=True)
    code = models.CharField(max_length=50, null=False, blank=False)
    css_code = models.CharField(max_length=500, null=False, blank=False)
    text_color_css = models.CharField(max_length=500, null=True, blank=False)

class FontProjectSetting(models.Model):
    active = models.BooleanField(default=True)
    code = models.CharField(max_length=50, null=False, blank=False)
    css_code = models.CharField(max_length=500, null=False, blank=False)
    sample_code = models.CharField(max_length=150, null=True, blank=True, default="CODE_NAME")

class ReviewOffer(models.Model):
    created = models.DateTimeField(default=timezone.now)
    modified = models.DateTimeField(default=timezone.now)

    STATUS = (
        ('1', 'OK'),
        ('2', '匿名ならOK'),
        ('3', '許可しない')
    )
    
    REVIEW = (
        ('3', 'NO_ACTION'),
        ('1', 'LIKE'),
        ('2', 'DISLIKE'),
    )

    user_id = models.CharField(null=True, blank=True, default=None, max_length=1000)
    review = models.CharField(choices=REVIEW, null=True, blank=True, default=None, max_length=1000)
    offer_id = models.CharField(null=True, blank=True, default=None, max_length=1000)
    status = models.CharField(choices=STATUS, null=True, blank=True, default=None, max_length=1000)
    note = models.CharField(null=False, blank=True, default=None, max_length=1500)


class MediaConvertJob(ModelBase):
    """
    Model to store AWS MediaConvert job information for media file optimization
    Optimized for left joins with tables containing media file fields
    """
    # Primary key removed as it's inherited from ModelBase
    original_object_key = models.CharField(
        max_length=512, 
        verbose_name='Original S3 object key', 
        db_index=True,
        help_text='S3 key of the original media file'
    )
    converted_media_key = models.CharField(
        max_length=512, 
        null=True, 
        blank=True, 
        verbose_name='Converted media S3 key',
        help_text='S3 key of the converted media file (HLS format)'
    )
    waveform_data_key = models.CharField(
        max_length=512, 
        null=True, 
        blank=True, 
        verbose_name='Waveform data S3 key',
        help_text='S3 key of the waveform data file'
    )
    status = models.CharField(
        max_length=20, 
        default='pending', 
        verbose_name='Conversion status', 
        db_index=True, 
        help_text='Conversion status: pending, processing, completed, failed, error'
    )
    job_id = models.CharField(
        max_length=128, 
        null=True, 
        blank=True, 
        verbose_name='AWS MediaConvert job ID', 
        db_index=True
    )
    error_message = models.TextField(
        null=True, 
        blank=True, 
        verbose_name='Error message'
    )

    class Meta:
        db_table = 'app_mediaconvertjob'
        verbose_name = 'Media Convert Job'
        verbose_name_plural = 'Media Convert Jobs'
        # Composite index for optimized status and time-based queries
        indexes = [
            models.Index(fields=['status', 'created'], name='idx_mcj_status_created'),
        ]

    def __str__(self):
        return f"MediaConvertJob: {self.original_object_key} ({self.status})"

    def is_completed(self):
        """Check if the conversion process is completed"""
        return self.status == 'COMPLETE'

    def is_processing(self):
        """Check if the job is currently being processed"""
        return self.status in ['PENDING', 'PROCESSING']

    def has_error(self):
        """Check if there was an error during conversion"""
        return self.status in ['FAILED', 'ERROR']
        
    # Note: Status values are flexible - adjust according to your MediaConvert workflow

    @classmethod
    def get_by_file_path(cls, file_path):
        """
        Get MediaConvertJob information based on file path
        This method is optimized for left joins from other models
        """
        # Process file path to match with original_object_key
        if file_path.startswith('/'):
            file_path = file_path[1:]  # Remove leading '/' if present
        
        return cls.objects.filter(original_object_key=file_path).first()

    @classmethod
    def get_completed_conversions(cls):
        """Get all completed conversions"""
        return cls.objects.filter(status='COMPLETE')

    @classmethod
    def get_pending_conversions(cls):
        """Get all pending or processing conversions"""
        return cls.objects.filter(status__in=['PENDING', 'PROCESSING'])
