# -*- coding: utf-8 -*-
# Created by SUN-ASTERISK\le.quy.quyet at 11/12/2021
import logging

from django.http import JsonResponse
from django.shortcuts import redirect
from django.utils.translation import gettext as _
from django.template.loader import render_to_string

from accounts.models import AuthUser
from app.views import create_message_after_upload
from app.models import Product, ProductUser, OfferProject, FormContractAndPlan
from app.app_forms.contract_plan_upload_form import ContractPlanUploadForm
from app.app_services.upload_contract_services import (get_current_form_contract_and_plan_service)


def get_current_form_upload_and_plan(request):
    product_id = request.GET.get('product_id')
    if not product_id:
        return JsonResponse({'error': 'product_id is required.'}, status=400)

    product = Product.objects.filter(product_id=product_id).first()
    if not product:
        return JsonResponse({'error': 'Product not found.'}, status=404)

    form_upload_and_plan, product_message_file = get_current_form_contract_and_plan_service(product)
    context = {
        'form_contract_and_plan': form_upload_and_plan,
        'product_message_file': product_message_file,
        'project': product,
    }
    if not product_message_file or product_message_file.type_file == '5':
        if not product_message_file or product_message_file.offer.condition in ['1', '8']:
            form_upload_html = render_to_string('direct/_upload_plan.html', context)
            file_type = 'plan'
        else:
            form_upload_html = render_to_string('direct/_upload_contract.html', context)
            file_type = 'contract'
    elif product_message_file.type_file == '2':
        form_upload_html = render_to_string('direct/_upload_contract.html', context)
        file_type = 'contract'
    form_confirm_html = render_to_string('direct/_form_type.html', context)
    return JsonResponse({'form_upload_html': form_upload_html, 'form_confirm_html': form_confirm_html, 'file_type': file_type}, status=200)
