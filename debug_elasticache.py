#!/usr/bin/env python3
"""
Script debug ElastiCache Redis cho staging environment
"""
import os
import sys
import django
import redis
import json
import time
from datetime import datetime

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "voice.settings")
django.setup()

from django.conf import settings

def check_elasticache_connection():
    """Kiểm tra kết nối ElastiCache"""
    print("🔍 Checking ElastiCache connection...")
    print(f"   Host: {settings.REDIS_HOST}")
    print(f"   Port: {settings.REDIS_PORT}")
    
    try:
        r = redis.StrictRedis(
            host=settings.REDIS_HOST, 
            port=settings.REDIS_PORT, 
            db=0,
            socket_connect_timeout=5,
            socket_timeout=5
        )
        
        # Test ping
        ping_result = r.ping()
        print(f"   ✅ Ping: {ping_result}")
        
        # Test basic operations
        test_key = f"test_key_{int(time.time())}"
        r.set(test_key, "test_value", ex=60)
        value = r.get(test_key)
        r.delete(test_key)
        print(f"   ✅ Set/Get/Delete: OK")
        
        return r
    except redis.ConnectionError as e:
        print(f"   ❌ Connection Error: {e}")
        return None
    except redis.TimeoutError as e:
        print(f"   ❌ Timeout Error: {e}")
        return None
    except Exception as e:
        print(f"   ❌ Other Error: {e}")
        return None

def check_celery_broker_config():
    """Kiểm tra cấu hình Celery broker"""
    print("\n📋 Celery Broker Configuration:")
    print(f"   BROKER_URL: {getattr(settings, 'BROKER_URL', 'Not set')}")
    print(f"   CELERY_BROKER_URL: {getattr(settings, 'CELERY_BROKER_URL', 'Not set')}")
    print(f"   CELERY_RESULT_BACKEND: {getattr(settings, 'CELERY_RESULT_BACKEND', 'Not set')}")
    print(f"   BROKER_TRANSPORT_OPTIONS: {getattr(settings, 'BROKER_TRANSPORT_OPTIONS', 'Not set')}")

def check_elasticache_info(redis_client):
    """Lấy thông tin ElastiCache"""
    if not redis_client:
        return
        
    print("\n📊 ElastiCache Info:")
    try:
        info = redis_client.info()
        print(f"   Redis Version: {info.get('redis_version', 'Unknown')}")
        print(f"   Connected Clients: {info.get('connected_clients', 'Unknown')}")
        print(f"   Used Memory: {info.get('used_memory_human', 'Unknown')}")
        print(f"   Max Memory: {info.get('maxmemory_human', 'Unknown')}")
        print(f"   Keyspace Hits: {info.get('keyspace_hits', 'Unknown')}")
        print(f"   Keyspace Misses: {info.get('keyspace_misses', 'Unknown')}")
        
        # Kiểm tra database info
        for key, value in info.items():
            if key.startswith('db'):
                print(f"   {key}: {value}")
                
    except Exception as e:
        print(f"   ❌ Error getting info: {e}")

def check_celery_queues(redis_client):
    """Kiểm tra Celery queues trong ElastiCache"""
    if not redis_client:
        return
        
    print("\n📬 Celery Queues:")
    try:
        # Kiểm tra các queue phổ biến
        queues = ['celery', 'celery.pidbox', 'celeryev']
        
        for queue_name in queues:
            try:
                length = redis_client.llen(queue_name)
                print(f"   {queue_name}: {length} tasks")
                
                # Nếu có task, hiển thị một vài task đầu
                if length > 0:
                    for i in range(min(3, length)):
                        task_data = redis_client.lindex(queue_name, i)
                        if task_data:
                            try:
                                task_json = json.loads(task_data)
                                task_name = task_json.get('task', 'Unknown')
                                task_id = task_json.get('id', 'Unknown')[:8]
                                print(f"     - {task_name} (ID: {task_id}...)")
                            except:
                                print(f"     - Raw: {str(task_data)[:50]}...")
            except Exception as e:
                print(f"   {queue_name}: Error - {e}")
                
    except Exception as e:
        print(f"   ❌ Error checking queues: {e}")

def check_task_results(redis_client):
    """Kiểm tra task results"""
    if not redis_client:
        return
        
    print("\n📈 Task Results:")
    try:
        # Tìm các key result
        result_keys = redis_client.keys('celery-task-meta-*')
        print(f"   Total results: {len(result_keys)}")
        
        if result_keys:
            # Phân tích status
            status_count = {}
            recent_results = []
            
            for key in result_keys[-10:]:  # Lấy 10 kết quả gần nhất
                try:
                    result_data = redis_client.get(key)
                    if result_data:
                        result_json = json.loads(result_data)
                        status = result_json.get('status', 'Unknown')
                        status_count[status] = status_count.get(status, 0) + 1
                        
                        task_id = key.decode().replace('celery-task-meta-', '')[:8]
                        recent_results.append(f"{task_id}... -> {status}")
                except:
                    pass
            
            print("   Status summary:")
            for status, count in status_count.items():
                print(f"     {status}: {count}")
                
            print("   Recent results:")
            for result in recent_results[-5:]:
                print(f"     {result}")
                
    except Exception as e:
        print(f"   ❌ Error checking results: {e}")

def test_task_submission(redis_client):
    """Test gửi task vào ElastiCache"""
    if not redis_client:
        return
        
    print("\n🧪 Testing Task Submission:")
    try:
        from voice.celery import app
        
        # Tạo một task test đơn giản
        test_task_data = {
            "id": f"test-task-{int(time.time())}",
            "task": "app.tasks.send_mail_offer_creator",
            "args": ["test", "1", "1", "1", "https", "test.com", "/test"],
            "kwargs": {},
            "retries": 0,
            "eta": None,
            "expires": None,
            "utc": True
        }
        
        # Đẩy vào queue
        queue_before = redis_client.llen('celery')
        redis_client.lpush('celery', json.dumps(test_task_data))
        queue_after = redis_client.llen('celery')
        
        print(f"   Queue before: {queue_before}")
        print(f"   Queue after: {queue_after}")
        print(f"   ✅ Task submitted successfully")
        
        # Chờ và kiểm tra xem task có được xử lý không
        time.sleep(2)
        queue_final = redis_client.llen('celery')
        print(f"   Queue after 2s: {queue_final}")
        
        if queue_final < queue_after:
            print("   ✅ Task was processed by worker")
        else:
            print("   ❌ Task not processed - worker issue?")
            
    except Exception as e:
        print(f"   ❌ Error testing submission: {e}")

def check_worker_connectivity():
    """Kiểm tra worker có thể kết nối ElastiCache không"""
    print("\n👷 Worker Connectivity:")
    try:
        from voice.celery import app
        inspect = app.control.inspect(timeout=10)
        
        # Kiểm tra stats
        stats = inspect.stats()
        if stats:
            print(f"   ✅ Workers responding: {len(stats)}")
            for worker_name, worker_stats in stats.items():
                broker_info = worker_stats.get('broker', {})
                print(f"   Worker: {worker_name}")
                print(f"     Broker: {broker_info}")
        else:
            print("   ❌ No workers responding")
            
        # Kiểm tra ping workers
        ping_result = inspect.ping()
        if ping_result:
            print(f"   ✅ Ping successful: {list(ping_result.keys())}")
        else:
            print("   ❌ Ping failed")
            
    except Exception as e:
        print(f"   ❌ Error checking workers: {e}")

def main():
    print("🔍 ElastiCache Redis Debug Tool for Staging")
    print("=" * 60)
    
    # Kiểm tra cấu hình
    check_celery_broker_config()
    
    # Kiểm tra kết nối
    redis_client = check_elasticache_connection()
    
    if redis_client:
        # Kiểm tra thông tin ElastiCache
        check_elasticache_info(redis_client)
        
        # Kiểm tra queues
        check_celery_queues(redis_client)
        
        # Kiểm tra results
        check_task_results(redis_client)
        
        # Test gửi task
        test_task_submission(redis_client)
    
    # Kiểm tra worker
    check_worker_connectivity()
    
    print("\n" + "=" * 60)
    print("✅ Debug completed")

if __name__ == "__main__":
    main()
