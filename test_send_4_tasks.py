#!/usr/bin/env python3
"""
Script test để gửi 4 task và monitor xem c<PERSON> bao nhiêu task đ<PERSON><PERSON><PERSON> xử lý
"""
import os
import sys
import django
import time
import redis
import json
from datetime import datetime

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "voice.settings")
django.setup()

from django.conf import settings
from app.tasks import send_mail_offer_creator
from accounts.models import AuthUser
from app.models import OfferCreator

def get_test_data():
    """Lấy dữ liệu test"""
    try:
        # Lấy một offer creator để test
        offer = OfferCreator.objects.first()
        if not offer:
            print("❌ Không tìm thấy OfferCreator để test")
            return None
            
        # Lấy một vài user để test
        users = AuthUser.objects.filter(is_active=True)[:4]
        if len(users) < 4:
            print(f"❌ Chỉ tìm thấy {len(users)} users, cần ít nhất 4")
            return None
            
        return {
            'offer_id': offer.pk,
            'users': users,
            'sender_id': users[0].pk,
            'scheme': 'https',
            'host': 'https://soremo.jp',
            'path': '/test/path'
        }
    except Exception as e:
        print(f"❌ Lỗi khi lấy test data: {e}")
        return None

def monitor_redis_queue():
    """Monitor Redis queue"""
    try:
        r = redis.StrictRedis(host=settings.REDIS_HOST, port=settings.REDIS_PORT, db=0)
        queue_length = r.llen('celery')
        return queue_length
    except:
        return -1

def send_test_tasks():
    """Gửi 4 task test"""
    test_data = get_test_data()
    if not test_data:
        return False
        
    print("🚀 Bắt đầu gửi 4 task test...")
    print(f"📋 Offer ID: {test_data['offer_id']}")
    
    task_ids = []
    
    # Gửi 4 task
    for i, user in enumerate(test_data['users']):
        print(f"\n📤 Gửi task {i+1}/4 cho user: {user.email}")
        
        # Monitor queue trước khi gửi
        queue_before = monitor_redis_queue()
        print(f"   Queue length trước: {queue_before}")
        
        try:
            # Gửi task
            result = send_mail_offer_creator.delay(
                'new_message_now',  # type
                test_data['offer_id'],  # offer_id
                user.pk,  # recipient_id
                test_data['sender_id'],  # sender_id
                test_data['scheme'],  # scheme
                test_data['host'],  # host
                test_data['path']  # path
            )
            
            task_ids.append(result.id)
            print(f"   ✅ Task ID: {result.id}")
            
            # Monitor queue sau khi gửi
            time.sleep(0.1)  # Chờ một chút
            queue_after = monitor_redis_queue()
            print(f"   Queue length sau: {queue_after}")
            
        except Exception as e:
            print(f"   ❌ Lỗi gửi task: {e}")
            
        time.sleep(0.5)  # Chờ giữa các task
    
    return task_ids

def monitor_task_execution(task_ids, timeout=30):
    """Monitor việc thực thi task"""
    print(f"\n🔍 Monitor {len(task_ids)} tasks trong {timeout}s...")
    
    r = redis.StrictRedis(host=settings.REDIS_HOST, port=settings.REDIS_PORT, db=0)
    start_time = time.time()
    completed_tasks = set()
    
    while time.time() - start_time < timeout:
        # Kiểm tra từng task
        for task_id in task_ids:
            if task_id not in completed_tasks:
                result_key = f'celery-task-meta-{task_id}'
                result_data = r.get(result_key)
                
                if result_data:
                    try:
                        result_json = json.loads(result_data)
                        status = result_json.get('status', 'Unknown')
                        if status in ['SUCCESS', 'FAILURE']:
                            completed_tasks.add(task_id)
                            print(f"   ✅ Task {task_id[:8]}... completed with status: {status}")
                    except:
                        pass
        
        # Nếu tất cả task đã hoàn thành
        if len(completed_tasks) == len(task_ids):
            print(f"🎉 Tất cả {len(task_ids)} tasks đã hoàn thành!")
            break
            
        time.sleep(1)
    
    # Báo cáo cuối
    print(f"\n📊 Kết quả cuối:")
    print(f"   Tổng tasks gửi: {len(task_ids)}")
    print(f"   Tasks hoàn thành: {len(completed_tasks)}")
    print(f"   Tasks chưa hoàn thành: {len(task_ids) - len(completed_tasks)}")
    
    if len(completed_tasks) < len(task_ids):
        print(f"❌ Có {len(task_ids) - len(completed_tasks)} tasks không được xử lý!")
        
        # Kiểm tra queue còn lại
        queue_remaining = monitor_redis_queue()
        print(f"   Queue còn lại: {queue_remaining}")
    
    return len(completed_tasks)

def main():
    print("🧪 Test gửi 4 tasks và monitor")
    print("=" * 50)
    
    # Gửi tasks
    task_ids = send_test_tasks()
    if not task_ids:
        print("❌ Không thể gửi tasks")
        return
    
    # Monitor execution
    completed = monitor_task_execution(task_ids)
    
    print("\n" + "=" * 50)
    if completed == len(task_ids):
        print("✅ Test thành công - tất cả tasks được xử lý")
    else:
        print(f"❌ Test thất bại - chỉ {completed}/{len(task_ids)} tasks được xử lý")

if __name__ == "__main__":
    main()
