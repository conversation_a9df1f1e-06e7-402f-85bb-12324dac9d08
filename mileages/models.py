from decimal import Decimal, ROUND_HALF_UP

from django.db import models
import datetime
import io
import re
import uuid
import json
from base64 import urlsafe_b64encode
import requests

from PIL import Image
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.db import models, transaction
from django.db.models import Q, QuerySet, Prefetch, Avg, Max, Sum, Case, When
from model_utils import FieldTracker
from django.utils.translation import gettext as _
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth.models import User
from django.contrib.contenttypes.fields import GenericRelation, GenericForeignKey
from django.conf import settings

from common.base_models import BaseModelCropImg


class ModelBase(models.Model):
    modified = models.DateTimeField(auto_now=True, )
    created = models.DateTimeField(auto_now_add=True, )
    tracker = FieldTracker()

    class Meta:
        abstract = True


class MileageRank(ModelBase, BaseModelCropImg): # MileageProgram

    # id(pk)
    mileage_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # relations
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='mileage_ranks')

    # core fields
    rank_name_jp = models.CharField(max_length=256, blank=True, null=True, default='') # membership_stage_jp
    rank_name_en = models.CharField(max_length=256, blank=True, null=True, default='') # membership_stage_en
    point = models.FloatField(null=True, default=0) # qualifying_miles
    usage_fee = models.FloatField(default=17.5) # preferential_fee_rate

    # マイレージカードのサムネイル
    image = models.FileField(blank=True, upload_to='images') 
    x = models.FloatField(default=0)
    y = models.FloatField(default=0)
    width = models.FloatField(default=0)
    height = models.FloatField(default=0)
    is_default = models.BooleanField(default=False)

    class Meta:
        ordering = ['-point']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._original_image = self.image

    def save(self, *args, **kwargs):
        # if self.image and self.image != self._original_image:
        #     img, output = self.crop_img('image', self.x, self.y, self.width, self.height)
        if self.usage_fee > 100:
            self.usage_fee = 100
        elif self.usage_fee < 0:
            self.usage_fee = 0
        super(MileageRank, self).save(*args, **kwargs)

    def get_next_rank(self):
        return MileageRank.objects.filter(point__gt=self.point).order_by('point').first()

    def get_previous_rank(self):
        return MileageRank.objects.filter(point__lt=self.point).order_by('point').last()

    def get_usage_fee(self):
        return Decimal(self.usage_fee).quantize(Decimal('1.00'), ROUND_HALF_UP).normalize()
