from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from typing import Optional
from .models import MileageRank
from .serializers import MileageRankSerializer, MileageStatusSerializer

class MyMileageRankView(APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]

    def options(self, request, *args, **kwargs):
        """CORSプリフライトリクエストを許可"""
        response = Response()
        response['Access-Control-Allow-Origin'] = "http://localhost:3000"
        response['Access-Control-Allow-Methods'] = "GET, OPTIONS"
        response['Access-Control-Allow-Headers'] = "Authorization, Content-Type"
        return response

    def get(self, request):
        mileage_ranks = MileageRank.objects.filter(user=request.user)  # type: ignore
        serializer = MileageRankSerializer(mileage_ranks, many=True)
        return Response(serializer.data)


class MileageStatusApiView(APIView):
    authentication_classes = [TokenAuthentication, SessionAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        認証ユーザーのトータルマイルから、        
        現在のステージ、優遇手数料率、次のステージ、残りマイル数を返す        
        """
        user = request.user
        
        try:
            # ユーザーの総マイル数を取得
            total_miles = user.total_point
            
            # 現在のステージを取得（ユーザーのマイル数以下の最大ポイントのランク）
            current_stage = MileageRank.objects.filter(  # type: ignore
                point__lte=total_miles
            ).order_by('-point').first()
            
            if not current_stage:
                return Response({
                    'status': 'error',
                    'message': 'ステージが登録されていることを確認してください。',
                    'data': {
                        'current_stage': None,
                        'current_fee_rate': None,
                        'next_stage': None,
                        'miles_to_next_stage': None
                    }
                }, status=status.HTTP_404_NOT_FOUND)
            
            # 次のステージを取得（現在のランクより大きいポイントの最小ランク）
            next_stage = MileageRank.objects.filter(  # type: ignore
                point__gt=current_stage.point
            ).order_by('point').first()
            
            # 次のランクまでの残りマイルを計算
            miles_to_next_stage = None
            if next_stage:
                miles_to_next_stage = max(0, next_stage.point - total_miles)
            
            # シリアライザー用のデータを準備
            serializer_data = {
                'current_stage': current_stage.rank_name_jp,
                'current_stage_image': current_stage.image.url if current_stage.image else None,
                'current_fee_rate': current_stage.usage_fee,
                'next_stage': next_stage.rank_name_jp if next_stage else None,
                'miles_to_next_stage': miles_to_next_stage
            }
            
            # シリアライザーを使用してデータを整形
            serializer = MileageStatusSerializer(serializer_data)
            
            return Response({
                'status': 'success',
                'data': serializer.data,
                'message': 'マイレージステータスの取得に成功しました'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'マイレージステータスの取得中にエラーが発生しました: {str(e)}',
                'data': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
