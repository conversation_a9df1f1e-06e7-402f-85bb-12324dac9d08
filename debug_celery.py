#!/usr/bin/env python3
"""
Script để debug Celery queue và task status
"""
import os
import sys
import django
import redis
import json
from datetime import datetime

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "voice.settings")
django.setup()

from django.conf import settings
from celery import Celery

def check_redis_connection():
    """Kiểm tra kết nối Redis"""
    try:
        r = redis.StrictRedis(host=settings.REDIS_HOST, port=settings.REDIS_PORT, db=0)
        r.ping()
        print("✅ Redis connection: OK")
        return r
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return None

def check_celery_queue(redis_client):
    """Kiểm tra Celery queue"""
    try:
        # Kiểm tra queue length
        queue_length = redis_client.llen('celery')
        print(f"📊 Celery queue length: {queue_length}")
        
        # <PERSON><PERSON>y một vài task đầu tiên trong queue
        if queue_length > 0:
            print("\n📋 Tasks in queue:")
            for i in range(min(5, queue_length)):
                task_data = redis_client.lindex('celery', i)
                if task_data:
                    try:
                        task_json = json.loads(task_data)
                        task_name = task_json.get('task', 'Unknown')
                        task_id = task_json.get('id', 'Unknown')
                        print(f"  {i+1}. Task: {task_name}, ID: {task_id}")
                    except:
                        print(f"  {i+1}. Raw data: {task_data[:100]}...")
        
        return queue_length
    except Exception as e:
        print(f"❌ Error checking queue: {e}")
        return 0

def check_celery_results(redis_client):
    """Kiểm tra Celery results"""
    try:
        # Tìm các key result
        result_keys = redis_client.keys('celery-task-meta-*')
        print(f"\n📈 Total task results stored: {len(result_keys)}")
        
        if result_keys:
            print("\n🔍 Recent task results:")
            # Lấy 5 kết quả gần nhất
            for key in result_keys[-5:]:
                result_data = redis_client.get(key)
                if result_data:
                    try:
                        result_json = json.loads(result_data)
                        status = result_json.get('status', 'Unknown')
                        task_id = key.decode().replace('celery-task-meta-', '')
                        print(f"  Task ID: {task_id[:8]}..., Status: {status}")
                    except:
                        print(f"  Key: {key}, Raw: {result_data[:50]}...")
                        
    except Exception as e:
        print(f"❌ Error checking results: {e}")

def check_worker_status():
    """Kiểm tra worker status"""
    try:
        from voice.celery import app
        inspect = app.control.inspect()
        
        # Kiểm tra active workers
        stats = inspect.stats()
        if stats:
            print(f"\n👷 Active workers: {len(stats)}")
            for worker_name, worker_stats in stats.items():
                print(f"  Worker: {worker_name}")
                print(f"    Pool: {worker_stats.get('pool', {}).get('max-concurrency', 'Unknown')} processes")
        else:
            print("\n❌ No active workers found!")
            
        # Kiểm tra active tasks
        active = inspect.active()
        if active:
            total_active = sum(len(tasks) for tasks in active.values())
            print(f"🔄 Active tasks: {total_active}")
        else:
            print("🔄 No active tasks")
            
    except Exception as e:
        print(f"❌ Error checking workers: {e}")

def main():
    print("🔍 Celery Debug Tool")
    print("=" * 50)
    
    # Kiểm tra Redis
    redis_client = check_redis_connection()
    if not redis_client:
        return
    
    # Kiểm tra queue
    check_celery_queue(redis_client)
    
    # Kiểm tra results
    check_celery_results(redis_client)
    
    # Kiểm tra workers
    check_worker_status()
    
    print("\n" + "=" * 50)
    print("✅ Debug completed")

if __name__ == "__main__":
    main()
