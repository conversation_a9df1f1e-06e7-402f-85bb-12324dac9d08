# Generated by Django 5.2.4 on 2025-08-13 00:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0103_alter_authuser_options_alter_authuser_company_url_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='apikey',
            name='memo',
            field=models.CharField(blank=True, default='', max_length=255, verbose_name='用途'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='account_name',
            field=models.CharField(blank=True, default='', max_length=255, null=True, verbose_name='account_holder'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='account_type',
            field=models.CharField(choices=[('normal', '普通'), ('temp', '当座'), ('reverse_tax', '納税準備預金'), ('saving', '貯蓄'), ('other', 'その他')], default='normal', max_length=50),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='bank',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='bank_branch',
            field=models.CharField(blank=True, default='', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='bank_branch_number',
            field=models.CharField(blank=True, default='', max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='city',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='市区町村・番地'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='city2',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='市区町村・番地'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='email',
            field=models.EmailField(default=None, max_length=70, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='first_name',
            field=models.CharField(default='guest', max_length=30),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='is_check_ip',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='is_staff',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='last_name',
            field=models.CharField(default='user', max_length=30),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='mansion',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='建物名・部屋番号'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='mansion2',
            field=models.CharField(blank=True, default='', max_length=200, null=True, verbose_name='建物名・部屋番号'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='post_number',
            field=models.CharField(blank=True, default='', max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='post_number2',
            field=models.CharField(blank=True, default='', max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='province',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='都道府県'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='province2',
            field=models.CharField(blank=True, default='', max_length=100, null=True, verbose_name='都道府県'),
        ),
        migrations.AlterField(
            model_name='authuser',
            name='username',
            field=models.CharField(max_length=70, unique=True),
        ),
    ]
